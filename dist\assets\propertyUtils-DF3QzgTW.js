import{o as P,j as s,L as C,p as y}from"./index-Dklazue-.js";import{a as $}from"./useMutation-BrUrPIzr.js";import{R as D}from"./EditOutlined-DxjsqgjX.js";import{R}from"./DeleteOutlined-BjE_e6LE.js";const E=({title:e,location:i,price:n,detail:l,bath_tub:r,bed_room:c,square_feet:x,src:d,is_favorite:f=!1,showActions:v=!1,onEdit:m=null,onDelete:u=null,id:o,slug:h,image:j})=>{const g=P(),b=$({mutationFn:async a=>await y.request("addProperty",{slug:`${o}/favorite`,data:{value:a}}),onSuccess:()=>{g.invalidateQueries({queryKey:["properties"],exact:!1}),g.invalidateQueries({queryKey:["getProperty"],exact:!1})}}),p=a=>{if(a.preventDefault(),a.stopPropagation(),b.isPending)return;const _=!f;b.mutate(_)},N=a=>{a.preventDefault(),a.stopPropagation(),m&&m({id:o,slug:h,title:e})},w=a=>{a.preventDefault(),a.stopPropagation(),u&&u({id:o,slug:h,title:e})};return s.jsx(C,{to:d,children:s.jsxs("div",{className:"property-card border rounded overflow-hidden bg-white",children:[s.jsxs("div",{className:"p-card-header position-relative",children:[s.jsx("img",{src:j||"/assets/img/card-img.png",alt:e,className:"w-100",style:{height:"200px",objectFit:"cover"}}),s.jsx("div",{className:"favorite-icon position-absolute",onClick:p,style:{top:"10px",right:"10px",borderRadius:"50%",cursor:b.isPending?"not-allowed":"pointer",opacity:b.isPending?.7:1,transition:"opacity 0.2s ease"},children:s.jsx("img",{src:f?"/assets/img/heart-active.png":"/assets/img/heart-icon.png",alt:"favorite"})}),v&&s.jsxs("div",{className:"action-buttons position-absolute",style:{top:"10px",left:"10px",display:"flex",gap:"8px"},children:[s.jsx("div",{onClick:N,style:{backgroundColor:"rgba(0, 0, 0, 0.9)",borderRadius:"50%",width:"32px",height:"32px",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer",color:"white",border:"1px solid rgba(255, 255, 255, 0.3)"},title:"Edit Property",children:s.jsx(D,{style:{fontSize:"14px"}})}),s.jsx("div",{onClick:w,style:{backgroundColor:"rgba(220, 53, 69, 0.95)",borderRadius:"50%",width:"32px",height:"32px",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer",color:"white",border:"1px solid rgba(255, 255, 255, 0.3)"},title:"Delete Property",children:s.jsx(R,{style:{fontSize:"14px"}})})]})]}),s.jsxs("div",{className:"",children:[s.jsxs("div",{className:"p-card-body",children:[s.jsx("h6",{className:"mt-3",children:e}),s.jsxs("p",{className:"mt-3 mb-3",children:[" ",n]}),s.jsxs("div",{className:"d-flex align-items-center",children:[s.jsx("div",{children:s.jsx("img",{src:"/assets/img/card-location.png",alt:""})}),s.jsx("div",{className:"ms-2",children:s.jsx("p",{children:i})})]}),s.jsx("p",{className:"detail-para mt-2",style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:l})]}),s.jsxs("div",{className:"p-card-footer mt-3 mb-3",children:[s.jsxs("div",{className:"d-flex align-items-center",children:[s.jsx("div",{children:s.jsx("img",{src:"/assets/img/bathtub-icon.png",alt:""})}),s.jsx("div",{className:"ms-2",children:s.jsx("p",{children:r})})]}),s.jsxs("div",{className:"d-flex align-items-center ms-3",children:[s.jsx("div",{children:s.jsx("img",{src:"/assets/img/bed-icon.png",alt:""})}),s.jsx("div",{className:"ms-2",children:s.jsx("p",{children:c})})]}),s.jsxs("div",{className:"d-flex align-items-center ms-3",children:[s.jsx("div",{children:s.jsx("img",{src:"/assets/img/squarefoot-icon.png",alt:""})}),s.jsx("div",{className:"ms-2",children:s.jsx("p",{children:x})})]})]})]})]})})},k=(e,i=2,n=8)=>{if(!e)return"No description available";const l=e.split(" "),r=i*n;return l.length<=r?e:l.slice(0,r).join(" ")+"..."},t=e=>{if(!e)return e;const i=parseFloat(e);return isNaN(i)?e:Math.floor(i)},q=(e,i={})=>{var m,u,o,h,j,g;if(!e)return null;const{showActions:n=!1,onEdit:l=null,onDelete:r=null}=i,c=window.user||{},x=e.user_id===c.id||((m=e.user)==null?void 0:m.id)===c.id||e.created_by===c.id,d=n&&x,f=e.images&&e.images.length>0?e.images[0].url:"/assets/img/property-placeholder.png",v=k(e.description,2,8);return{id:e.id,slug:e.slug,title:e.name||`${t(e.bed)} Bed ${t(e.bath)} Bath ${((u=e.type)==null?void 0:u.name)||"Property"}`,location:`${e.city}, ${e.state}`,price:`${t(e.price).toLocaleString()}`,detail:v,bath_tub:t(e.bath),bed_room:t(e.bed),square_feet:`${t(e.size)} ${e.size_type||"sqft"}`,src:`/listing/detail/${e.id}`,image:f,garage:t(e.garage),year_built:t(e.year_built),basement:e.basement,hoa:e.hoa?e.hoa_price?t(e.hoa_price):!0:!1,mls_id:e.mls_id,property_type:(o=e.type)==null?void 0:o.name,home_style:(h=e.home_style)==null?void 0:h.name,agent:(j=e.user)==null?void 0:j.name,agent_phone:e.mobile_no,is_favorite:e.is_favorite,showActions:d,onEdit:d?l:null,onDelete:d?r:null,isOwner:x,currentUserId:c.id,propertyUserId:e.user_id||((g=e.user)==null?void 0:g.id)||e.created_by}},F=(e,i={})=>Array.isArray(e)?e.map(n=>q(n,i)).filter(Boolean):[];export{E as P,F as t};
