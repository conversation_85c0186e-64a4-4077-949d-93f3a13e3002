import{r as u,j as t,R as S}from"./index-Dklazue-.js";import{B as n}from"./index-BUt89ETK.js";import{u as E}from"./useLocationData-CQHRFCOb.js";import{u as I}from"./useStartupData-QD8kuEYy.js";import{F as C}from"./react-stripe.esm-ypQSOYN5.js";import{B as g}from"./button-DNhBCuue.js";import{R as D}from"./searchbar-BCS1MYCc.js";import{M as T}from"./index-CatU6E6a.js";import{S as z}from"./index-vmMMvWhJ.js";const w=u.createContext(),ee=({children:h})=>{const[d,m]=u.useState({}),[x,j]=u.useState(!1),p=F=>{m(F)},a=()=>{m({})};return t.jsx(w.Provider,{value:{filters:d,updateFilters:p,clearFilters:a,isFilterModalOpen:x,setIsFilterModalOpen:j},children:h})},G=()=>u.useContext(w),te=({fields:h=[]})=>{const{filters:d,updateFilters:m,clearFilters:x,isFilterModalOpen:j,setIsFilterModalOpen:p}=G(),[a]=C.useForm(),[F,y]=u.useState(!1),{data:_}=I(),c=_==null?void 0:_.data,{selectedState:k,stateOptions:M,cityOptions:O,statesLoading:P,citiesLoading:v,handleStateChange:R,updateSelectedState:f}=E(),B=S.useMemo(()=>c!=null&&c.postQuestions?c.postQuestions.map(s=>({value:s.id,label:s.name||s.title||s.question})):[],[c==null?void 0:c.postQuestions]);S.useEffect(()=>{const s={...d};a.setFieldsValue(s),d.state?f(d.state):f(""),y(!!d.most_asked)},[d,a,f]);const L=s=>{y(s),s||a.setFieldValue("question_id",void 0)},Q=()=>{const s=a.getFieldsValue(),e=Object.entries(s).reduce((o,[l,r])=>(r!=null&&r!==""&&(l==="professional_type"?o.user_professional_type=r:l==="state"?o.user_state=r:l==="city"?o.user_city=r:l==="last_10_days"&&r===!0?o.last_days=10:l==="least_commented"&&r===!0?o.sort_by="comments_count":l==="question_id"&&r?o.question_id=r:o[l]=r),o),{});e.page=1,e.limit=12,m(e),p(!1)},N=()=>{a.resetFields(),x(),f(""),y(!1),p(!1)},V=s=>{R(s,a)},q=s=>{const{name:e,label:o,type:l,placeholder:r,options:b,...i}=s;if(e==="state")return t.jsx(n,{name:e,label:o,type:"select",placeholder:r,options:M,loading:P,handlechange:V,showSearch:!0,...i},e);if(e==="city")return t.jsx(n,{name:e,label:o,type:"select",placeholder:r,options:O,disabled:!k,loading:v,showSearch:!0,...i},e);if(e==="professional_type")return t.jsx(n,{name:e,label:o,type:"radio",options:[{value:"broker",label:"Real Estate Broker"},{value:"lender",label:"Lender/ mtg Broker"},{value:"commercial",label:"Commercial Agent"}],...i},e);if(e==="last_10_days")return t.jsx(n,{name:e,type:"checkbox",placeholder:"Last 10 days posts only",...i},e);if(e==="least_commented")return t.jsx(n,{name:e,type:"checkbox",placeholder:"Least Commented On",...i},e);if(e==="most_asked")return t.jsxs("div",{children:[t.jsx(n,{name:e,type:"checkbox",placeholder:"Most asked questions",handlecheckbox:A=>L(A.target.checked),...i}),F&&t.jsx("div",{className:"mt-2",children:t.jsx(n,{name:"question_id",type:"select",placeholder:"Select Question",options:B,loading:!c,showSearch:!0})})]},e);switch(l){case"select":return t.jsx(n,{name:e,label:o,type:"select",placeholder:r,options:b,loading:i.loading,...i},e);case"radio":return t.jsx(n,{name:e,label:o,type:"radio",options:b,...i},e);case"checkbox":return t.jsx(n,{name:e,label:o,type:"checkbox",options:b,...i},e);case"input":default:return t.jsx(n,{name:e,label:o,placeholder:r,type:l==="input"?"number":void 0,...i},e)}};return t.jsxs(t.Fragment,{children:[t.jsx(g,{icon:t.jsx(D,{}),onClick:()=>p(!0),type:"default",className:"d-none",children:"Filter"}),t.jsx(T,{title:"Filter Posts",open:j,onCancel:()=>p(!1),footer:null,width:800,children:t.jsxs(C,{form:a,layout:"vertical",onFinish:Q,preserve:!1,children:[t.jsx("div",{className:"row",children:h.map(s=>t.jsx("div",{className:"col-12 col-md-6 mb-3",children:q(s)},s.name))}),t.jsx("div",{className:"d-flex justify-content-end gap-2 mt-4",children:t.jsxs(z,{children:[t.jsx(g,{onClick:N,children:"Reset"}),t.jsx(g,{type:"primary",htmlType:"submit",children:"Apply Filters"})]})})]})})]})};export{ee as P,te as a,G as u};
