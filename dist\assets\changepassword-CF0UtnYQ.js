import{u,j as s}from"./index-Dklazue-.js";import{I as w}from"./index-vmMMvWhJ.js";import{B as a,c as l,v as r}from"./index-BUt89ETK.js";import{F as f}from"./flatbutton-B_tUS4QM.js";import{u as h}from"./useMutation-BrUrPIzr.js";import{F as d}from"./react-stripe.esm-ypQSOYN5.js";import"./button-DNhBCuue.js";import"./index-Cj6uPc4c.js";import"./index-CjGjc6T5.js";import"./index-CHbHgJvR.js";import"./useLocale-BNhrTARD.js";const I=()=>{const m=u(),[t]=d.useForm(),c=e=>{p(e)},{mutate:p,isPending:o}=h("changePassword",{useFormData:!1,onSuccess:async e=>{e&&(m("/setting"),t.resetFields())}});return s.jsx(w,{showSidebar:!1,pageType:"signup",children:s.jsx(d,{name:"login",layout:"vertical",onFinish:c,form:t,initialValues:{remember:!0},autoComplete:"off",children:s.jsxs("div",{className:"container-fluid",children:[s.jsxs("div",{className:"row mt-5 justify-content-center",children:[s.jsx("div",{className:"col-12 col-md-12 col-lg-7",children:s.jsx("h2",{className:"mb-3",children:"Chanage Password"})}),s.jsx("div",{className:"col-12 col-md-8 col-lg-7",children:s.jsx(a,{name:"current_password",placeholder:"***",label:"Current Password",type:"password",rules:l("current-password",r.required,r.password)})}),s.jsx("div",{className:"col-12 col-md-8 col-lg-7",children:s.jsx(a,{name:"password",placeholder:"***",label:"New Password",type:"password",rules:l("new-password",r.required,r.password)})}),s.jsx("div",{className:"col-12 col-md-8 col-lg-7",children:s.jsx(a,{name:"confirm_password",placeholder:"***",label:"Confirm Password",type:"password",rules:[r.required("confirm-password"),({getFieldValue:e})=>({validator:(x,i)=>{const n=e("password");return!i||!n?Promise.resolve():i!==n?Promise.reject(new Error("Confirm Passwords does not match")):Promise.resolve()}})],dependencies:["password"]})})]}),s.jsx("div",{className:"text-center",children:s.jsx(f,{title:o?"Updating...":"Update",className:"mx-auto mt-4 signin-btn signup-btn mt-5",htmlType:"submit",loading:o,disabled:o})})]})})})};export{I as default};
