import{j as s}from"./index-Dklazue-.js";import{I as o}from"./index-vmMMvWhJ.js";import"./useMutation-BrUrPIzr.js";import"./button-DNhBCuue.js";import"./index-Cj6uPc4c.js";const a=({date:r,duration:i,price:c})=>s.jsxs("div",{className:"suscription-box",children:[s.jsxs("div",{children:[s.jsx("p",{children:"Date"}),s.jsx("p",{className:"color-light",children:r})]}),s.jsxs("div",{children:[s.jsx("p",{children:"1 month"}),s.jsx("p",{className:"color-light",children:i})]}),s.jsx("div",{children:s.jsx("p",{className:"color-blue font-16 font-600",children:"$"+c})})]}),e=()=>{const r=[{date:"25 Jan 2024",duration:"$5.99 for a month",price:"5.99"},{date:"25 Feb 2024",duration:"$5.99 for a month",price:"5.99"},{date:"25 Mar 2024",duration:"$5.99 for a month",price:"5.99"}];return s.jsx("div",{className:"container-fluid",children:s.jsx("div",{className:"row",children:s.jsx("div",{className:"col-12",children:s.jsxs("div",{className:"subscription-history",children:[s.jsx("h2",{className:"mt-3 mb-4",children:"Subscription History"}),r.map((i,c)=>s.jsx(a,{date:i.date,duration:i.duration,price:i.price},c))]})})})})},m=()=>s.jsx(o,{children:s.jsx("div",{className:"conatiner-fluid",children:s.jsx("div",{className:"row",children:s.jsx("div",{className:"col-12",children:s.jsx(e,{})})})})});export{m as default};
