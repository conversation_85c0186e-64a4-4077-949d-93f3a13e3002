import{j as e,f as k,m as A,r as j,a7 as u,a8 as W,u as $,p as D}from"./index-Dklazue-.js";import{u as J,I as O}from"./index-vmMMvWhJ.js";import{F as w}from"./flatbutton-B_tUS4QM.js";import{u as R}from"./useQuery-C3n1GVcJ.js";import{u as Y}from"./useMutation-BrUrPIzr.js";import{E as z,u as X,a as K,F as T,P as H}from"./react-stripe.esm-ypQSOYN5.js";import{S as _}from"./Skeleton--lTrbnrp.js";import{M as Q}from"./index-CatU6E6a.js";import"./button-DNhBCuue.js";import"./index-Cj6uPc4c.js";import"./useLocale-BNhrTARD.js";import"./index-ChnPz6Q1.js";import"./fade-B36sOBLs.js";var U="basil",Z=function(t){return t===3?"v3":t},q="https://js.stripe.com",ee="".concat(q,"/").concat(U,"/stripe.js"),te=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,re=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/;var ne=function(t){return te.test(t)||re.test(t)},se=function(){for(var t=document.querySelectorAll('script[src^="'.concat(q,'"]')),r=0;r<t.length;r++){var s=t[r];if(ne(s.src))return s}return null},I=function(t){var r="",s=document.createElement("script");s.src="".concat(ee).concat(r);var a=document.head||document.body;if(!a)throw new Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return a.appendChild(s),s},ae=function(t,r){!t||!t._registerWrapper||t._registerWrapper({name:"stripe-js",version:"7.4.0",startTime:r})},b=null,E=null,C=null,ie=function(t){return function(r){t(new Error("Failed to load Stripe.js",{cause:r}))}},oe=function(t,r){return function(){window.Stripe?t(window.Stripe):r(new Error("Stripe.js not available"))}},ce=function(t){return b!==null?b:(b=new Promise(function(r,s){if(typeof window>"u"||typeof document>"u"){r(null);return}if(window.Stripe){r(window.Stripe);return}try{var a=se();if(!(a&&t)){if(!a)a=I(t);else if(a&&C!==null&&E!==null){var i;a.removeEventListener("load",C),a.removeEventListener("error",E),(i=a.parentNode)===null||i===void 0||i.removeChild(a),a=I(t)}}C=oe(r,s),E=ie(s),a.addEventListener("load",C),a.addEventListener("error",E)}catch(o){s(o);return}}),b.catch(function(r){return b=null,Promise.reject(r)}))},le=function(t,r,s){if(t===null)return null;var a=r[0],i=a.match(/^pk_test/),o=Z(t.version),m=U;i&&o!==m&&console.warn("Stripe.js@".concat(o," was loaded on the page, but @stripe/stripe-js@").concat("7.4.0"," expected Stripe.js@").concat(m,". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning"));var l=t.apply(void 0,r);return ae(l,s),l},P,M=!1,V=function(){return P||(P=ce(null).catch(function(t){return P=null,Promise.reject(t)}),P)};Promise.resolve().then(function(){return V()}).catch(function(n){M||console.warn(n)});var de=function(){for(var t=arguments.length,r=new Array(t),s=0;s<t;s++)r[s]=arguments[s];M=!0;var a=Date.now();return V().then(function(i){return le(i,r,a)})};const ue=de(W.stripe_publish_key),me=({onSuccess:n,onCancel:t,token:r,clientSecret:s})=>{const{token:a}=k(),i=A(),o=X(),m=K(),[l,p]=j.useState(!1);j.useEffect(()=>{o&&(async()=>{const y=new URLSearchParams(i.search).get("payment_intent_client_secret");if(y&&o)try{const{paymentIntent:g}=await o.retrievePaymentIntent(y);switch(g.status){case"succeeded":u.success({message:"Payment Successful",description:"Success! Payment received.",duration:5}),n&&n({paymentIntent:g});break;case"processing":u.info({message:"Payment Processing",description:"Payment processing. We'll update you when payment is received.",duration:5});break;case"requires_payment_method":u.error({message:"Payment Failed",description:"Payment failed. Please try another payment method.",duration:7});break;default:u.error({message:"Payment Error",description:"Something went wrong.",duration:7});break}}catch(g){console.error("Error retrieving payment intent:",g),u.error({message:"Error",description:"Failed to retrieve payment status.",duration:7})}})()},[o,i.search,n]);const[x]=T.useForm(),S=async d=>{if(!o||!m||!s){u.error({message:"Payment System Error",description:"Stripe has not loaded yet. Please refresh the page and try again.",duration:7});return}p(!0);try{const{error:v,paymentIntent:y}=await o.confirmPayment({elements:m,redirect:"if_required"});v?(u.error({message:"Payment Error",description:v.message,duration:7}),p(!1)):y&&(c(y),p(!1))}catch(v){p(!1),u.error({message:"Unexpected Error",description:"An unexpected error occurred during payment processing. Please try again.",duration:7}),console.error("Payment error:",v)}},c=d=>{switch(d.status){case"succeeded":u.success({message:"Payment Successful",description:"Success! Payment received.",duration:5}),n&&n({paymentIntent:d});break;case"processing":u.info({message:"Payment Processing",description:"Payment processing. We'll update you when payment is received.",duration:5});break;case"requires_payment_method":u.error({message:"Payment Failed",description:"Payment failed. Please try another payment method.",duration:7});break;case"requires_action":u.info({message:"Additional Authentication Required",description:"Please complete the additional authentication step.",duration:5});break;default:u.error({message:"Payment Error",description:"Something went wrong.",duration:7});break}};return e.jsx("div",{className:"payment-form-container",children:e.jsxs(T,{form:x,onFinish:S,className:"payment-form",children:[e.jsx("div",{className:"payment-element-container",children:e.jsx(H,{options:{layout:"tabs"}})}),e.jsx("div",{className:"form-actions",children:e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx(w,{title:"Cancel",className:"cancel-btn",onClick:t,type:"button"}),e.jsx(w,{title:l?"Processing...":"Subscribe",className:"payment-btn ms-3",htmlType:"submit",loading:l,disabled:!o||l})]})})]})})},pe=({selectedPlan:n,onSuccess:t,onCancel:r,token:s,clientSecret:a,isCreatingSubscription:i})=>{if(i)return e.jsx("div",{className:"payment-form-container",children:e.jsx(_,{active:!0,paragraph:{rows:4}})});if(!a)return e.jsx("div",{className:"payment-form-container",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{children:"Failed to initialize payment. Please try again."}),e.jsx(w,{title:"Cancel",className:"cancel-btn",onClick:r})]})});const o={clientSecret:a,appearance:{theme:"stripe"}};return e.jsx(z,{stripe:ue,options:o,children:e.jsx(me,{onSuccess:t,onCancel:r,token:s,clientSecret:a})})},F=()=>{A(),$();const[n,t]=j.useState(!1),[r,s]=j.useState(null),[a,i]=j.useState(null),[o,m]=j.useState(!1),{token:l}=k(),{showAlert:p}=J(),{data:x,isLoading:S,refetch:c}=R("getProfile",{token:l,staleTime:5*60*1e3,gcTime:10*60*1e3}),{mutate:d,isPending:v}=Y("cancelSubscription",{...l&&{token:l},onSuccess:async h=>{var N;const f=await c();(N=f==null?void 0:f.data)!=null&&N.data&&(window.user=f.data.data)}}),y=async h=>{s(h),t(!0),m(!0),i(null);try{const f=h.priceId||h.price_id||h.stripe_price_id||h.id;if(!f)throw new Error("Price ID not found in selected plan");const N=await D.request("createSubscription",{data:{price_id:f},token:l,useFormData:!1}),{clientSecret:G}=N.data;i(G)}catch{}finally{m(!1)}},g=async h=>{t(!1),s(null),i(null),setTimeout(async()=>{window.location.reload()},3e3)},L=()=>{t(!1),s(null),i(null)},B=async()=>{(await p({title:"Cancel Subscription",text:"Are you sure you want to cancel your subscription? This action cannot be undone.",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, Cancel",cancelButtonText:"No, Keep"})).isConfirmed&&d()};return e.jsxs("div",{className:"subscription-container",children:[e.jsx(he,{}),e.jsx(fe,{onSubscribeClick:y,token:l,userData:x,userLoading:S,onCancelSubscription:B,isCancelling:v}),e.jsx(ye,{}),e.jsx(Q,{title:"Complete Your Subscription",open:n,onCancel:L,footer:null,centered:!0,children:e.jsx(pe,{selectedPlan:r,onSuccess:g,onCancel:L,token:l,clientSecret:a,isCreatingSubscription:o})})]})},he=()=>e.jsxs("div",{className:"header",children:[e.jsx("div",{className:"logo mt-3 mb-3",children:e.jsx("img",{src:"/assets/img/suscribtion-img.png",alt:""})}),e.jsx("h1",{children:"Get Sphere Premium Free for 2 Months."})]}),fe=({onSubscribeClick:n,token:t,userData:r,userLoading:s,onCancelSubscription:a,isCancelling:i})=>{var x,S;const{data:o,isLoading:m,isError:l}=R("getSubscriptionProduct",{showSuccessNotification:!1,token:t}),p=(x=r==null?void 0:r.data)==null?void 0:x.isSubscribed;if(m||s)return e.jsx("div",{className:"plan-options",children:e.jsx("div",{className:"plans-container",children:[...Array(1)].map((c,d)=>e.jsxs("div",{className:"plan-card",children:[e.jsx(_,{active:!0,paragraph:{rows:2}}),e.jsx(_.Button,{active:!0,size:"large",className:"mt-4",block:!0})]},d))})});if(l)return e.jsx("div",{className:"text-center text-danger",children:"Failed to load plans."});if(p){const c=r.data.subscriptions,d=c.cancelAtPeriodEnd===!0;return e.jsx("div",{className:"plan-options",children:e.jsx("div",{className:"plans-container",children:e.jsxs("div",{className:"plan-card active-subscription",children:[e.jsx("div",{className:"subscription-status",children:e.jsx("span",{className:`status-badge ${d?"cancelling":"active"}`,children:d?"Ending Soon":"Active"})}),e.jsx("h3",{children:e.jsx("strong",{children:c.productName})}),e.jsx("p",{children:c.description||"You are currently subscribed to our premium plan."}),e.jsxs("p",{className:"price",children:["$",c.amount||c.price||"5.99"," ",(c.currency||"USD").toUpperCase()]}),d&&c.endAt?e.jsx("div",{className:"cancellation-notice mt-3",children:e.jsx("p",{className:"cancellation-message",children:e.jsxs("strong",{children:["Your subscription will end on"," ",new Date(c.endAt).toLocaleDateString(),"."]})})}):c.current_period_end?e.jsxs("p",{className:"renewal-date",children:["Next billing:"," ",new Date(c.current_period_end).toLocaleDateString()]}):null,!d&&e.jsx(w,{title:i?"Cancelling...":"Cancel Subscription",className:"cancel-btn mt-4",onClick:a,loading:i,disabled:i})]})})})}return e.jsx("div",{className:"plan-options",children:e.jsx("div",{className:"plans-container",children:(S=o==null?void 0:o.data)==null?void 0:S.map(c=>e.jsx(ve,{plan:c,onSubscribeClick:n},c.id))})})},ve=({plan:n,onSubscribeClick:t})=>e.jsxs("div",{className:"plan-card",children:[e.jsx("h3",{children:e.jsx("strong",{children:n.name})}),e.jsx("p",{children:n.description||"No description available."}),e.jsxs("p",{className:"price",children:["$",n.price," ",n.currency.toUpperCase()]}),e.jsx(w,{title:"Subscribe",className:"signin-btn mt-4",onClick:()=>t(n)})]}),ye=()=>e.jsx("div",{className:"subscription-offer",children:e.jsx("p",{children:e.jsx("strong",{children:"2 months for free, then $4.99 a month"})})}),Te=()=>{const{token:n}=k();return n?e.jsx("div",{className:"container-fluid",children:e.jsx("div",{className:"row gx-0",children:e.jsx("div",{className:"col-12",children:e.jsx(F,{})})})}):e.jsx(O,{children:e.jsx("div",{className:"container-fluid",children:e.jsx("div",{className:"row gx-0",children:e.jsx("div",{className:"col-12",children:e.jsx(F,{})})})})})};export{Te as default};
