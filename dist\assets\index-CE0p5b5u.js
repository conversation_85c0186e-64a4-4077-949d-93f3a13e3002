import{r as a,W as j,v as U,j as e,h as v}from"./index-Dklazue-.js";import{F as A}from"./react-stripe.esm-ypQSOYN5.js";import{U as C}from"./index-Vj938NLd.js";import{A as B}from"./index-Cj6uPc4c.js";import{R as E}from"./InboxOutlined-Cxk4AEr6.js";var N={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"},P=function(r,t){return a.createElement(j,U({},r,{ref:t,icon:N}))},L=a.forwardRef(P),V={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"}}]},name:"user",theme:"outlined"},W=function(r,t){return a.createElement(j,U({},r,{ref:t,icon:V}))},D=a.forwardRef(W);const{Dragger:H}=C,k=(i,r)=>{const t=new FileReader;t.readAsDataURL(i),t.onloadend=function(){r(t.result)}},G=({fileList:i,listType:r="picture-circle",callback:t,static_img:u="/images/upload-profile.png",fileType:n="image",multiple:p=!1,maxSize:c=2,showErrors:d=!0,useFormItem:b=!1,formItemProps:w={},uploadAction:R="",...S})=>{const[J,y]=a.useState(!1),[f,M]=a.useState(),[m,O]=a.useState([]),q=()=>{switch(n){case"image":return"image/*";case"document":return".pdf,.doc,.docx,.txt";case"any":default:return"*"}},z=s=>{switch(n){case"image":return s.type.startsWith("image/");case"document":return["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","text/plain"].includes(s.type);case"any":default:return!0}},I=s=>{const l=[];if(!z(s)){const o=`Invalid file type. Only ${n} files are allowed.`;return l.push(o),d&&v.error(o),!1}if(!(s.size/1024/1024<c)){const o=`File must be smaller than ${c}MB!`;return l.push(o),d&&v.error(o),!1}O(l);const x={...s,fileObj:s};return n==="image"?k(s,o=>{M(o),t&&t(x)}):t&&t(x),!1},$=s=>{y(!1)},h={name:"file",multiple:p,action:R,accept:q(),beforeUpload:I,onChange:$,fileList:i,...S},F=()=>r==="picture-circle"&&n==="image"?e.jsx(C,{...h,listType:r,className:"avatar-uploader",showUploadList:!1,children:e.jsxs("div",{style:{position:"relative",display:"inline-block"},children:[e.jsx(B,{src:f||u,size:100,icon:!f&&!u?e.jsx(D,{}):null,style:{objectFit:"cover",borderRadius:"50%"}}),e.jsx("div",{style:{position:"absolute",bottom:0,right:0,backgroundColor:"#1890ff",borderRadius:"50%",width:24,height:24,display:"flex",alignItems:"center",justifyContent:"center",color:"#fff",border:"2px solid #fff",cursor:"pointer"},children:e.jsx(L,{style:{fontSize:12}})})]})}):e.jsxs(H,{...h,children:[e.jsx("p",{className:"ant-upload-drag-icon",children:e.jsx(E,{})}),e.jsxs("p",{className:"ant-upload-text",children:["Click or drag ",n," files to this area to upload"]}),e.jsxs("p",{className:"ant-upload-hint",children:[p?"Support for multiple files.":"Support for single file.",` Max size: ${c}MB`]})]}),g=e.jsxs("div",{children:[F(),d&&m.length>0&&e.jsx("div",{style:{color:"#ff4d4f",marginTop:8},children:m.map((s,l)=>e.jsx("div",{children:s},l))})]});return b?e.jsx(A.Item,{...w,children:g}):g},T=a.memo(G);export{T as C};
