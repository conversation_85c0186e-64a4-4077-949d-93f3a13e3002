import React from "react";
import InnerLayout from "@/components/shared/layout/innerlayout";
import NotificationItem from "@/components/partial/notificationitems/notificationitem";
import ReusablePagination from "@/components/shared/ReusablePagination";
import { usePaginatedQuery } from "@/hooks/reactQuery";
import { Skeleton } from "antd";
import dayjs from "dayjs";
import EmptyState from "@/components/shared/EmptyState";
const groupNotificationsByDate = (notifications) => {
  const today = dayjs();
  const yesterday = today.subtract(1, "day");
  const groups = {};

  notifications.forEach((notif) => {
    const notifDate = dayjs(notif.createdAt);
    let label = notifDate.isSame(today, "day")
      ? "Today"
      : notifDate.isSame(yesterday, "day")
      ? "Yesterday"
      : notifDate.format("MMM D, YYYY");
    if (!groups[label]) groups[label] = [];
    groups[label].push(notif);
  });
  return groups;
};

const formatTimeAgo = (date) => {
  const now = dayjs();
  const d = dayjs(date);
  const diffMins = now.diff(d, "minute");
  if (diffMins < 60) return `${diffMins} mins ago`;
  const diffHours = now.diff(d, "hour");
  if (diffHours < 24) return `${diffHours} hours ago`;
  const diffDays = now.diff(d, "day");
  if (diffDays === 1) return `Yesterday`;
  return d.format("MMM D, YYYY");
};

const Notifications = () => {
  const { data, isLoading, setPage, setPageSize, pagination } =
    usePaginatedQuery("getNotification", {
      initialPage: 1,
      initialPageSize: 10,
    });

  // Use backend data as is
  const notifications = data?.data || [];
  const grouped = groupNotificationsByDate(notifications);

  const handlePageChange = (page, pageSize) => {
    setPage(page);
    setPageSize(pageSize);
  };

  return (
    <div>
      <InnerLayout>
        <div className="container-fluid">
          <div className="row">
            <div className="col-12 mt-4">
              <p className="font-36 color-black font-600 ">Notifications</p>
            </div>
            <div className="col-12 mt-4">
              {isLoading ? (
                <>
                  {[...Array(5)].map((_, idx) => (
                    <div className="mb-4" key={idx}>
                      <Skeleton.Avatar active size={40} shape="circle" />
                      <Skeleton
                        active
                        title={false}
                        paragraph={{ rows: 2, width: [200, 120] }}
                        className="ms-2 d-inline-block align-middle"
                        style={{ width: 250 }}
                      />
                    </div>
                  ))}
                </>
              ) : notifications.length === 0 ? (
                <div className="text-center py-5">
                  <EmptyState />
                </div>
              ) : (
                Object.entries(grouped).map(([label, notifs]) => (
                  <div key={label} className="mb-4">
                    <div className="mb-4 font-16 font-600 color-grey">
                      {label}
                    </div>
                    {notifs.map((item, idx) => (
                      <NotificationItem
                        key={item.id || idx}
                        avatar={item?.actor?.image_url}
                        message={item.body}
                        time={formatTimeAgo(item.created_at)}
                        notification={item}
                      />
                    ))}
                  </div>
                ))
              )}
            </div>
            <div className="col-12">
              <ReusablePagination
                pagination={pagination}
                handlePageChange={handlePageChange}
                isLoading={isLoading}
                itemName="notifications"
                align="end"
              />
            </div>
          </div>
        </div>
      </InnerLayout>
    </div>
  );
};

export default Notifications;
