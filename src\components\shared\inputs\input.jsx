import React from "react";
import { Form, Input, InputNumber } from "antd";

const InputField = ({
  name,
  rules,
  icon,
  defaultValue,
  placeholder,
  className,
  disabled,
  label,
  type,
  suffix,
  onChange,
  min,
  max,
  maxLength,
  formatter,
  prefix,
  parser,
}) => {
  return (
    <Form.Item label={label} name={name} rules={rules} validateTrigger="onBlur">
      {type === "number" ? (
        <InputNumber
          prefix={icon}
          suffix={suffix}
          prefix={prefix}
          size="large"
          defaultValue={defaultValue}
          placeholder={placeholder}
          className={className}
          disabled={disabled}
          onChange={onChange}
          min={min}
          max={max}
          maxLength={maxLength}
          formatter={formatter}
          parser={parser}
          style={{ width: "100%" }}
        />
      ) : (
        <Input
          prefix={icon}
          suffix={suffix}
          size="large"
          defaultValue={defaultValue}
          placeholder={placeholder}
          className={className}
          disabled={disabled}
          type={type}
          onChange={onChange}
        />
      )}
    </Form.Item>
  );
};

export default InputField;
