import React, { useState, useEffect } from "react";
import InnerLayout from "@/components/shared/layout/innerlayout";
import PropertyCard from "@/components/shared/card/propertycard";
import SearchBar from "@/components/shared/inputs/searchbar";
import BaseInput from "@/components/shared/inputs";
import Filter from "@/components/shared/Filter";
import ReusablePagination from "@/components/shared/ReusablePagination";
import EmptyState from "@/components/shared/EmptyState";
import useSearchFilterPagination from "@/hooks/useSearchFilterPagination";
import useLocationData from "@/hooks/useLocationData";
import useStartupData from "@/hooks/reactQuery/useStartupData";
import { transformPropertiesData } from "@/utils/propertyUtils";
import { Skeleton } from "antd";
import { FilterProvider } from "@/store/FilterContext";
import _ from "lodash";

const StateContent = () => {
  // Screen-specific state and city (separate from filter modal)
  const [screenState, setScreenState] = useState("");
  const [screenCity, setScreenCity] = useState("");
  const [isDataReady, setIsDataReady] = useState(false);

  // Get startup data FIRST
  const { data: startupResponse, loading: startupLoading } = useStartupData();
  const startupData = startupResponse?.data;

  // Get location data - same data as rest of the app
  const {
    stateOptions,
    cityOptions,
    statesLoading,
    citiesLoading,
    handleStateChange,
  } = useLocationData();

  // Properties API - only triggered after both state and city are selected
  const { data, isLoading, pagination, handlePageChange, handleFilterClick } =
    useSearchFilterPagination("properties", {
      pageSize: 12,
      enabled: isDataReady && screenState && screenCity,
      defaultParams: isDataReady
        ? { state: screenState, city: screenCity }
        : {},
    });

  // Auto-select first state when data is loaded
  useEffect(() => {
    if (!startupLoading && stateOptions.length > 0 && !screenState) {
      const firstState = stateOptions[0].value;
      setScreenState(firstState);
      handleStateChange(firstState);
    }
  }, [startupLoading, stateOptions, screenState, handleStateChange]);

  // Auto-select first city when cities are loaded
  useEffect(() => {
    if (
      screenState &&
      !citiesLoading &&
      cityOptions.length > 0 &&
      !screenCity
    ) {
      const firstCity = cityOptions[0].value;
      setScreenCity(firstCity);
      setIsDataReady(true);
    }
  }, [screenState, citiesLoading, cityOptions, screenCity]);

  // Handle state change on this screen
  const handleScreenStateChange = (value) => {
    setScreenState(value);
    setScreenCity(""); // Reset city
    // Don't reset isDataReady here - keep UI stable until city is selected
    handleStateChange(value); // Load cities for new state
  };

  // Handle city change on this screen
  const handleScreenCityChange = (value) => {
    setScreenCity(value);
    setIsDataReady(true); // Now trigger Properties API with updated params
  };

  // Transform properties data
  const properties = React.useMemo(() => {
    if (!data?.data) return [];
    return transformPropertiesData(data.data);
  }, [data]);

  // Filter options - same as Property Listing
  const bedBathOptions = Array.from({ length: 10 }, (_, i) => ({
    value: i + 1,
    label: i + 1,
  }));

  const garageOptions = Array.from({ length: 10 }, (_, i) => ({
    value: i + 1,
    label: `${i + 1} Car${i + 1 > 1 ? "s" : ""}`,
  }));

  const sizeTypeOptions = [
    { value: "sqft", label: "Sq.Ft" },
    { value: "sq.m", label: "Sq.M" },
    { value: "sq.yd", label: "Sq.Yd" },
    { value: "acres", label: "Acres" },
  ];

  // Filter fields - exact same as Property Listing
  const filterFields = [
    {
      name: "type_id",
      label: "Property Type",
      type: "select",
      placeholder: "Select Property Type",
      options: startupData?.propertyTypes?.map((type) => ({
        value: type.id,
        label: type.name,
      })),
      loading: startupLoading,
    },
    {
      name: "home_style_id",
      label: "Home Style",
      type: "select",
      placeholder: "Select Home Style",
      options: startupData?.homeStyles?.map((style) => ({
        value: style.id,
        label: style.name,
      })),
      loading: startupLoading,
    },
    {
      name: "state",
      label: "State",
      type: "select",
      placeholder: "Select State",
    },
    {
      name: "city",
      label: "City",
      type: "select",
      placeholder: "Select City",
    },
    {
      name: "zip",
      label: "Zip Code",
      type: "input",
      placeholder: "Enter Zip Code",
    },
    {
      name: "basement",
      label: "Basement",
      type: "radio",
      options: [
        { value: true, label: "Yes" },
        { value: false, label: "No" },
      ],
    },
    {
      name: "max_bed",
      label: "Bed",
      type: "select",
      placeholder: "Select Bedrooms",
      options: bedBathOptions,
    },
    {
      name: "max_bath",
      label: "Bath",
      type: "select",
      placeholder: "Select Bathrooms",
      options: bedBathOptions,
    },
    {
      name: "max_garage",
      label: "Garage",
      type: "select",
      placeholder: "Select Garage",
      options: garageOptions,
    },
    {
      name: "size_type",
      label: "Size Type",
      type: "select",
      placeholder: "Select Size Type",
      options: sizeTypeOptions,
    },
  ];

  return (
    <InnerLayout>
      <div className="container-fluid">
        <div className="row">
          <div className="col-12 state-filter">
            <SearchBar />
          </div>
          <div className="col-12 mt-3">
            <div className="d-flex align-items-center justify-content-between">
              <div>
                {/* Show skeleton for title while loading */}
                {!isDataReady ? (
                  <Skeleton.Input style={{ width: 200, height: 36 }} />
                ) : (
                  <p className="font-36 font-600">{screenCity}</p>
                )}
              </div>
              <div className="d-flex align-items-center state-select-area">
                {/* Show skeleton for selects while loading */}
                {!isDataReady ? (
                  <>
                    <div className="ms-3">
                      <Skeleton.Input style={{ width: 150, height: 40 }} />
                    </div>
                    <div>
                      <Skeleton.Input style={{ width: 150, height: 40 }} />
                    </div>
                  </>
                ) : (
                  <>
                    <BaseInput
                      type="select"
                      placeholder="State"
                      className="ms-3"
                      options={stateOptions}
                      loading={statesLoading}
                      value={screenState}
                      handlechange={handleScreenStateChange}
                      showSearch={true}
                    />
                    <BaseInput
                      key={screenState} // Force re-render when state changes
                      type="select"
                      placeholder="City"
                      options={cityOptions}
                      loading={citiesLoading}
                      value={screenCity}
                      handlechange={handleScreenCityChange}
                      disabled={!screenState}
                      showSearch={true}
                    />
                  </>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Filter Component - exact same behavior as Property Listing */}
        <Filter fields={filterFields} />

        <div className="row mt-3">
          {/* Show listing loader while state/city are being loaded */}
          {!isDataReady ? (
            Array.from({ length: 12 }).map((_, index) => (
              <div key={index} className="col-12 col-sm-6 col-md-4 col-lg-3">
                <div className="card">
                  <div className="card-body">
                    <div className="d-flex align-items-center mb-3">
                      <Skeleton.Avatar size={40} />
                      <div className="ms-3 flex-grow-1">
                        <Skeleton.Input style={{ width: 120, height: 16 }} />
                        <div className="mt-1">
                          <Skeleton.Input style={{ width: 80, height: 12 }} />
                        </div>
                      </div>
                    </div>

                    <Skeleton.Image
                      active
                      className="w-100 text-center align-items-center d-flex mb-2"
                      style={{ width: "100%", height: 200, display: "block" }}
                    />

                    <Skeleton paragraph={{ rows: 2, width: ["100%", "80%"] }} />
                    <div className="d-flex justify-content-between align-items-center mt-3">
                      <div className="d-flex gap-3">
                        <Skeleton.Input style={{ width: 60, height: 20 }} />
                        <Skeleton.Input style={{ width: 60, height: 20 }} />
                        <Skeleton.Input style={{ width: 60, height: 20 }} />
                      </div>
                      <Skeleton.Input style={{ width: 40, height: 20 }} />
                    </div>
                  </div>
                </div>
              </div>
            ))
          ) : // Show properties after state and city are ready
          isLoading ? (
            Array.from({ length: 12 }).map((_, index) => (
              <div key={index} className="col-12 col-sm-6 col-md-4 col-lg-3">
                <div className="card">
                  <div className="card-body">
                    <div className="d-flex align-items-center mb-3">
                      <Skeleton.Avatar size={40} />
                      <div className="ms-3 flex-grow-1">
                        <Skeleton.Input style={{ width: 120, height: 16 }} />
                        <div className="mt-1">
                          <Skeleton.Input style={{ width: 80, height: 12 }} />
                        </div>
                      </div>
                    </div>

                    <Skeleton.Image
                      active
                      className="w-100 text-center align-items-center d-flex mb-2"
                      style={{ width: "100%", height: 200, display: "block" }}
                    />

                    <Skeleton paragraph={{ rows: 2, width: ["100%", "80%"] }} />
                    <div className="d-flex justify-content-between align-items-center mt-3">
                      <div className="d-flex gap-3">
                        <Skeleton.Input style={{ width: 60, height: 20 }} />
                        <Skeleton.Input style={{ width: 60, height: 20 }} />
                        <Skeleton.Input style={{ width: 60, height: 20 }} />
                      </div>
                      <Skeleton.Input style={{ width: 40, height: 20 }} />
                    </div>
                  </div>
                </div>
              </div>
            ))
          ) : !_.isEmpty(properties) ? (
            properties.map((item, index) => (
              <div
                key={item.id || index}
                className="col-12 col-sm-6 col-md-4 col-lg-3"
              >
                <PropertyCard {...item} />
              </div>
            ))
          ) : (
            <EmptyState
              title="No properties found"
              description="No properties available in this location"
            />
          )}
        </div>

        {/* Pagination - same as Property Listing */}
        {isDataReady && (
          <ReusablePagination
            pagination={pagination}
            handlePageChange={handlePageChange}
            isLoading={isLoading}
            itemName="properties"
            pageSizeOptions={["12", "24", "48"]}
          />
        )}
      </div>
    </InnerLayout>
  );
};

const State = () => {
  return (
    <FilterProvider>
      <StateContent />
    </FilterProvider>
  );
};

export default State;
