import { useCallback } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useMutation } from "@/hooks/reactQuery";

export const useCommentSubmission = (postId) => {
  const queryClient = useQueryClient();

  // Helper function to update comment count across all post instances
  const updatePostCommentCount = useCallback((increment = true) => {
    const delta = increment ? 1 : -1;
    
    // Update all postItem queries
    queryClient.setQueriesData(
      { queryKey: ["postItem"] },
      (oldData) => {
        if (!oldData) return oldData;

        // Handle paginated data structure
        if (oldData.data && Array.isArray(oldData.data)) {
          return {
            ...oldData,
            data: oldData.data.map(post => 
              post.id === postId || post.id === parseInt(postId)
                ? {
                    ...post,
                    comments_count: Math.max(0, (post.comments_count || 0) + delta)
                  }
                : post
            )
          };
        }

        // Handle single post data structure
        if (oldData.data && (oldData.data.id === postId || oldData.data.id === parseInt(postId))) {
          return {
            ...oldData,
            data: {
              ...oldData.data,
              comments_count: Math.max(0, (oldData.data.comments_count || 0) + delta)
            }
          };
        }

        // Handle direct array structure
        if (Array.isArray(oldData)) {
          return oldData.map(post => 
            post.id === postId || post.id === parseInt(postId)
              ? {
                  ...post,
                  comments_count: Math.max(0, (post.comments_count || 0) + delta)
                }
              : post
          );
        }

        // Handle direct post object
        if (oldData.id === postId || oldData.id === parseInt(postId)) {
          return {
            ...oldData,
            comments_count: Math.max(0, (oldData.comments_count || 0) + delta)
          };
        }

        return oldData;
      }
    );
  }, [postId, queryClient]);

  const { mutate: submitComment, isPending: isSubmittingComment } = useMutation(
    "comments",
    {
      useFormData: false,
      showSuccessNotification: false,
      onMutate: async () => {
        // Cancel any outgoing refetches
        await queryClient.cancelQueries({ queryKey: ["postItem"] });
        await queryClient.cancelQueries({ queryKey: ["getComments"] });

        // Optimistically update comment count
        updatePostCommentCount(true);

        return { postId };
      },
      onError: (error, variables, context) => {
        // Revert optimistic update
        updatePostCommentCount(false);
      },
      onSuccess: (data) => {
        // Add the new comment to the comments cache
        queryClient.setQueryData(
          ["getComments", { params: { post_id: postId } }],
          (oldData) => {
            if (!oldData) return { data: [data] };
            
            if (oldData.data && Array.isArray(oldData.data)) {
              return {
                ...oldData,
                data: [...oldData.data, data]
              };
            }
            
            if (Array.isArray(oldData)) {
              return [...oldData, data];
            }
            
            return { data: [data] };
          }
        );
      },
      invalidateQueries: [
        { queryKey: ["getComments"], type: "all" },
        { queryKey: ["postItem"], type: "all" },
      ],
    }
  );

  return {
    submitComment,
    isSubmittingComment,
  };
};

export default useCommentSubmission;