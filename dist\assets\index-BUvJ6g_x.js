import{u as d,j as e}from"./index-Dklazue-.js";import{I as w,u as g}from"./index-vmMMvWhJ.js";import{O as m}from"./optionlist-DUV6NZmx.js";import{u as c}from"./useMutation-BrUrPIzr.js";import"./button-DNhBCuue.js";import"./index-Cj6uPc4c.js";const A=()=>{d();const{showAlert:o}=g(),{mutate:s,isPending:t}=c("deleteAccount",{onSuccess:async()=>{localStorage.clear(),window.user={},window.location.replace("/login")}}),{mutate:l,isPending:n}=c("disableAccount",{onSuccess:async()=>{localStorage.clear(),window.user={},window.location.replace("/login")}}),i=async a=>{a==="Deactivate account"?(await o({title:"Are you sure?",text:"Your account will be deactivated and you will be logged out!",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, deactivate it!",cancelButtonText:"Cancel"})).isConfirmed&&l():a==="Delete Account"&&(await o({title:"Are you sure?",text:"This action cannot be undone. Your account will be permanently deleted and you will be logged out!",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, delete it!",cancelButtonText:"Cancel"})).isConfirmed&&s({slug:window.user.id,data:""})},r=[{label:"Change Password",link:"/change-password"},{label:n?"Deactivating...":"Deactivate account",action:()=>!n&&!t&&i("Deactivate account"),disabled:n||t},{label:t?"Deleting...":"Delete Account",action:()=>!n&&!t&&i("Delete Account"),disabled:n||t}];return e.jsx(w,{children:e.jsx("div",{className:"container-fluid",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12 mt-4",children:e.jsx("p",{className:"font-36 color-black font-600",children:"Settings"})}),e.jsx("div",{className:"col-12",children:e.jsx(m,{title:"Settings",options:r})})]})})})};export{A as default};
