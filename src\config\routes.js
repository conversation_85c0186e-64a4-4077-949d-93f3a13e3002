import { lazy } from "react";

/**
 * Route configuration with authentication requirements
 */

// Public routes (accessible without authentication)
export const publicRoutes = [
  {
    path: "/login",
    name: "login",
    component: lazy(() => import("@/pages/auth/login")),
    exact: true,
  },
  {
    path: "/signup",
    name: "signup",
    component: lazy(() => import("@/pages/auth/signup")),
    exact: true,
  },
  {
    path: "/forgetpassword",
    name: "forgetpassword",
    component: lazy(() => import("@/pages/auth/forgetpassword")),
    exact: true,
  },
];

// Private routes (require authentication)
export const privateRoutes = [
  {
    path: "/home",
    name: "home",
    component: lazy(() => import("@/pages/home")),
    exact: true,
  },
  {
    path: "/sphare-it",
    name: "shareit",
    component: lazy(() => import("@/pages/sphereit")),
    exact: true,
  },
  {
    path: "/sphare-it/:id",
    name: "shareit-detail",
    component: lazy(() => import("@/pages/sphereit/detail")),
    exact: true,
  },
  {
    path: "/agent",
    name: "agent",
    component: lazy(() => import("@/pages/agent")),
    exact: true,
  },
  {
    path: "/agent/detail/:user_id/:type",
    name: "agent-detail",
    component: lazy(() => import("@/pages/agent/detail")),
    exact: true,
  },
  {
    path: "/agent/detail/posts",
    name: "agent-detail-posts",
    component: lazy(() => import("@/pages/agent/detail")),
    exact: true,
  },
  {
    path: "/listing",
    name: "listing",
    component: lazy(() => import("@/pages/listing")),
    exact: true,
  },
  {
    path: "/listing/detail/:id",
    name: "listing-detail",
    component: lazy(() => import("@/pages/listing/detail")),
    exact: true,
  },
  {
    path: "/listing/add/:id?",
    name: "add-listing",
    component: lazy(() => import("@/pages/listing/add")),
    exact: true,
  },
  {
    path: "/state",
    name: "state",
    component: lazy(() => import("@/pages/states")),
    exact: true,
  },
  {
    path: "/contract",
    name: "contract",
    component: lazy(() => import("@/pages/contract")),
    exact: true,
  },
  {
    path: "/contract/:id",
    name: "contract-detail",
    component: lazy(() => import("@/pages/contract/detail")),
    exact: true,
  },
  {
    path: "/favourite",
    name: "favourite",
    component: lazy(() => import("@/pages/favourite")),
    exact: true,
  },
  {
    path: "/notifications",
    name: "notifications",
    component: lazy(() => import("@/pages/notifications")),
    exact: true,
  },
  {
    path: "/setting",
    name: "setting",
    component: lazy(() => import("@/pages/setting")),
    exact: true,
  },
  {
    path: "/about",
    name: "about",
    component: lazy(() => import("@/pages/about")),
    exact: true,
  },
  {
    path: "/about/faq",
    name: "faq",
    component: lazy(() => import("@/pages/staticpages/faq")),
    exact: true,
  },
  {
    path: "/about/terms",
    name: "terms",
    component: lazy(() => import("@/pages/staticpages/terms")),
    exact: true,
  },
  {
    path: "/about/privacy",
    name: "privacy",
    component: lazy(() => import("@/pages/staticpages/privacy")),
    exact: true,
  },
  {
    path: "/subscription/:token?",
    name: "subscription",
    component: lazy(() => import("@/pages/subscription")),
    exact: true,
  },
  {
    path: "/subscription/history",
    name: "history",
    component: lazy(() => import("@/pages/subscription/history")),
    exact: true,
  },
  {
    path: "/profile/:type",
    name: "profile",
    component: lazy(() => import("@/pages/profile")),
    exact: true,
  },
  {
    path: "/editprofile",
    name: "edit-profile",
    component: lazy(() => import("@/pages/profile/editprofile")),
    exact: true,
  },
  {
    path: "/inbox",
    name: "inbox",
    component: lazy(() => import("@/pages/chat")),
    exact: true,
  },
  {
    path: "/change-password",
    name: "change-password",
    component: lazy(() => import("@/pages/setting/changepassword")),
    exact: true,
  },
];

// Special routes
export const specialRoutes = [
  {
    path: "/",
    name: "root",
    component: lazy(() => import("@/pages/banner")),
    exact: true,
  },
];

// Error routes
export const errorRoutes = [
  {
    path: "*",
    name: "not-found",
    component: lazy(() => import("@/pages/errors/NotFound")),
  },
];
