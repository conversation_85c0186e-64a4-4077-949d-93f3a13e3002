import React from "react";
import { Bad<PERSON>, Avatar } from "antd";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";

dayjs.extend(relativeTime);

const MessageBubble = ({ msg }) => {
  const currentUserId = window.user?.id;
  const isMyMessage = msg?.user_id === currentUserId;
  const userName = isMyMessage ? window.user?.name : msg.user?.name;
  const userAvatar = isMyMessage
    ? window.user?.image_url || "/assets/img/avatar-1.png"
    : msg?.user?.image_url || "/assets/img/avatar-1.png";
  const messageTime = formatTime(msg.created_at);
  const messageText = msg.message;
  return (
    <div className={isMyMessage ? "my-chat-box" : "user-chat-box"}>
      <div className="d-flex ms-3">
        {!isMyMessage && (
          <div className="chat-user-img">
            <Avatar src={userAvatar} size={50} alt={userName} />
          </div>
        )}
        <div className={isMyMessage ? "me-3" : "ms-3"}>
          {messageText && (
            <div className="text-user">
              <p>{messageText}</p>
            </div>
          )}

          <p className="color-light mt-2">{messageTime}</p>
        </div>
        {isMyMessage && (
          <div className="chat-user-img">
            <Avatar src={userAvatar} size={50} alt={userName} />
          </div>
        )}
      </div>
    </div>
  );
};

// Helper function to format time
const formatTime = (timestamp) => {
  if (!timestamp) return "";

  const messageTime = dayjs(timestamp);
  const now = dayjs();
  const diffInHours = now.diff(messageTime, "hour");

  if (diffInHours < 1) {
    const diffInMinutes = now.diff(messageTime, "minute");
    return diffInMinutes < 1 ? "Now" : `${diffInMinutes} min ago`;
  } else if (diffInHours < 24) {
    return `${diffInHours}hr ago`;
  } else {
    return messageTime.format("MM/DD/YYYY");
  }
};

export default MessageBubble;
