import{j as e,i as I,k as Y,r as j,m as W}from"./index-Dklazue-.js";import{I as O}from"./index-vmMMvWhJ.js";import{B as F}from"./index-BUt89ETK.js";import{A as D}from"./index-Cj6uPc4c.js";import{S as k}from"./Skeleton--lTrbnrp.js";import{P}from"./postinput-CVyP1L1L.js";import{R as V}from"./index-CjGjc6T5.js";import"./useMutation-BrUrPIzr.js";import"./button-DNhBCuue.js";import"./react-stripe.esm-ypQSOYN5.js";import"./useLocale-BNhrTARD.js";import"./flatbutton-B_tUS4QM.js";import"./trialUtils-PQN1eD5v.js";import"./index-BNeAK5sW.js";import"./index-ChnPz6Q1.js";import"./fade-B36sOBLs.js";import"./index-CHbHgJvR.js";const q=({name:i,message:c,isTyping:r,msgTime:o,msgStatus:m,onlineStatus:t,avatar:n})=>e.jsxs("div",{className:"chat-user",children:[e.jsx("div",{className:"chat-user-img",children:e.jsx(D,{src:n,size:40})}),e.jsxs("div",{className:"chat-user-msg-area ms-3 d-flex justify-content-between w-100",children:[e.jsxs("div",{children:[e.jsx("p",{className:"font-16 color-black",children:i}),e.jsx("p",{className:"color-light font-14",children:r?e.jsx("em",{children:"Typing..."}):c})]}),e.jsx("div",{className:"text-end",children:e.jsx("p",{className:"font-12 color-gray",children:o})})]})]});I.extend(Y);const G=({rooms:i,selectedRoom:c,onSelectRoom:r,loading:o,connected:m})=>{if(o)return e.jsx("div",{style:{padding:"10px 0"},children:[...Array(5)].map((n,f)=>e.jsx("div",{className:"chat-user",style:{marginBottom:"15px"},children:e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx(k.Avatar,{size:50}),e.jsxs("div",{className:"ms-3 flex-grow-1",children:[e.jsx(k.Input,{style:{width:120,height:16,marginBottom:8},active:!0}),e.jsx(k.Input,{style:{width:200,height:14},active:!0})]}),e.jsx("div",{className:"text-end",children:e.jsx(k.Input,{style:{width:60,height:12,marginBottom:8},active:!0})})]})},f))});if(!m)return e.jsx("div",{className:"text-center py-5",children:e.jsx("p",{className:"color-light",children:"Connecting to chat server..."})});if(i.length===0)return e.jsxs("div",{className:"text-center py-5",children:[e.jsx("p",{className:"color-light",children:"No conversations found"}),e.jsx("p",{className:"color-light font-12",children:"Start a new conversation to see it here"})]});const t=n=>{if(!n)return"";const f=I(n),l=I(),g=l.diff(f,"hour");if(g<1){const p=l.diff(f,"minute");return p<1?"Now":`${p} min ago`}else return g<24?`${g}hr ago`:f.format("MM/DD/YYYY")};return e.jsx(e.Fragment,{children:i.map((n,f)=>{var g,p;const l=(c==null?void 0:c.id)===n.id;return e.jsx("div",{onClick:()=>r(n),style:{cursor:"pointer",backgroundColor:l?"#f5f5f5":"transparent",borderRadius:"8px",padding:"5px"},children:e.jsx(q,{name:n.room_title||`Room ${n.id}`,message:((g=n.last_message)==null?void 0:g.message)||"No messages yet",isTyping:!1,msgTime:t((p=n.last_message)==null?void 0:p.created_at),msgStatus:"sent",onlineStatus:"online",avatar:n.room_image||"/assets/img/avatar-1.png"})},n.id||`room-${f}`)})})};I.extend(Y);const J=({msg:i})=>{var f,l,g,p,v;const c=(f=window.user)==null?void 0:f.id,r=(i==null?void 0:i.user_id)===c,o=r?(l=window.user)==null?void 0:l.name:(g=i.user)==null?void 0:g.name,m=r?((p=window.user)==null?void 0:p.image_url)||"/assets/img/avatar-1.png":((v=i==null?void 0:i.user)==null?void 0:v.image_url)||"/assets/img/avatar-1.png",t=K(i.created_at),n=i.message;return e.jsx("div",{className:r?"my-chat-box":"user-chat-box",children:e.jsxs("div",{className:"d-flex ms-3",children:[!r&&e.jsx("div",{className:"chat-user-img",children:e.jsx(D,{src:m,size:50,alt:o})}),e.jsxs("div",{className:r?"me-3":"ms-3",children:[n&&e.jsx("div",{className:"text-user",children:e.jsx("p",{children:n})}),e.jsx("p",{className:"color-light mt-2",children:t})]}),r&&e.jsx("div",{className:"chat-user-img",children:e.jsx(D,{src:m,size:50,alt:o})})]})})},K=i=>{if(!i)return"";const c=I(i),r=I(),o=r.diff(c,"hour");if(o<1){const m=r.diff(c,"minute");return m<1?"Now":`${m} min ago`}else return o<24?`${o}hr ago`:c.format("MM/DD/YYYY")},Q=({user:i={}})=>{const{name:c="Alene",avatar:r="/assets/img/avatar-1.png",statusText:o="Offline",onlineStatus:m="offline"}=i;return e.jsx("div",{className:"detail-header d-flex align-items-center justify-content-between",children:e.jsx("div",{className:"d-flex align-items-center",children:e.jsxs("div",{className:"d-flex align-items-center ms-3",children:[e.jsx(D,{src:r,size:40}),e.jsx("div",{className:"ms-2",children:e.jsx("p",{children:c})})]})})})},X=({messages:i,selectedRoom:c,otherUser:r,onSendMessage:o,connected:m})=>{const[t,n]=j.useState(""),f=j.useRef(null);j.useEffect(()=>{l()},[i]);const l=()=>{var v;(v=f.current)==null||v.scrollIntoView({behavior:"smooth"})},g=()=>{t.trim()&&m&&o&&(o(t),n(""))};if(!c)return e.jsx("div",{className:"chat-detail d-flex align-items-center justify-content-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h4",{className:"color-light mt-3",children:"Select a conversation"}),e.jsx("p",{className:"color-light",children:"Choose a conversation from the list to start messaging"})]})});const p={name:r==null?void 0:r.name,avatar:(r==null?void 0:r.image_url)||c.room_image||"/assets/img/avatar-1.png"};return e.jsxs("div",{className:"chat-detail",children:[e.jsx(Q,{user:p}),e.jsxs("div",{className:"chat-area",children:[i.length===0?e.jsxs("div",{className:"text-center py-5",children:[e.jsx("p",{className:"color-light",children:"No messages yet"}),e.jsx("p",{className:"color-light font-12",children:"Start the conversation by sending a message"})]}):i.map((v,b)=>e.jsx(J,{msg:v},v.id?`msg-${v.id}`:`temp-${b}-${Date.now()}`)),e.jsx("div",{ref:f})]}),e.jsx("div",{className:"chat-send-area",children:e.jsx(P,{placeholder:"Type a message...",button:e.jsx("img",{src:"/assets/img/chat-send-icon.png",alt:"Send",style:{cursor:m&&t.trim()?"pointer":"not-allowed",opacity:m&&t.trim()?1:.5},onClick:g}),postCreationHook:{content:t,setContent:n,isFormValid:t.trim().length>0,handleSubmit:g}})})]})},je=()=>{const i=W(),[c,r]=j.useState(!1),[o,m]=j.useState([]),[t,n]=j.useState(null),[f,l]=j.useState([]),[g,p]=j.useState(""),[v,b]=j.useState(!1),C=j.useRef(null),E=j.useRef(null);j.useEffect(()=>{E.current=t},[t]),j.useEffect(()=>{window.socket&&!C.current&&(C.current=window.socket,B(),r(!0),R())},[]),j.useEffect(()=>{var s,d,a,x,h,y;if((s=i.state)!=null&&s.directMessage&&((d=i.state)!=null&&d.targetUser)&&o.length>0){const u=i.state.targetUser,w=(a=window.user)==null?void 0:a.id,_=o.find(N=>{var S;return(S=N.users)==null?void 0:S.some(M=>M.id===u.id&&M.id!==w)});if(_)n(_),l([]),A(_.id);else{const N={id:`temp_${u.id}`,users:[{id:(x=window.user)==null?void 0:x.id,name:(h=window.user)==null?void 0:h.name,image_url:(y=window.user)==null?void 0:y.image_url},u],isDirectMessage:!0,targetUserId:u.id};n(N),l([])}window.history.replaceState({},document.title)}},[i.state,o]);const B=()=>{const s=C.current;s&&(s.on("connect",()=>{r(!0),R()}),s.on("disconnect",()=>{r(!1),m([]),n(null),l([])}),s.on("_receivedMessage",d=>{var u;console.log("Received message:",d);const a=d==null?void 0:d.data;if(!a)return;const x=(u=window.user)==null?void 0:u.id,h=a.user_id===x,y=E.current;y&&a.chat_room_id===y.id&&!h&&l(w=>w.some(N=>N.id===a.id)?w:[...w,a]),$(a)}))},T=(s,d,a=null,x=null)=>{C.current&&C.current.emit(s,d,h=>{h.status&&typeof a=="function"?a(h.data):typeof x=="function"&&x(h)})},R=()=>{b(!0),T("_loadRoomsWithCb",{keyword:g,limit:10},s=>{m(s||[]),b(!1)},s=>{b(!1)})},U=s=>{n(s),l([]),A(s.id)},A=(s,d=null)=>{s&&T("_loadChatHistoryWithCb",{chat_room_id:s,last_record_id:d},a=>{l(Array.isArray(a)?a.reverse():[])})},H=s=>{var x,h,y;if(!s.trim()||!t)return;if(t.isDirectMessage){T("_sendMessageWithCb",{other_user_id:t.targetUserId,message:s,message_type:"text"},u=>{var _,N,S;console.log("Direct message sent:",u);const w={...u,user:{id:(_=window.user)==null?void 0:_.id,name:(N=window.user)==null?void 0:N.name,image_url:(S=window.user)==null?void 0:S.image_url}};l(M=>[...M,w]),u.chat_room_id&&n(M=>({...M,id:u.chat_room_id,isDirectMessage:!1})),$(w),R()});return}const d=(x=window.user)==null?void 0:x.id,a=(y=(h=t.users)==null?void 0:h.find(u=>u.id!==d))==null?void 0:y.id;T("_sendMessageWithCb",{chat_room_id:t.id,other_user_id:a,message:s,message_type:"text"},u=>{var _,N,S;console.log("data",u);const w={...u,user:{id:(_=window.user)==null?void 0:_.id,name:(N=window.user)==null?void 0:N.name,image_url:(S=window.user)==null?void 0:S.image_url}};l(M=>[...M,w]),$(w)})},$=s=>{m(d=>{const a=[...d],x=a.findIndex(h=>h.id===s.chat_room_id);if(x!==-1){const h={...a[x],last_message:{...s,created_at:s.created_at||new Date().toISOString()}};a.splice(x,1),a.unshift(h),(t==null?void 0:t.id)===s.chat_room_id&&n(h)}else R();return a})},L=s=>{p(s),setTimeout(()=>{c&&R()},500)},z=(()=>{var d;if(!(t!=null&&t.users))return null;const s=(d=window.user)==null?void 0:d.id;return t.users.find(a=>a.id!==s)||t.users[0]})();return e.jsx(O,{children:e.jsx("div",{className:"container-fluid mt-5",children:e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-12",children:e.jsxs("div",{className:"chat-box",children:[e.jsxs("div",{className:"chat-listing",children:[e.jsx("div",{className:"listing-header d-flex justify-content-between",children:e.jsxs("div",{className:"d-flex",children:[e.jsx("p",{className:"color-black font-18 me-2",children:"Messages"}),e.jsx("p",{className:"msg-numbers",children:o.length})]})}),e.jsx("div",{children:e.jsx(F,{placeholder:"Search in Messenger",icon:e.jsx(V,{style:{color:"gray"}}),className:"chat-search",value:g,onChange:s=>L(s.target.value)})}),e.jsx(G,{rooms:o,selectedRoom:t,onSelectRoom:U,loading:v,connected:c})]}),e.jsx(X,{messages:f,selectedRoom:t,otherUser:z,onSendMessage:H,connected:c})]})})})})})};export{je as default};
