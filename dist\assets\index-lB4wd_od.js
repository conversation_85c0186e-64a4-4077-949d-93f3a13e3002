import{e as E,r as w,l as F,R as b,j as e,u as I,_ as O}from"./index-Dklazue-.js";import{I as k,u as z}from"./index-vmMMvWhJ.js";import{S as A}from"./searchbar-BCS1MYCc.js";import{C as M}from"./ContractPost-BP1laxu2.js";import{u as R,C as T}from"./useContractPostCreation-DUI14t5S.js";import{a as D,u as _}from"./useQuery-C3n1GVcJ.js";import{u as B,P as K,a as Q}from"./PostsFilter-C_OIWz4k.js";import{R as q}from"./ReusablePagination-B7_yMXG_.js";import{u as L}from"./useMutation-BrUrPIzr.js";import{S as c}from"./Skeleton--lTrbnrp.js";import{E as W}from"./index-CHbHgJvR.js";import"./button-DNhBCuue.js";import"./index-Cj6uPc4c.js";import"./index-BUt89ETK.js";import"./react-stripe.esm-ypQSOYN5.js";import"./useLocale-BNhrTARD.js";import"./index-CjGjc6T5.js";import"./useLikePost-BsM_zOKm.js";import"./EditOutlined-DxjsqgjX.js";import"./DeleteOutlined-BjE_e6LE.js";import"./flatbutton-B_tUS4QM.js";import"./useStartupData-QD8kuEYy.js";import"./trialUtils-PQN1eD5v.js";import"./index-BNeAK5sW.js";import"./index-ChnPz6Q1.js";import"./fade-B36sOBLs.js";import"./index-Vj938NLd.js";import"./useLocationData-CQHRFCOb.js";import"./index-CatU6E6a.js";const H=(o={})=>{var u,j,P,y;const{searchKeyword:d}=E(),{filters:r,setIsFilterModalOpen:p}=B(),[s,x]=w.useState(d),h=w.useMemo(()=>F.debounce(t=>{x(t)},500),[]);w.useEffect(()=>(h(d),()=>h.cancel()),[d,h]);const l=w.useMemo(()=>Object.keys(r).some(t=>r[t]!==void 0&&r[t]!==null&&r[t]!=="")||s&&s.trim(),[r,s]),g=b.useMemo(()=>{const t={...r};return o.defaultParams&&Object.assign(t,o.defaultParams),s&&s.trim()&&(t.keyword=s.trim()),t.is_anonymous=!0,t.page||(t.page=1),t.limit||(t.limit=o.pageSize||12),Object.keys(t).forEach(i=>{i!=="page"&&i!=="limit"&&(t[i]===void 0||t[i]===null||t[i]==="")&&delete t[i]}),t},[s,r,o.defaultParams,o.pageSize]),a=D("postItem",{params:g,initialPage:1,initialPageSize:o.pageSize||12,staleTime:0,gcTime:0,keepPreviousData:!0,refetchOnWindowFocus:!1,refetchOnMount:!0,enabled:!l,...o}),S=_("postItem",{params:g,staleTime:0,gcTime:0,refetchOnWindowFocus:!1,refetchOnMount:!0,enabled:l,...o}),m=l?S:a,C=(t,i)=>{!l&&a.setPageSize&&a.setPage&&(i!==a.pageSize?(a.setPageSize(i),a.setPage(1)):a.setPage(t))},v=()=>{p(!0)};return{...m,pagination:l?{current:1,pageSize:((j=(u=m.data)==null?void 0:u.data)==null?void 0:j.length)||0,total:((y=(P=m.data)==null?void 0:P.data)==null?void 0:y.length)||0,totalPages:1}:a.pagination,handlePageChange:C,handleFilterClick:v,searchKeyword:d,debouncedSearchKeyword:s,filters:r,apiParams:g,hasActiveFilters:l}},Y=()=>{const o=I(),{showAlert:d}=z(),[r,p]=b.useState(null),{data:s,isLoading:x,pagination:h,handlePageChange:l,handleFilterClick:g,hasActiveFilters:a}=H({pageSize:12}),S=b.useCallback(()=>{p(null)},[]),m=R(r,S),{mutate:C,isPending:v}=L("deletePost",{useFormData:!1,showSuccessNotification:!1,invalidateQueries:[{queryKey:["postItem"],type:"paginated"}]}),u=(s==null?void 0:s.data)||[],j=[{name:"state",label:"State",type:"select",placeholder:"Select State"},{name:"city",label:"City",type:"select",placeholder:"Select City"},{name:"least_commented",label:"Least Commented On",type:"checkbox"},{name:"most_asked",label:"Most asked questions",type:"checkbox"}],P=n=>{const f=u.find(N=>N.id===n);f&&(p(f),window.scrollTo({top:0,behavior:"smooth"}))},y=()=>{p(null),m.resetForm()},t=async n=>{(await d({title:"Are you sure?",text:"Are you sure you want to delete this post?",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, delete it!",cancelButtonText:"Cancel"})).isConfirmed&&C({slug:n,data:""})},i=n=>!0;return e.jsx(k,{children:e.jsxs("div",{className:"container-fluid",children:[e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-12",children:[e.jsx(A,{onFilterClick:g}),e.jsx(T,{postCreationHook:m,onCancelEdit:y})]})}),e.jsx(Q,{fields:j}),e.jsx("div",{className:"row mt-5",children:x?Array.from({length:12}).map((n,f)=>e.jsx("div",{className:"col-12 col-md-6 col-lg-4 col-xl-3 mb-4",children:e.jsx("div",{className:"card",children:e.jsxs("div",{className:"card-body",children:[e.jsxs("div",{className:"d-flex align-items-center mb-3",children:[e.jsx(c.Avatar,{size:40}),e.jsxs("div",{className:"ms-3 flex-grow-1",children:[e.jsx(c.Input,{style:{width:120,height:16}}),e.jsx("div",{className:"mt-1",children:e.jsx(c.Input,{style:{width:80,height:12}})})]})]}),e.jsx(c.Image,{active:!0,className:"w-100 text-center align-items-center d-flex mb-2",style:{width:"100%",height:200,display:"block"}}),e.jsx(c,{paragraph:{rows:2,width:["100%","80%"]}}),e.jsxs("div",{className:"d-flex justify-content-between align-items-center mt-3",children:[e.jsxs("div",{className:"d-flex gap-3",children:[e.jsx(c.Input,{style:{width:60,height:20}}),e.jsx(c.Input,{style:{width:60,height:20}})]}),e.jsx(c.Input,{style:{width:40,height:20}})]})]})})},f)):O.isEmpty(u)?e.jsx("div",{className:"col-12 d-flex justify-content-center",children:e.jsx(W,{description:a?"No posts match your search criteria":"No contract Q&A posts available"})}):u.map(n=>e.jsx("div",{className:"col-12 col-md-6 col-lg-4 col-xl-3 mb-4",children:e.jsx(M,{...n,onClick:()=>o(`/contract/${n.id}`),showActions:i(),onEdit:P,onDelete:t})},n.id))}),!a&&e.jsx(q,{pagination:h,handlePageChange:l,isLoading:x,itemName:"posts",pageSizeOptions:["12","24","48","96"],align:"end"})]})})},Se=()=>e.jsx(K,{children:e.jsx(Y,{})});export{Se as default};
