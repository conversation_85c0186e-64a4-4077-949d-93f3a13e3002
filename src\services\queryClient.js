import { QueryClient } from "@tanstack/react-query";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      refetchOnMount: false,

      retry: (failureCount, error) => {
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        return failureCount < 1;
      },
      retryDelay: 2000,

      staleTime: 0,
      gcTime: 0,

      networkMode: "online",
      notifyOnChangeProps: ["data", "error", "isLoading"],

      structuralSharing: true,
      throwOnError: false,
    },
    mutations: {
      retry: false,
      retryDelay: 0,
      networkMode: "online",

      throwOnError: false,
    },
  },
});

export default queryClient;
