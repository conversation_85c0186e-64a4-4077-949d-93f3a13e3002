import{r as l,W as ui,v as F,R as a,z as Ie,K,x as M,y as I,Y as wi,E as Mi,w as Ti,N as _i,O as Ri,P as Di,Q as v,Z as ii,$ as Hi,a0 as Ai,a1 as Li,T as Ki,a2 as Vi,j as Ae}from"./index-Dklazue-.js";import{R as ti,a as ni}from"./index-vmMMvWhJ.js";import{b as ai,c as Wi}from"./useMutation-BrUrPIzr.js";import{u as qi}from"./button-DNhBCuue.js";import{i as Xi,a as Ji,g as <PERSON>,b as Ui,c as Gi,d as Qi,S as Yi}from"./index-CjGjc6T5.js";import{u as Zi}from"./useLocale-BNhrTARD.js";var ki={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M272.9 512l265.4-339.1c4.1-5.2.4-12.9-6.3-12.9h-77.3c-4.9 0-9.6 2.3-12.6 6.1L186.8 492.3a31.99 31.99 0 000 39.5l255.3 326.1c3 3.9 7.7 6.1 12.6 6.1H532c6.7 0 10.4-7.7 6.3-12.9L272.9 512zm304 0l265.4-339.1c4.1-5.2.4-12.9-6.3-12.9h-77.3c-4.9 0-9.6 2.3-12.6 6.1L490.8 492.3a31.99 31.99 0 000 39.5l255.3 326.1c3 3.9 7.7 6.1 12.6 6.1H836c6.7 0 10.4-7.7 6.3-12.9L576.9 512z"}}]},name:"double-left",theme:"outlined"},et=function(i,m){return l.createElement(ui,F({},i,{ref:m,icon:ki}))},ri=l.forwardRef(et),it={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M533.2 492.3L277.9 166.1c-3-3.9-7.7-6.1-12.6-6.1H188c-6.7 0-10.4 7.7-6.3 12.9L447.1 512 181.7 851.1A7.98 7.98 0 00188 864h77.3c4.9 0 9.6-2.3 12.6-6.1l255.3-326.1c9.1-11.7 9.1-27.9 0-39.5zm304 0L581.9 166.1c-3-3.9-7.7-6.1-12.6-6.1H492c-6.7 0-10.4 7.7-6.3 12.9L751.1 512 485.7 851.1A7.98 7.98 0 00492 864h77.3c4.9 0 9.6-2.3 12.6-6.1l255.3-326.1c9.1-11.7 9.1-27.9 0-39.5z"}}]},name:"double-right",theme:"outlined"},tt=function(i,m){return l.createElement(ui,F({},i,{ref:m,icon:it}))},oi=l.forwardRef(tt),nt={items_per_page:"条/页",jump_to:"跳至",jump_to_confirm:"确定",page:"页",prev_page:"上一页",next_page:"下一页",prev_5:"向前 5 页",next_5:"向后 5 页",prev_3:"向前 3 页",next_3:"向后 3 页",page_size:"页码"},at=[10,20,50,100],rt=function(i){var m=i.pageSizeOptions,t=m===void 0?at:m,d=i.locale,E=i.changeSize,O=i.pageSize,x=i.goButton,f=i.quickGo,N=i.rootPrefixCls,C=i.disabled,c=i.buildOptionText,B=i.showSizeChanger,T=i.sizeChangerRender,V=a.useState(""),w=Ie(V,2),h=w[0],P=w[1],W=function(){return!h||Number.isNaN(h)?void 0:Number(h)},k=typeof c=="function"?c:function(u){return"".concat(u," ").concat(d.items_per_page)},ne=function(g){P(g.target.value)},s=function(g){x||h===""||(P(""),!(g.relatedTarget&&(g.relatedTarget.className.indexOf("".concat(N,"-item-link"))>=0||g.relatedTarget.className.indexOf("".concat(N,"-item"))>=0))&&(f==null||f(W())))},z=function(g){h!==""&&(g.keyCode===K.ENTER||g.type==="click")&&(P(""),f==null||f(W()))},ee=function(){return t.some(function(g){return g.toString()===O.toString()})?t:t.concat([O]).sort(function(g,R){var ae=Number.isNaN(Number(g))?0:Number(g),X=Number.isNaN(Number(R))?0:Number(R);return ae-X})},y="".concat(N,"-options");if(!B&&!f)return null;var q=null,_=null,U=null;return B&&T&&(q=T({disabled:C,size:O,onSizeChange:function(g){E==null||E(Number(g))},"aria-label":d.page_size,className:"".concat(y,"-size-changer"),options:ee().map(function(u){return{label:k(u),value:u}})})),f&&(x&&(U=typeof x=="boolean"?a.createElement("button",{type:"button",onClick:z,onKeyUp:z,disabled:C,className:"".concat(y,"-quick-jumper-button")},d.jump_to_confirm):a.createElement("span",{onClick:z,onKeyUp:z},x)),_=a.createElement("div",{className:"".concat(y,"-quick-jumper")},d.jump_to,a.createElement("input",{disabled:C,type:"text",value:h,onChange:ne,onKeyUp:z,onBlur:s,"aria-label":d.page}),d.page,U)),a.createElement("li",{className:y},q,_)},pe=function(i){var m=i.rootPrefixCls,t=i.page,d=i.active,E=i.className,O=i.showTitle,x=i.onClick,f=i.onKeyPress,N=i.itemRender,C="".concat(m,"-item"),c=M(C,"".concat(C,"-").concat(t),I(I({},"".concat(C,"-active"),d),"".concat(C,"-disabled"),!t),E),B=function(){x(t)},T=function(h){f(h,x,t)},V=N(t,"page",a.createElement("a",{rel:"nofollow"},t));return V?a.createElement("li",{title:O?String(t):null,className:c,onClick:B,onKeyDown:T,tabIndex:0},V):null},ot=function(i,m,t){return t};function li(){}function ci(e){var i=Number(e);return typeof i=="number"&&!Number.isNaN(i)&&isFinite(i)&&Math.floor(i)===i}function Z(e,i,m){var t=typeof e>"u"?i:e;return Math.floor((m-1)/t)+1}var lt=function(i){var m=i.prefixCls,t=m===void 0?"rc-pagination":m,d=i.selectPrefixCls,E=d===void 0?"rc-select":d,O=i.className,x=i.current,f=i.defaultCurrent,N=f===void 0?1:f,C=i.total,c=C===void 0?0:C,B=i.pageSize,T=i.defaultPageSize,V=T===void 0?10:T,w=i.onChange,h=w===void 0?li:w,P=i.hideOnSinglePage,W=i.align,k=i.showPrevNextJumpers,ne=k===void 0?!0:k,s=i.showQuickJumper,z=i.showLessItems,ee=i.showTitle,y=ee===void 0?!0:ee,q=i.onShowSizeChange,_=q===void 0?li:q,U=i.locale,u=U===void 0?nt:U,g=i.style,R=i.totalBoundaryShowSizeChanger,ae=R===void 0?50:R,X=i.disabled,D=i.simple,re=i.showTotal,ve=i.showSizeChanger,Oe=ve===void 0?c>ae:ve,Be=i.sizeChangerRender,we=i.pageSizeOptions,he=i.itemRender,G=he===void 0?ot:he,be=i.jumpPrevIcon,H=i.jumpNextIcon,Q=i.prevIcon,oe=i.nextIcon,le=a.useRef(null),Y=ai(10,{value:B,defaultValue:V}),fe=Ie(Y,2),S=fe[0],Se=fe[1],Me=ai(1,{value:x,defaultValue:N,postState:function(o){return Math.max(1,Math.min(o,Z(void 0,S,c)))}}),ie=Ie(Me,2),r=ie[0],A=ie[1],Te=a.useState(r),Le=Ie(Te,2),te=Le[0],Ce=Le[1];l.useEffect(function(){Ce(r)},[r]);var Ke=Math.max(1,r-(z?3:5)),Ve=Math.min(Z(void 0,S,c),r+(z?3:5));function $e(n,o){var p=n||a.createElement("button",{type:"button","aria-label":o,className:"".concat(t,"-item-link")});return typeof n=="function"&&(p=a.createElement(n,Ti({},i))),p}function We(n){var o=n.target.value,p=Z(void 0,S,c),J;return o===""?J=o:Number.isNaN(Number(o))?J=te:o>=p?J=p:J=Number(o),J}function gi(n){return ci(n)&&n!==r&&ci(c)&&c>0}var pi=c>S?s:!1;function vi(n){(n.keyCode===K.UP||n.keyCode===K.DOWN)&&n.preventDefault()}function qe(n){var o=We(n);switch(o!==te&&Ce(o),n.keyCode){case K.ENTER:j(o);break;case K.UP:j(o-1);break;case K.DOWN:j(o+1);break}}function hi(n){j(We(n))}function bi(n){var o=Z(n,S,c),p=r>o&&o!==0?o:r;Se(n),Ce(p),_==null||_(r,n),A(p),h==null||h(p,n)}function j(n){if(gi(n)&&!X){var o=Z(void 0,S,c),p=n;return n>o?p=o:n<1&&(p=1),p!==te&&Ce(p),A(p),h==null||h(p,S),p}return r}var ye=r>1,xe=r<Z(void 0,S,c);function Xe(){ye&&j(r-1)}function Je(){xe&&j(r+1)}function Fe(){j(Ke)}function Ue(){j(Ve)}function ce(n,o){if(n.key==="Enter"||n.charCode===K.ENTER||n.keyCode===K.ENTER){for(var p=arguments.length,J=new Array(p>2?p-2:0),je=2;je<p;je++)J[je-2]=arguments[je];o.apply(void 0,J)}}function fi(n){ce(n,Xe)}function Si(n){ce(n,Je)}function Ci(n){ce(n,Fe)}function $i(n){ce(n,Ue)}function yi(n){var o=G(n,"prev",$e(Q,"prev page"));return a.isValidElement(o)?a.cloneElement(o,{disabled:!ye}):o}function xi(n){var o=G(n,"next",$e(oe,"next page"));return a.isValidElement(o)?a.cloneElement(o,{disabled:!xe}):o}function ze(n){(n.type==="click"||n.keyCode===K.ENTER)&&j(te)}var Ge=null,zi=wi(i,{aria:!0,data:!0}),Ni=re&&a.createElement("li",{className:"".concat(t,"-total-text")},re(c,[c===0?0:(r-1)*S+1,r*S>c?c:r*S])),Qe=null,b=Z(void 0,S,c);if(P&&c<=S)return null;var $=[],se={rootPrefixCls:t,onClick:j,onKeyPress:ce,showTitle:y,itemRender:G,page:-1},Pi=r-1>0?r-1:0,Ei=r+1<b?r+1:b,Ne=s&&s.goButton,ji=Mi(D)==="object"?D.readOnly:!D,ue=Ne,Ye=null;D&&(Ne&&(typeof Ne=="boolean"?ue=a.createElement("button",{type:"button",onClick:ze,onKeyUp:ze},u.jump_to_confirm):ue=a.createElement("span",{onClick:ze,onKeyUp:ze},Ne),ue=a.createElement("li",{title:y?"".concat(u.jump_to).concat(r,"/").concat(b):null,className:"".concat(t,"-simple-pager")},ue)),Ye=a.createElement("li",{title:y?"".concat(r,"/").concat(b):null,className:"".concat(t,"-simple-pager")},ji?te:a.createElement("input",{type:"text","aria-label":u.jump_to,value:te,disabled:X,onKeyDown:vi,onKeyUp:qe,onChange:qe,onBlur:hi,size:3}),a.createElement("span",{className:"".concat(t,"-slash")},"/"),b));var L=z?1:2;if(b<=3+L*2){b||$.push(a.createElement(pe,F({},se,{key:"noPager",page:1,className:"".concat(t,"-item-disabled")})));for(var me=1;me<=b;me+=1)$.push(a.createElement(pe,F({},se,{key:me,page:me,active:r===me})))}else{var Ii=z?u.prev_3:u.prev_5,Oi=z?u.next_3:u.next_5,Ze=G(Ke,"jump-prev",$e(be,"prev page")),ke=G(Ve,"jump-next",$e(H,"next page"));ne&&(Ge=Ze?a.createElement("li",{title:y?Ii:null,key:"prev",onClick:Fe,tabIndex:0,onKeyDown:Ci,className:M("".concat(t,"-jump-prev"),I({},"".concat(t,"-jump-prev-custom-icon"),!!be))},Ze):null,Qe=ke?a.createElement("li",{title:y?Oi:null,key:"next",onClick:Ue,tabIndex:0,onKeyDown:$i,className:M("".concat(t,"-jump-next"),I({},"".concat(t,"-jump-next-custom-icon"),!!H))},ke):null);var _e=Math.max(1,r-L),Re=Math.min(r+L,b);r-1<=L&&(Re=1+L*2),b-r<=L&&(_e=b-L*2);for(var de=_e;de<=Re;de+=1)$.push(a.createElement(pe,F({},se,{key:de,page:de,active:r===de})));if(r-1>=L*2&&r!==3&&($[0]=a.cloneElement($[0],{className:M("".concat(t,"-item-after-jump-prev"),$[0].props.className)}),$.unshift(Ge)),b-r>=L*2&&r!==b-2){var ei=$[$.length-1];$[$.length-1]=a.cloneElement(ei,{className:M("".concat(t,"-item-before-jump-next"),ei.props.className)}),$.push(Qe)}_e!==1&&$.unshift(a.createElement(pe,F({},se,{key:1,page:1}))),Re!==b&&$.push(a.createElement(pe,F({},se,{key:b,page:b})))}var Pe=yi(Pi);if(Pe){var De=!ye||!b;Pe=a.createElement("li",{title:y?u.prev_page:null,onClick:Xe,tabIndex:De?null:0,onKeyDown:fi,className:M("".concat(t,"-prev"),I({},"".concat(t,"-disabled"),De)),"aria-disabled":De},Pe)}var Ee=xi(Ei);if(Ee){var ge,He;D?(ge=!xe,He=ye?0:null):(ge=!xe||!b,He=ge?null:0),Ee=a.createElement("li",{title:y?u.next_page:null,onClick:Je,tabIndex:He,onKeyDown:Si,className:M("".concat(t,"-next"),I({},"".concat(t,"-disabled"),ge)),"aria-disabled":ge},Ee)}var Bi=M(t,O,I(I(I(I(I({},"".concat(t,"-start"),W==="start"),"".concat(t,"-center"),W==="center"),"".concat(t,"-end"),W==="end"),"".concat(t,"-simple"),D),"".concat(t,"-disabled"),X));return a.createElement("ul",F({className:Bi,style:g,ref:le},zi),Ni,Pe,D?Ye:$,Ee,a.createElement(rt,{locale:u,rootPrefixCls:t,disabled:X,selectPrefixCls:E,changeSize:bi,pageSize:S,pageSizeOptions:we,quickGo:pi?j:null,goButton:ue,showSizeChanger:Oe,sizeChangerRender:Be}))};const ct=e=>{const{componentCls:i}=e;return{[`${i}-disabled`]:{"&, &:hover":{cursor:"not-allowed",[`${i}-item-link`]:{color:e.colorTextDisabled,cursor:"not-allowed"}},"&:focus-visible":{cursor:"not-allowed",[`${i}-item-link`]:{color:e.colorTextDisabled,cursor:"not-allowed"}}},[`&${i}-disabled`]:{cursor:"not-allowed",[`${i}-item`]:{cursor:"not-allowed",backgroundColor:"transparent","&:hover, &:active":{backgroundColor:"transparent"},a:{color:e.colorTextDisabled,backgroundColor:"transparent",border:"none",cursor:"not-allowed"},"&-active":{borderColor:e.colorBorder,backgroundColor:e.itemActiveBgDisabled,"&:hover, &:active":{backgroundColor:e.itemActiveBgDisabled},a:{color:e.itemActiveColorDisabled}}},[`${i}-item-link`]:{color:e.colorTextDisabled,cursor:"not-allowed","&:hover, &:active":{backgroundColor:"transparent"},[`${i}-simple&`]:{backgroundColor:"transparent","&:hover, &:active":{backgroundColor:"transparent"}}},[`${i}-simple-pager`]:{color:e.colorTextDisabled},[`${i}-jump-prev, ${i}-jump-next`]:{[`${i}-item-link-icon`]:{opacity:0},[`${i}-item-ellipsis`]:{opacity:1}}},[`&${i}-simple`]:{[`${i}-prev, ${i}-next`]:{[`&${i}-disabled ${i}-item-link`]:{"&:hover, &:active":{backgroundColor:"transparent"}}}}}},st=e=>{const{componentCls:i}=e;return{[`&${i}-mini ${i}-total-text, &${i}-mini ${i}-simple-pager`]:{height:e.itemSizeSM,lineHeight:v(e.itemSizeSM)},[`&${i}-mini ${i}-item`]:{minWidth:e.itemSizeSM,height:e.itemSizeSM,margin:0,lineHeight:v(e.calc(e.itemSizeSM).sub(2).equal())},[`&${i}-mini ${i}-prev, &${i}-mini ${i}-next`]:{minWidth:e.itemSizeSM,height:e.itemSizeSM,margin:0,lineHeight:v(e.itemSizeSM)},[`&${i}-mini:not(${i}-disabled)`]:{[`${i}-prev, ${i}-next`]:{[`&:hover ${i}-item-link`]:{backgroundColor:e.colorBgTextHover},[`&:active ${i}-item-link`]:{backgroundColor:e.colorBgTextActive},[`&${i}-disabled:hover ${i}-item-link`]:{backgroundColor:"transparent"}}},[`
    &${i}-mini ${i}-prev ${i}-item-link,
    &${i}-mini ${i}-next ${i}-item-link
    `]:{backgroundColor:"transparent",borderColor:"transparent","&::after":{height:e.itemSizeSM,lineHeight:v(e.itemSizeSM)}},[`&${i}-mini ${i}-jump-prev, &${i}-mini ${i}-jump-next`]:{height:e.itemSizeSM,marginInlineEnd:0,lineHeight:v(e.itemSizeSM)},[`&${i}-mini ${i}-options`]:{marginInlineStart:e.paginationMiniOptionsMarginInlineStart,"&-size-changer":{top:e.miniOptionsSizeChangerTop},"&-quick-jumper":{height:e.itemSizeSM,lineHeight:v(e.itemSizeSM),input:Object.assign(Object.assign({},Qi(e)),{width:e.paginationMiniQuickJumperInputWidth,height:e.controlHeightSM})}}}},ut=e=>{const{componentCls:i}=e;return{[`
    &${i}-simple ${i}-prev,
    &${i}-simple ${i}-next
    `]:{height:e.itemSizeSM,lineHeight:v(e.itemSizeSM),verticalAlign:"top",[`${i}-item-link`]:{height:e.itemSizeSM,backgroundColor:"transparent",border:0,"&:hover":{backgroundColor:e.colorBgTextHover},"&:active":{backgroundColor:e.colorBgTextActive},"&::after":{height:e.itemSizeSM,lineHeight:v(e.itemSizeSM)}}},[`&${i}-simple ${i}-simple-pager`]:{display:"inline-block",height:e.itemSizeSM,marginInlineEnd:e.marginXS,input:{boxSizing:"border-box",height:"100%",padding:`0 ${v(e.paginationItemPaddingInline)}`,textAlign:"center",backgroundColor:e.itemInputBg,border:`${v(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadius,outline:"none",transition:`border-color ${e.motionDurationMid}`,color:"inherit","&:hover":{borderColor:e.colorPrimary},"&:focus":{borderColor:e.colorPrimaryHover,boxShadow:`${v(e.inputOutlineOffset)} 0 ${v(e.controlOutlineWidth)} ${e.controlOutline}`},"&[disabled]":{color:e.colorTextDisabled,backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,cursor:"not-allowed"}}}}},mt=e=>{const{componentCls:i}=e;return{[`${i}-jump-prev, ${i}-jump-next`]:{outline:0,[`${i}-item-container`]:{position:"relative",[`${i}-item-link-icon`]:{color:e.colorPrimary,fontSize:e.fontSizeSM,opacity:0,transition:`all ${e.motionDurationMid}`,"&-svg":{top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,margin:"auto"}},[`${i}-item-ellipsis`]:{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,display:"block",margin:"auto",color:e.colorTextDisabled,letterSpacing:e.paginationEllipsisLetterSpacing,textAlign:"center",textIndent:e.paginationEllipsisTextIndent,opacity:1,transition:`all ${e.motionDurationMid}`}},"&:hover":{[`${i}-item-link-icon`]:{opacity:1},[`${i}-item-ellipsis`]:{opacity:0}}},[`
    ${i}-prev,
    ${i}-jump-prev,
    ${i}-jump-next
    `]:{marginInlineEnd:e.marginXS},[`
    ${i}-prev,
    ${i}-next,
    ${i}-jump-prev,
    ${i}-jump-next
    `]:{display:"inline-block",minWidth:e.itemSize,height:e.itemSize,color:e.colorText,fontFamily:e.fontFamily,lineHeight:v(e.itemSize),textAlign:"center",verticalAlign:"middle",listStyle:"none",borderRadius:e.borderRadius,cursor:"pointer",transition:`all ${e.motionDurationMid}`},[`${i}-prev, ${i}-next`]:{outline:0,button:{color:e.colorText,cursor:"pointer",userSelect:"none"},[`${i}-item-link`]:{display:"block",width:"100%",height:"100%",padding:0,fontSize:e.fontSizeSM,textAlign:"center",backgroundColor:"transparent",border:`${v(e.lineWidth)} ${e.lineType} transparent`,borderRadius:e.borderRadius,outline:"none",transition:`all ${e.motionDurationMid}`},[`&:hover ${i}-item-link`]:{backgroundColor:e.colorBgTextHover},[`&:active ${i}-item-link`]:{backgroundColor:e.colorBgTextActive},[`&${i}-disabled:hover`]:{[`${i}-item-link`]:{backgroundColor:"transparent"}}},[`${i}-slash`]:{marginInlineEnd:e.paginationSlashMarginInlineEnd,marginInlineStart:e.paginationSlashMarginInlineStart},[`${i}-options`]:{display:"inline-block",marginInlineStart:e.margin,verticalAlign:"middle","&-size-changer":{display:"inline-block",width:"auto"},"&-quick-jumper":{display:"inline-block",height:e.controlHeight,marginInlineStart:e.marginXS,lineHeight:v(e.controlHeight),verticalAlign:"top",input:Object.assign(Object.assign(Object.assign({},Fi(e)),Ui(e,{borderColor:e.colorBorder,hoverBorderColor:e.colorPrimaryHover,activeBorderColor:e.colorPrimary,activeShadow:e.activeShadow})),{"&[disabled]":Object.assign({},Gi(e)),width:e.calc(e.controlHeightLG).mul(1.25).equal(),height:e.controlHeight,boxSizing:"border-box",margin:0,marginInlineStart:e.marginXS,marginInlineEnd:e.marginXS})}}}},dt=e=>{const{componentCls:i}=e;return{[`${i}-item`]:{display:"inline-block",minWidth:e.itemSize,height:e.itemSize,marginInlineEnd:e.marginXS,fontFamily:e.fontFamily,lineHeight:v(e.calc(e.itemSize).sub(2).equal()),textAlign:"center",verticalAlign:"middle",listStyle:"none",backgroundColor:e.itemBg,border:`${v(e.lineWidth)} ${e.lineType} transparent`,borderRadius:e.borderRadius,outline:0,cursor:"pointer",userSelect:"none",a:{display:"block",padding:`0 ${v(e.paginationItemPaddingInline)}`,color:e.colorText,"&:hover":{textDecoration:"none"}},[`&:not(${i}-item-active)`]:{"&:hover":{transition:`all ${e.motionDurationMid}`,backgroundColor:e.colorBgTextHover},"&:active":{backgroundColor:e.colorBgTextActive}},"&-active":{fontWeight:e.fontWeightStrong,backgroundColor:e.itemActiveBg,borderColor:e.colorPrimary,a:{color:e.colorPrimary},"&:hover":{borderColor:e.colorPrimaryHover},"&:hover a":{color:e.colorPrimaryHover}}}}},gt=e=>{const{componentCls:i}=e;return{[i]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Di(e)),{display:"flex","&-start":{justifyContent:"start"},"&-center":{justifyContent:"center"},"&-end":{justifyContent:"end"},"ul, ol":{margin:0,padding:0,listStyle:"none"},"&::after":{display:"block",clear:"both",height:0,overflow:"hidden",visibility:"hidden",content:'""'},[`${i}-total-text`]:{display:"inline-block",height:e.itemSize,marginInlineEnd:e.marginXS,lineHeight:v(e.calc(e.itemSize).sub(2).equal()),verticalAlign:"middle"}}),dt(e)),mt(e)),ut(e)),st(e)),ct(e)),{[`@media only screen and (max-width: ${e.screenLG}px)`]:{[`${i}-item`]:{"&-after-jump-prev, &-before-jump-next":{display:"none"}}},[`@media only screen and (max-width: ${e.screenSM}px)`]:{[`${i}-options`]:{display:"none"}}}),[`&${e.componentCls}-rtl`]:{direction:"rtl"}}},pt=e=>{const{componentCls:i}=e;return{[`${i}:not(${i}-disabled)`]:{[`${i}-item`]:Object.assign({},Hi(e)),[`${i}-jump-prev, ${i}-jump-next`]:{"&:focus-visible":Object.assign({[`${i}-item-link-icon`]:{opacity:1},[`${i}-item-ellipsis`]:{opacity:0}},ii(e))},[`${i}-prev, ${i}-next`]:{[`&:focus-visible ${i}-item-link`]:Object.assign({},ii(e))}}}},mi=e=>Object.assign({itemBg:e.colorBgContainer,itemSize:e.controlHeight,itemSizeSM:e.controlHeightSM,itemActiveBg:e.colorBgContainer,itemLinkBg:e.colorBgContainer,itemActiveColorDisabled:e.colorTextDisabled,itemActiveBgDisabled:e.controlItemBgActiveDisabled,itemInputBg:e.colorBgContainer,miniOptionsSizeChangerTop:0},Ji(e)),di=e=>Ri(e,{inputOutlineOffset:0,paginationMiniOptionsMarginInlineStart:e.calc(e.marginXXS).div(2).equal(),paginationMiniQuickJumperInputWidth:e.calc(e.controlHeightLG).mul(1.1).equal(),paginationItemPaddingInline:e.calc(e.marginXXS).mul(1.5).equal(),paginationEllipsisLetterSpacing:e.calc(e.marginXXS).div(2).equal(),paginationSlashMarginInlineStart:e.marginSM,paginationSlashMarginInlineEnd:e.marginSM,paginationEllipsisTextIndent:"0.13em"},Xi(e)),vt=_i("Pagination",e=>{const i=di(e);return[gt(i),pt(i)]},mi),ht=e=>{const{componentCls:i}=e;return{[`${i}${i}-bordered${i}-disabled:not(${i}-mini)`]:{"&, &:hover":{[`${i}-item-link`]:{borderColor:e.colorBorder}},"&:focus-visible":{[`${i}-item-link`]:{borderColor:e.colorBorder}},[`${i}-item, ${i}-item-link`]:{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,[`&:hover:not(${i}-item-active)`]:{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,a:{color:e.colorTextDisabled}},[`&${i}-item-active`]:{backgroundColor:e.itemActiveBgDisabled}},[`${i}-prev, ${i}-next`]:{"&:hover button":{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,color:e.colorTextDisabled},[`${i}-item-link`]:{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder}}},[`${i}${i}-bordered:not(${i}-mini)`]:{[`${i}-prev, ${i}-next`]:{"&:hover button":{borderColor:e.colorPrimaryHover,backgroundColor:e.itemBg},[`${i}-item-link`]:{backgroundColor:e.itemLinkBg,borderColor:e.colorBorder},[`&:hover ${i}-item-link`]:{borderColor:e.colorPrimary,backgroundColor:e.itemBg,color:e.colorPrimary},[`&${i}-disabled`]:{[`${i}-item-link`]:{borderColor:e.colorBorder,color:e.colorTextDisabled}}},[`${i}-item`]:{backgroundColor:e.itemBg,border:`${v(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,[`&:hover:not(${i}-item-active)`]:{borderColor:e.colorPrimary,backgroundColor:e.itemBg,a:{color:e.colorPrimary}},"&-active":{borderColor:e.colorPrimary}}}}},bt=Ai(["Pagination","bordered"],e=>{const i=di(e);return[ht(i)]},mi);function si(e){return l.useMemo(()=>typeof e=="boolean"?[e,{}]:e&&typeof e=="object"?[!0,e]:[void 0,void 0],[e])}var ft=function(e,i){var m={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&i.indexOf(t)<0&&(m[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var d=0,t=Object.getOwnPropertySymbols(e);d<t.length;d++)i.indexOf(t[d])<0&&Object.prototype.propertyIsEnumerable.call(e,t[d])&&(m[t[d]]=e[t[d]]);return m};const St=e=>{const{align:i,prefixCls:m,selectPrefixCls:t,className:d,rootClassName:E,style:O,size:x,locale:f,responsive:N,showSizeChanger:C,selectComponentClass:c,pageSizeOptions:B}=e,T=ft(e,["align","prefixCls","selectPrefixCls","className","rootClassName","style","size","locale","responsive","showSizeChanger","selectComponentClass","pageSizeOptions"]),{xs:V}=Wi(N),[,w]=Li(),{getPrefixCls:h,direction:P,showSizeChanger:W,className:k,style:ne}=Ki("pagination"),s=h("pagination",m),[z,ee,y]=vt(s),q=qi(x),_=q==="small"||!!(V&&!q&&N),[U]=Zi("Pagination",Vi),u=Object.assign(Object.assign({},U),f),[g,R]=si(C),[ae,X]=si(W),D=g??ae,re=R??X,ve=c||Yi,Oe=l.useMemo(()=>B?B.map(H=>Number(H)):void 0,[B]),Be=H=>{var Q;const{disabled:oe,size:le,onSizeChange:Y,"aria-label":fe,className:S,options:Se}=H,{className:Me,onChange:ie}=re||{},r=(Q=Se.find(A=>String(A.value)===String(le)))===null||Q===void 0?void 0:Q.value;return l.createElement(ve,Object.assign({disabled:oe,showSearch:!0,popupMatchSelectWidth:!1,getPopupContainer:A=>A.parentNode,"aria-label":fe,options:Se},re,{value:r,onChange:(A,Te)=>{Y==null||Y(A),ie==null||ie(A,Te)},size:_?"small":"middle",className:M(S,Me)}))},we=l.useMemo(()=>{const H=l.createElement("span",{className:`${s}-item-ellipsis`},"•••"),Q=l.createElement("button",{className:`${s}-item-link`,type:"button",tabIndex:-1},P==="rtl"?l.createElement(ni,null):l.createElement(ti,null)),oe=l.createElement("button",{className:`${s}-item-link`,type:"button",tabIndex:-1},P==="rtl"?l.createElement(ti,null):l.createElement(ni,null)),le=l.createElement("a",{className:`${s}-item-link`},l.createElement("div",{className:`${s}-item-container`},P==="rtl"?l.createElement(oi,{className:`${s}-item-link-icon`}):l.createElement(ri,{className:`${s}-item-link-icon`}),H)),Y=l.createElement("a",{className:`${s}-item-link`},l.createElement("div",{className:`${s}-item-container`},P==="rtl"?l.createElement(ri,{className:`${s}-item-link-icon`}):l.createElement(oi,{className:`${s}-item-link-icon`}),H));return{prevIcon:Q,nextIcon:oe,jumpPrevIcon:le,jumpNextIcon:Y}},[P,s]),he=h("select",t),G=M({[`${s}-${i}`]:!!i,[`${s}-mini`]:_,[`${s}-rtl`]:P==="rtl",[`${s}-bordered`]:w.wireframe},k,d,E,ee,y),be=Object.assign(Object.assign({},ne),O);return z(l.createElement(l.Fragment,null,w.wireframe&&l.createElement(bt,{prefixCls:s}),l.createElement(lt,Object.assign({},we,T,{style:be,prefixCls:s,selectPrefixCls:he,className:G,locale:u,pageSizeOptions:Oe,showSizeChanger:D,sizeChangerRender:Be}))))},Pt=({pagination:e,handlePageChange:i,isLoading:m,itemName:t="items",pageSizeOptions:d=["10","20","50"],showSizeChanger:E=!0,showQuickJumper:O=!0,className:x="",align:f=""})=>{if(m||!e||e.totalPages<=1)return null;const N={center:"justify-content-center",start:"justify-content-start",end:"justify-content-end"}[f]||"justify-content-end";return Ae.jsx("div",{className:`row my-5 ${x}`,children:Ae.jsx("div",{className:`col-12 d-flex ${N}`,children:Ae.jsx(St,{current:e.currentPage,pageSize:e.pageLimit,total:e.totalCount,onChange:i,onShowSizeChange:i,showSizeChanger:E,showTotal:(C,c)=>`Showing ${c[0]}-${c[1]} of ${C} ${t}`,pageSizeOptions:d})})})};export{Pt as R};
