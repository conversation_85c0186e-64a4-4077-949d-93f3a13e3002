import React from "react";
import { Avatar } from "antd";

const getMessageIcon = (status) => {
  switch (status) {
    case "sent":
      return <img src="/assets/img/send-icon.png" alt="sent" />;
    case "viewed":
      return <img src="/assets/img/viewed-msg.png" alt="viewed" />;
    case "received":
      return <p className="msg-receive"></p>;
    default:
      return null;
  }
};

const ChatUserList = ({
  name,
  message,
  isTyping,
  msgTime,
  msgStatus,
  onlineStatus,
  avatar,
}) => {
  return (
    <div className="chat-user">
      <div className="chat-user-img">
        <Avatar src={avatar} size={40} />
      </div>

      <div className="chat-user-msg-area ms-3 d-flex justify-content-between w-100">
        <div>
          <p className="font-16 color-black">{name}</p>
          <p className="color-light font-14">
            {isTyping ? <em>Typing...</em> : message}
          </p>
        </div>

        <div className="text-end">
          <p className="font-12 color-gray">{msgTime}</p>
          {/* <div className="text-end">{getMessageIcon(msgStatus)}</div> */}
        </div>
      </div>
    </div>
  );
};

export default ChatUserList;
