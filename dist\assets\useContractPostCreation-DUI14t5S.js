import{r,W as N,v as B,u as $,R as E,j as e,X as K,q as T,h as z}from"./index-Dklazue-.js";import{F as O}from"./flatbutton-B_tUS4QM.js";import{B as W,I as H}from"./index-BUt89ETK.js";import{u as _}from"./useStartupData-QD8kuEYy.js";import{u as A}from"./index-vmMMvWhJ.js";import{h as C}from"./trialUtils-PQN1eD5v.js";import{I as X}from"./index-BNeAK5sW.js";import{B as Y}from"./button-DNhBCuue.js";import{U as q}from"./index-Vj938NLd.js";import{u as M}from"./useMutation-BrUrPIzr.js";var D={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 632H136v-39.9l138.5-164.3 150.1 178L658.1 489 888 761.6V792zm0-129.8L664.2 396.8c-3.2-3.8-9-3.8-12.2 0L424.6 666.4l-144-170.7c-3.2-3.8-9-3.8-12.2 0L136 652.7V232h752v430.2zM304 456a88 88 0 100-176 88 88 0 000 176zm0-116c15.5 0 28 12.5 28 28s-12.5 28-28 28-28-12.5-28-28 12.5-28 28-28z"}}]},name:"picture",theme:"outlined"},G=function(p,l){return r.createElement(N,B({},p,{ref:l,icon:D}))},J=r.forwardRef(G),Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 302.3L784 376V224c0-35.3-28.7-64-64-64H128c-35.3 0-64 28.7-64 64v576c0 35.3 28.7 64 64 64h592c35.3 0 64-28.7 64-64V648l128 73.7c21.3 12.3 48-3.1 48-27.6V330c0-24.6-26.7-40-48-27.7zM712 792H136V232h576v560zm176-167l-104-59.8V458.9L888 399v226zM208 360h112c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H208c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}}]},name:"video-camera",theme:"outlined"},ee=function(p,l){return r.createElement(N,B({},p,{ref:l,icon:Z}))},te=r.forwardRef(ee);const{TextArea:se}=H,fe=({placeholder:a="What's on your mind?",postCreationHook:p,onCancelEdit:l})=>{const{showAlert:c}=A(),{data:f,isLoading:d}=_(),t=f==null?void 0:f.data,n=$(),{content:x,setContent:u,selectedFile:h,filePreview:m,fileType:g,questionId:w,setQuestionId:R,isCreatingPost:i,isFormValid:I,isEditMode:y,handleImageUpload:j,handleVideoUpload:b,removeFile:L,handleSubmit:U}=p||{},k=E.useMemo(()=>t!=null&&t.postQuestions?t.postQuestions.map(o=>({value:o.id,label:o.name||o.title||o.question})):[],[t==null?void 0:t.postQuestions]),V=async()=>{await C(c,n)},F=async o=>!await C(c,n)&&j?j({file:o}):!1,s=async o=>!await C(c,n)&&b?b({file:o}):!1,v=async()=>{!await C(c,n)&&U&&U()};return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"mb-3",children:e.jsx(W,{name:"question_id",type:"select",placeholder:"Question",value:w,handlechange:R,options:k,loading:d,disabled:i})}),e.jsx("div",{style:{maxWidth:"100%",border:"1px solid #ddd",borderRadius:"10px"},className:"mt-4",children:e.jsxs("div",{style:{backgroundColor:"#fff",padding:"10px",borderRadius:"10px"},children:[e.jsx(se,{value:x,onChange:o=>u(o.target.value),onClick:V,placeholder:a,autoSize:{minRows:3,maxRows:5},style:{border:"none",resize:"none",marginBottom:"10px",background:"transparent"},disabled:i}),m&&e.jsxs("div",{className:"mb-3 position-relative",style:{maxWidth:"200px"},children:[g==="image"?e.jsx(X,{width:200,height:150,src:m,style:{objectFit:"cover",borderRadius:"8px"},preview:!1}):e.jsxs("video",{width:200,height:150,controls:!0,style:{borderRadius:"8px",objectFit:"cover"},children:[e.jsx("source",{src:m,type:h==null?void 0:h.type}),"Your browser does not support the video tag."]}),e.jsx(Y,{type:"text",danger:!0,icon:e.jsx(K,{}),size:"small",onClick:L,style:{position:"absolute",top:"5px",right:"5px",backgroundColor:"rgba(0,0,0,0.5)",color:"white",border:"none"},disabled:i})]}),e.jsxs("div",{className:"d-flex justify-content-between align-items-center icon-color",children:[e.jsxs("div",{className:"d-flex gap-3",children:[e.jsx(q,{showUploadList:!1,beforeUpload:F,accept:"image/*",disabled:i,children:e.jsx(J,{style:{cursor:i?"not-allowed":"pointer",fontSize:"24px",color:g==="image"?"#1890ff":void 0},title:"Upload Image"})}),e.jsx(q,{showUploadList:!1,beforeUpload:s,accept:"video/*",disabled:i,children:e.jsx(te,{style:{cursor:i?"not-allowed":"pointer",fontSize:"24px",color:g==="video"?"#1890ff":void 0},title:"Upload Video"})})]}),e.jsxs("div",{className:"d-flex gap-2",children:[y&&l&&e.jsx(O,{type:"default",title:"Cancel",className:"px-4 post-btn px-5",onClick:l,disabled:i}),e.jsx(O,{type:"primary",title:i?y?"Updating...":"Posting...":y?"Update":"Post",className:"post-btn px-5",onClick:v,disabled:!I||i,icon:i?e.jsx(T,{}):void 0})]})]})]})})]})},he=(a=null,p=null)=>{const[l,c]=r.useState(""),[f,d]=r.useState(null),[t,n]=r.useState(null),[x,u]=r.useState(null),[h,m]=r.useState(null),g=!!a,{mutate:w,isPending:R}=M("addPost",{useFormData:!0,showSuccessNotification:!1,invalidateQueries:[{queryKey:["postItem"],type:"paginated"},{queryKey:["postItem"],type:"all"},{queryKey:["getComments"],type:"all"}],onSuccess:()=>{c(""),d(null),t&&URL.revokeObjectURL(t),n(null),u(null),m(null)}}),{mutate:i,isPending:I}=M("updatePost",{useFormData:!0,showSuccessNotification:!0,invalidateQueries:[{queryKey:["postItem"],type:"paginated"},{queryKey:["postItem"],type:"all"},{queryKey:["getComments"],type:"all"}],onSuccess:()=>{c(""),d(null),t&&URL.revokeObjectURL(t),n(null),u(null),m(null),p&&p()}}),y=R||I,j=()=>{c(""),d(null),t&&URL.revokeObjectURL(t),n(null),u(null),m(null)},b=(s,v)=>{const o=v==="image"&&s.type.startsWith("image/"),S=v==="video"&&s.type.startsWith("video/");if(!o&&!S)return z.error(`Please select a valid ${v} file`),!1;const P=5;if(!(s.size/1024/1024<P))return z.error(`${v==="image"?"Image":"Video"} must be smaller than ${P}MB`),!1;t&&URL.revokeObjectURL(t),d(s),u(v);const Q=URL.createObjectURL(s);return n(Q),!1},L=({file:s})=>b(s,"image"),U=({file:s})=>b(s,"video"),k=()=>{t&&URL.revokeObjectURL(t),d(null),n(null),u(null)},V=()=>{if(!l.trim()){z.error("Please enter some content for your post");return}const s={content:l.trim(),is_anonymous:!0};h&&(s.question_id=h),f&&(x==="image"?s.image=f:x==="video"&&(s.video=f)),g?i({slug:a.id,data:s}):w(s)};r.useEffect(()=>{a&&(c(a.content||""),m(a.question_id||null),a.image?(u("image"),n(a.image),d(null)):a.video&&(u("video"),n(a.video),d(null)))},[a]),r.useEffect(()=>()=>{t&&URL.revokeObjectURL(t)},[t]);const F=l.trim().length>0;return{content:l,selectedFile:f,filePreview:t,fileType:x,questionId:h,isCreatingPost:y,isFormValid:F,isEditMode:g,setContent:c,setQuestionId:m,handleImageUpload:L,handleVideoUpload:U,removeFile:k,handleSubmit:V,resetForm:j}};export{fe as C,he as u};
