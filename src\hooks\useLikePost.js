import { useState, useCallback } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import apiClient from "@/services/apiClient";

export const useLikePost = (
  postId,
  initialLiked = false,
  initialLikesCount = 0
) => {
  const [isLiked, setIsLiked] = useState(initialLiked);
  const [likesCount, setLikesCount] = useState(initialLikesCount);
  const queryClient = useQueryClient();

  // Helper function to update post data across all cache entries
  const updatePostInCache = useCallback((liked) => {
    const likeDelta = liked ? 1 : -1;
    
    // Update all postItem queries (paginated lists, single posts, etc.)
    queryClient.setQueriesData(
      { queryKey: ["postItem"] },
      (oldData) => {
        if (!oldData) return oldData;

        // Handle paginated data structure
        if (oldData.data && Array.isArray(oldData.data)) {
          return {
            ...oldData,
            data: oldData.data.map(post => 
              post.id === postId || post.id === parseInt(postId)
                ? {
                    ...post,
                    is_liked: liked,
                    likes_count: Math.max(0, (post.likes_count || 0) + likeDelta)
                  }
                : post
            )
          };
        }

        // Handle single post data structure
        if (oldData.data && (oldData.data.id === postId || oldData.data.id === parseInt(postId))) {
          return {
            ...oldData,
            data: {
              ...oldData.data,
              is_liked: liked,
              likes_count: Math.max(0, (oldData.data.likes_count || 0) + likeDelta)
            }
          };
        }

        // Handle direct array structure
        if (Array.isArray(oldData)) {
          return oldData.map(post => 
            post.id === postId || post.id === parseInt(postId)
              ? {
                  ...post,
                  is_liked: liked,
                  likes_count: Math.max(0, (post.likes_count || 0) + likeDelta)
                }
              : post
          );
        }

        // Handle direct post object
        if (oldData.id === postId || oldData.id === parseInt(postId)) {
          return {
            ...oldData,
            is_liked: liked,
            likes_count: Math.max(0, (oldData.likes_count || 0) + likeDelta)
          };
        }

        return oldData;
      }
    );
  }, [postId, queryClient]);

  const { mutate: toggleLike, isPending: isToggling } = useMutation({
    mutationFn: async (liked) => {
      const payload = {
        type: "post",
        value: liked,
      };

      const response = await apiClient.request("likePost", {
        slug: postId,
        data: payload,
        useFormData: false,
        showSuccessNotification: false,
      });

      return response;
    },
    onMutate: async (liked) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ["postItem"] });

      // Optimistically update local state
      setIsLiked(liked);
      setLikesCount((prev) => (liked ? prev + 1 : Math.max(0, prev - 1)));

      // Optimistically update all cache entries
      updatePostInCache(liked);

      // Return context for rollback
      return { previousLiked: !liked };
    },
    onError: (error, liked, context) => {
      // Revert local state
      setIsLiked(context?.previousLiked ?? !liked);
      setLikesCount((prev) => (liked ? Math.max(0, prev - 1) : prev + 1));

      // Revert cache updates
      updatePostInCache(context?.previousLiked ?? !liked);
    },
    onSettled: () => {
      // Invalidate queries as fallback to ensure consistency
      queryClient.invalidateQueries({
        queryKey: ["postItem"],
        exact: false,
      });
    },
  });

  const handleLikeToggle = useCallback(() => {
    const newLikedState = !isLiked;
    toggleLike(newLikedState);
  }, [isLiked, toggleLike]);

  return {
    isLiked,
    likesCount,
    isToggling,
    handleLikeToggle,
  };
};

export default useLikePost;