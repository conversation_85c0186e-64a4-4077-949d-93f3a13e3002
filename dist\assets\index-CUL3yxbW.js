import{u as x,j as a,i as f}from"./index-Dklazue-.js";import{I as g}from"./index-vmMMvWhJ.js";import{R as j}from"./ReusablePagination-B7_yMXG_.js";import{a as y}from"./useQuery-C3n1GVcJ.js";import{E as v}from"./EmptyState-YbiQZMha.js";import{S as h}from"./Skeleton--lTrbnrp.js";import"./useMutation-BrUrPIzr.js";import"./button-DNhBCuue.js";import"./index-Cj6uPc4c.js";import"./index-CjGjc6T5.js";import"./index-CHbHgJvR.js";import"./useLocale-BNhrTARD.js";const N=({avatar:o,message:s,time:t,notification:i})=>{const e=x(),r=()=>{const{recordId:c,redirectTo:m,customData:n}=i;m=="post"&&e(`/sphare-it/${c}`),m=="post"&&(n==null?void 0:n.is_anonymous)==!0&&e(`/contract/${c}`)};return a.jsxs("div",{className:"not-box d-flex align-items-center",onClick:r,style:{cursor:"pointer"},children:[a.jsx("div",{className:"notifoication-avatar",children:a.jsx("img",{src:o,alt:"avatar"})}),a.jsxs("div",{className:"ms-2",children:[a.jsx("p",{children:s}),a.jsx("p",{children:t})]})]})},b=o=>{const s=f(),t=s.subtract(1,"day"),i={};return o.forEach(e=>{const r=f(e.createdAt);let c=r.isSame(s,"day")?"Today":r.isSame(t,"day")?"Yesterday":r.format("MMM D, YYYY");i[c]||(i[c]=[]),i[c].push(e)}),i},Y=o=>{const s=f(),t=f(o),i=s.diff(t,"minute");if(i<60)return`${i} mins ago`;const e=s.diff(t,"hour");return e<24?`${e} hours ago`:s.diff(t,"day")===1?"Yesterday":t.format("MMM D, YYYY")},C=()=>{const{data:o,isLoading:s,setPage:t,setPageSize:i,pagination:e}=y("getNotification",{initialPage:1,initialPageSize:10}),r=(o==null?void 0:o.data)||[],c=b(r),m=(n,l)=>{t(n),i(l)};return a.jsx("div",{children:a.jsx(g,{children:a.jsx("div",{className:"container-fluid",children:a.jsxs("div",{className:"row",children:[a.jsx("div",{className:"col-12 mt-4",children:a.jsx("p",{className:"font-36 color-black font-600 ",children:"Notifications"})}),a.jsx("div",{className:"col-12 mt-4",children:s?a.jsx(a.Fragment,{children:[...Array(5)].map((n,l)=>a.jsxs("div",{className:"mb-4",children:[a.jsx(h.Avatar,{active:!0,size:40,shape:"circle"}),a.jsx(h,{active:!0,title:!1,paragraph:{rows:2,width:[200,120]},className:"ms-2 d-inline-block align-middle",style:{width:250}})]},l))}):r.length===0?a.jsx("div",{className:"text-center py-5",children:a.jsx(v,{})}):Object.entries(c).map(([n,l])=>a.jsxs("div",{className:"mb-4",children:[a.jsx("div",{className:"mb-4 font-16 font-600 color-grey",children:n}),l.map((d,u)=>{var p;return a.jsx(N,{avatar:(p=d==null?void 0:d.actor)==null?void 0:p.image_url,message:d.body,time:Y(d.created_at),notification:d},d.id||u)})]},n))}),a.jsx("div",{className:"col-12",children:a.jsx(j,{pagination:e,handlePageChange:m,isLoading:s,itemName:"notifications",align:"end"})})]})})})})};export{C as default};
