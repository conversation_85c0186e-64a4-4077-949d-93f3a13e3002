import React from "react";
import { Skeleton } from "antd";
import ChatUserList from "@/components/shared/list/chatuserlist";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";

dayjs.extend(relativeTime);

const ChatList = ({ 
  rooms, 
  selectedRoom, 
  onSelectRoom, 
  loading, 
  connected
}) => {
  // Show loading skeleton
  if (loading) {
    return (
      <div style={{ padding: "10px 0" }}>
        {[...Array(5)].map((_, index) => (
          <div key={index} className="chat-user" style={{ marginBottom: "15px" }}>
            <div className="d-flex align-items-center">
              <Skeleton.Avatar size={50} />
              <div className="ms-3 flex-grow-1">
                <Skeleton.Input style={{ width: 120, height: 16, marginBottom: 8 }} active />
                <Skeleton.Input style={{ width: 200, height: 14 }} active />
              </div>
              <div className="text-end">
                <Skeleton.Input style={{ width: 60, height: 12, marginBottom: 8 }} active />
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Show no data message when not connected or no rooms
  if (!connected) {
    return (
      <div className="text-center py-5">
        <p className="color-light">Connecting to chat server...</p>
      </div>
    );
  }

  if (rooms.length === 0) {
    return (
      <div className="text-center py-5">
        <p className="color-light">No conversations found</p>
        <p className="color-light font-12">Start a new conversation to see it here</p>
      </div>
    );
  }

  // Format last message time
  const formatMessageTime = (timestamp) => {
    if (!timestamp) return "";
    
    const messageTime = dayjs(timestamp);
    const now = dayjs();
    const diffInHours = now.diff(messageTime, 'hour');
    
    if (diffInHours < 1) {
      const diffInMinutes = now.diff(messageTime, 'minute');
      return diffInMinutes < 1 ? "Now" : `${diffInMinutes} min ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours}hr ago`;
    } else {
      return messageTime.format('MM/DD/YYYY');
    }
  };

  return (
    <>
      {rooms.map((room, index) => {
        const isSelected = selectedRoom?.id === room.id;
        return (
          <div 
            key={room.id || `room-${index}`}
            onClick={() => onSelectRoom(room)}
            style={{ 
              cursor: 'pointer',
              backgroundColor: isSelected ? '#f5f5f5' : 'transparent',
              borderRadius: '8px',
              padding: '5px'
            }}
          >
            <ChatUserList
              name={room.room_title || `Room ${room.id}`}
              message={room.last_message?.message || "No messages yet"}
              isTyping={false}
              msgTime={formatMessageTime(room.last_message?.created_at)}
              msgStatus="sent"
              onlineStatus="online"
              avatar={room.room_image || "/assets/img/avatar-1.png"}
            />
          </div>
        );
      })}
    </>
  );
};

export default ChatList;