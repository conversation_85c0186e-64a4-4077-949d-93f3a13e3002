.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 30px;
  background-color: #fff;
  border-bottom: 1px solid #F0F0F0;
}

.header-left {
  display: flex;
  align-items: center;
}

.app-header .logo {
  height: 60px;
  margin-right: 50px;
}

.nav-links a {
  margin: 0 10px;
  color: #333;
  text-decoration: none;
  font-weight: 500;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.user-img {
  height: 32px;
  width: 32px;
  border-radius: 50%;
  margin-right: 8px;
}

.user-name {
  font-weight: 500;
}

.icons .icon {
  font-size: 18px;
  margin: 0 8px;
  cursor: pointer;
}

.nav-link {
  margin: 0 !important;
  color: #333;
  text-decoration: none;
  font-weight: 500;
  transition: 0.2s;
  transition: max-height 0.4s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  border-radius: 4px;
  height: 40px;
  padding: 0 20px;
  margin: 0 10px !important;
}

.nav-link:hover {
  background: #3883E2;
  color: #fff !important;

}

.nav-link.active {
  background: #3883E2;
  color: #fff !important;
  padding: 0 20px;
}

.mobile-toggle {
  display: none;
  font-size: 22px;
  cursor: pointer;
}

header .ant-avatar>img {
  width: auto;
  height: auto;
}

header .ant-avatar-square {
  background-color: #fafafa;
}

header .anticon svg {
  color: #000;
}

.anticon svg {
  color: #8C8C8C;
}
.ant-notification .ant-notification-notice-icon-error svg {
  color: #ff0000;
}
.ant-notification .ant-notification-notice-icon-success svg{
  color: #52c41a;
}
.icon-color .anticon svg {
  color: #3883e2;
}

.active-dropdown-link p {
  color: #3883E2 !important;
}

.active-dropdown-link img {
  filter: brightness(0) saturate(100%) invert(29%) sepia(89%) saturate(1546%) hue-rotate(196deg) brightness(94%) contrast(89%);
}

.ant-layout-header {
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 999;
}

.ant-layout-content {
  margin-top: 60px;
}

.ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-submenu-title {
  display: flex;
  margin: 0;
  padding: 7px 12px;
  color: rgba(0, 0, 0, 0.88);
  font-weight: normal;
  font-size: 14px;
  line-height: 1.5714285714285714;
  cursor: pointer;
  transition: all 0.2s;
  border-radius: 4px;
}
.swal2-popup.swal2-modal.swal2-icon-warning.swal2-show {
    border-radius: 16px;
}

button.swal2-confirm::before {
  content: none !important;
}
button.swal2-cancel::before{
  content: none !important;
}
@media (max-width: 768px) {


  .nav-links {
    position: absolute;
    top: 60px;
    left: 0;
    background: #fff;
    width: 100%;
    flex-direction: column;
    padding: 10px 20px;
    display: none !important;
    transition: max-height 0.4s ease-in-out;
  }

  .nav-links.open {
    display: block !important;
    transition: max-height 0.4s ease-in-out;
  }


  .mobile-toggle {
    display: block;
  }

  .nav-link {
    padding: 10px 0;
  }
}