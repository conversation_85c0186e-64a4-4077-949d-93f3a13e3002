import{r as o,W as me,v as ue,h as L,u as pe,j as e,i as B,k as he}from"./index-Dklazue-.js";import{u as xe,b as Y,R as fe,a as ge,c as ve}from"./useLikePost-BsM_zOKm.js";import{u as Z}from"./useMutation-BrUrPIzr.js";import{D as _}from"./index-vmMMvWhJ.js";import{R as je}from"./EditOutlined-DxjsqgjX.js";import{R as ke}from"./DeleteOutlined-BjE_e6LE.js";var ye={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M752 664c-28.5 0-54.8 10-75.4 26.7L469.4 540.8a160.68 160.68 0 000-57.6l207.2-149.9C697.2 350 723.5 360 752 360c66.2 0 120-53.8 120-120s-53.8-120-120-120-120 53.8-120 120c0 11.6 1.6 22.7 4.7 33.3L439.9 415.8C410.7 377.1 364.3 352 312 352c-88.4 0-160 71.6-160 160s71.6 160 160 160c52.3 0 98.7-25.1 127.9-63.8l196.8 142.5c-3.1 10.6-4.7 21.8-4.7 33.3 0 66.2 53.8 120 120 120s120-53.8 120-120-53.8-120-120-120zm0-476c28.7 0 52 23.3 52 52s-23.3 52-52 52-52-23.3-52-52 23.3-52 52-52zM312 600c-48.5 0-88-39.5-88-88s39.5-88 88-88 88 39.5 88 88-39.5 88-88 88zm440 236c-28.7 0-52-23.3-52-52s23.3-52 52-52 52 23.3 52 52-23.3 52-52 52z"}}]},name:"share-alt",theme:"outlined"},be=function(h,m){return o.createElement(me,ue({},h,{ref:m,icon:ye}))},Ce=o.forwardRef(be);const Ne=(n=null,h=null)=>{const[m,j]=o.useState(""),[x,g]=o.useState(null),[a,v]=o.useState(null),[k,l]=o.useState(null),b=!!n,{mutate:C,isPending:I}=Z("addPost",{useFormData:!0,showSuccessNotification:!1,invalidateQueries:[{queryKey:["postItem"],type:"paginated"},{queryKey:["postItem"],type:"all"},{queryKey:["getComments"],type:"all"}],onSuccess:i=>{S()}}),{mutate:N,isPending:z}=Z("updatePost",{useFormData:!0,showSuccessNotification:!0,invalidateQueries:[{queryKey:["postItem"],type:"paginated"},{queryKey:["postItem"],type:"all"},{queryKey:["getComments"],type:"all"}],onSuccess:i=>{S(),h&&h()}}),$=I||z,S=o.useCallback(()=>{j(""),g(null),a&&URL.revokeObjectURL(a),v(null),l(null)},[a]),y=o.useCallback((i,u)=>{const V=u==="image"&&i.type.startsWith("image/"),r=u==="video"&&i.type.startsWith("video/");if(!V&&!r)return L.error(`Please select a valid ${u} file`),!1;const f=5;if(!(i.size/1024/1024<f))return L.error(`${u==="image"?"Image":"Video"} must be smaller than ${f}MB`),!1;a&&URL.revokeObjectURL(a),g(i),l(u);const s=URL.createObjectURL(i);return v(s),!1},[a]),F=o.useCallback(({file:i})=>y(i,"image"),[y]),K=o.useCallback(({file:i})=>y(i,"video"),[y]),R=o.useCallback(()=>{a&&URL.revokeObjectURL(a),g(null),v(null),l(null)},[a]),w=o.useCallback(()=>{if(!m.trim()){L.error("Please enter some content for your post");return}const i={content:m.trim()};x&&(k==="image"?i.image=x:k==="video"&&(i.video=x)),b?N({slug:n.id,data:i}):C(i)},[m,x,k,b,n,C,N]);o.useEffect(()=>{n&&(j(n.content||""),n.image?(l("image"),v(n.image),g(null)):n.video&&(l("video"),v(n.video),g(null)))},[n]),o.useEffect(()=>()=>{a&&URL.revokeObjectURL(a)},[a]);const T=m.trim().length>0;return{content:m,selectedFile:x,filePreview:a,fileType:k,isCreatingPost:$,isFormValid:T,isEditMode:b,setContent:j,handleImageUpload:F,handleVideoUpload:K,removeFile:R,handleSubmit:w,resetForm:S,handleSharePost:i=>{if(!i){L.error("Post ID is missing");return}C({original_post_id:i})}}};B.extend(he);const ze=({id:n,user:h,created_at:m,content:j,image:x,video:g,likes_count:a=0,comments_count:v=0,reposts_count:k=0,original_post:l,time:b,question:C,body:I,likes:N,comments:z,shares:$,reactions:S,onClick:y,img:F,type:K,onEdit:R,onDelete:w,showActions:T=!1,is_liked:U=!1,user_id:i})=>{const u=pe(),{handleSharePost:V}=Ne(),r=!!l,f=r?l:{content:j,image:x,video:g},c=r?h:null,s=r?l.user:h,p=s,A=j||C||I,W=x||F,ee=v??z??0,se=k??$??0,te=n,ie=r?l.is_liked:U,ne=r?a:a??N??0,{isLiked:E,likesCount:ae,isToggling:Q,handleLikeToggle:le}=xe(te,ie,ne),M=((s==null?void 0:s.id)||(s==null?void 0:s.user_id))===window.user.id,q=(t,d)=>{var X;const P=t?B(t).fromNow():b||"Just now",H=(d==null?void 0:d.state)||((X=d==null?void 0:d.location)==null?void 0:X.state)||"";return H?`${H} · ${P}`:P},D=[];M&&D.push({key:"edit",icon:e.jsx(je,{}),label:"Edit"},{key:"delete",icon:e.jsx(ke,{}),label:"Delete"});const J=t=>{var d;if((d=t.domEvent)==null||d.stopPropagation(),t.key==="edit"){const P=r?l.id:n;R&&R(P)}else t.key==="delete"&&w&&w(n)},O=t=>{t.stopPropagation()},ce=t=>{t.stopPropagation(),le()},oe=t=>{t.stopPropagation(),V(n)},re=t=>{t.stopPropagation(),u(`/agent/detail/${c==null?void 0:c.id}/posts}`)},G=(t,d)=>{t.stopPropagation(),u(`/agent/detail/${d}/posts}`)},de=t=>{t.stopPropagation(),u(`/sphare-it/${l.id}`)};return e.jsxs("div",{className:"post-card sphere-card p-3 rounded border bg-white d-flex flex-column ",onClick:y,children:[r?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"d-flex align-items-start mb-2 pb-2 border-bottom",children:e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("img",{src:c==null?void 0:c.image_url,alt:c==null?void 0:c.name,className:"rounded-circle",width:40,height:40,onClick:re,style:{cursor:"pointer"}}),e.jsxs("div",{className:"ms-2",children:[e.jsxs("h6",{className:"mb-0",children:[c==null?void 0:c.name,"  ",e.jsx("span",{className:"color-light font-14",children:"Repost this"})]}),e.jsx("small",{className:"text-muted",children:q(m,c)})]})]})}),e.jsxs("div",{className:"d-flex justify-content-between align-items-center py-2",children:[e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("img",{src:s==null?void 0:s.image_url,alt:s==null?void 0:s.name,className:"rounded-circle",width:40,height:40,onClick:t=>G(t,s==null?void 0:s.id),style:{cursor:"pointer"}}),e.jsxs("div",{className:"ms-2",onClick:de,style:{cursor:"pointer"},children:[e.jsx("h6",{className:"mb-0",children:s==null?void 0:s.name}),e.jsx("small",{className:"text-muted",children:q(l==null?void 0:l.created_at,s)})]})]}),M&&e.jsx(_,{menu:{items:D,onClick:J},trigger:["click"],placement:"bottomRight",children:e.jsx(Y,{style:{fontSize:"20px",cursor:"pointer",padding:"4px",borderRadius:"50%"},onClick:O})})]})]}):e.jsxs("div",{className:"d-flex justify-content-between align-items-center pb-2 border-bottom",children:[e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("img",{src:p.image_url,alt:"avatar",className:"rounded-circle",width:40,height:40,onClick:t=>G(t,(p==null?void 0:p.id)||(p==null?void 0:p.user_id)),style:{cursor:"pointer"}}),e.jsxs("div",{className:"ms-2",children:[e.jsx("h6",{className:"mb-0",children:p.name}),e.jsx("small",{className:"text-muted",children:q(m,p)})]})]}),M&&e.jsx(_,{menu:{items:D,onClick:J},trigger:["click"],placement:"bottomRight",children:e.jsx(Y,{style:{fontSize:"20px",cursor:"pointer",padding:"4px",borderRadius:"50%"},onClick:O})})]}),f.video?e.jsx("div",{className:r?"sphere-post mb-3":"sphere-post mb-3 mt-2",children:e.jsx("video",{src:f.video,controls:!0,className:"w-100 rounded",style:{height:"100%",objectFit:"cover"}})}):(f.image||W)&&e.jsx("div",{className:r?"sphere-post mb-3":"sphere-post mb-3 mt-2",children:e.jsx("img",{src:f.image||W,alt:"Post content",className:"img-fluid"})}),e.jsx("div",{className:"mb-3 mt-3",children:e.jsx("p",{children:f.content||A})}),e.jsxs("div",{className:"card-footer",children:[e.jsxs("div",{className:"d-flex align-items-center justify-content-between text-muted mb-2",style:{fontSize:14},children:[e.jsx("div",{children:e.jsxs("span",{className:"me-3",children:[ae," Likes"]})}),e.jsxs("div",{children:[e.jsxs("span",{className:"me-3",children:[ee," Comments"]}),e.jsxs("span",{children:[se," Share"]})]})]}),e.jsxs("div",{className:"d-flex align-items-center justify-content-between text-muted action-box",style:{fontSize:14},children:[e.jsxs("div",{className:"d-flex align-items-center cursor-pointer py-2 px-3 rounded hover-bg-light",onClick:ce,style:{color:E?"#1890ff":void 0,opacity:Q?.6:1,pointerEvents:Q?"none":"auto"},children:[E?e.jsx(fe,{style:{fontSize:"18px",marginRight:"8px"}}):e.jsx(ge,{style:{fontSize:"18px",marginRight:"8px"}}),e.jsx("span",{children:E?"Liked":"Like"})]}),e.jsxs("div",{className:"d-flex align-items-center cursor-pointer py-2 px-3 rounded hover-bg-light",children:[e.jsx(ve,{style:{fontSize:"18px",marginRight:"8px"}}),e.jsx("span",{children:"Comment"})]}),window.user.id!==h.id&&window.user.id!==i&&e.jsxs("div",{className:"d-flex align-items-center cursor-pointer py-2 px-3 rounded hover-bg-light",onClick:oe,children:[e.jsx(Ce,{style:{fontSize:"18px",marginRight:"8px"}}),e.jsx("span",{children:"Share"})]})]})]})]})};export{ze as S,Ne as u};
