import{r as N,u as w,R as f,j as s,L as d}from"./index-Dklazue-.js";import{I as u}from"./index-vmMMvWhJ.js";import{t as y,P as b}from"./propertyUtils-DF3QzgTW.js";import{S as I}from"./spherepost-C2uEIc3S.js";import{C as L}from"./ContractPost-BP1laxu2.js";import{u as r}from"./useQuery-C3n1GVcJ.js";import{S as e}from"./Skeleton--lTrbnrp.js";import"./useMutation-BrUrPIzr.js";import"./button-DNhBCuue.js";import"./index-Cj6uPc4c.js";import"./EditOutlined-DxjsqgjX.js";import"./DeleteOutlined-BjE_e6LE.js";import"./useLikePost-BsM_zOKm.js";const P=()=>{var m,h;const n=w(),{data:a,isLoading:o}=r("postItem",{params:{limit:4},staleTime:5*60*1e3}),{data:l,isLoading:x}=r("properties",{params:{limit:4},staleTime:5*60*1e3}),{data:c,isLoading:j}=r("postItem",{params:{limit:4,type:"contract"},staleTime:5*60*1e3}),g=f.useMemo(()=>l!=null&&l.data?y(l.data.slice(0,4)):[],[l]),p=((m=a==null?void 0:a.data)==null?void 0:m.slice(0,4))||[],v=((h=c==null?void 0:c.data)==null?void 0:h.slice(0,4))||[];return s.jsx(u,{children:s.jsxs("div",{className:"container-fluid mt-4",children:[s.jsx("div",{className:"row mt-5",children:s.jsx("div",{className:"col-12 w-100",children:s.jsx("img",{src:"/assets/img/banner-img.png",alt:"",className:"img-fluid w-100"})})}),s.jsxs("div",{className:"row",children:[s.jsx("div",{className:"col-12",children:s.jsxs("div",{className:"d-flex align-items-center justify-content-between",children:[s.jsx("div",{children:s.jsx("h4",{className:"mt-4 mb-3",children:"Recent Posts"})}),s.jsx("div",{children:s.jsx(d,{to:"/sphare-it",className:"font-18",children:"See More"})})]})}),o?Array.from({length:4}).map((i,t)=>s.jsx("div",{className:"col-12 col-md-6 col-lg-4 col-xl-3 mb-4",children:s.jsx("div",{className:"card",children:s.jsxs("div",{className:"card-body",children:[s.jsxs("div",{className:"d-flex align-items-center mb-3",children:[s.jsx(e.Avatar,{size:40}),s.jsxs("div",{className:"ms-3 flex-grow-1",children:[s.jsx(e.Input,{style:{width:120,height:16}}),s.jsx("div",{className:"mt-1",children:s.jsx(e.Input,{style:{width:80,height:12}})})]})]}),s.jsx(e.Image,{active:!0,className:"w-100 text-center align-items-center d-flex mb-2",style:{width:"100%",height:200,display:"block"}}),s.jsx(e,{paragraph:{rows:2,width:["100%","80%"]}}),s.jsxs("div",{className:"d-flex justify-content-between align-items-center mt-3",children:[s.jsxs("div",{className:"d-flex gap-3",children:[s.jsx(e.Input,{style:{width:60,height:20}}),s.jsx(e.Input,{style:{width:60,height:20}}),s.jsx(e.Input,{style:{width:60,height:20}})]}),s.jsx(e.Input,{style:{width:40,height:20}})]})]})})},t)):p.map(i=>s.jsx("div",{className:"col-12 col-md-6 col-lg-4 col-xl-3 mb-4",children:s.jsx(I,{...i,onClick:()=>n(`/sphare-it/${i.id}`)})},i.id))]}),s.jsxs("div",{className:"row",children:[s.jsx("div",{className:"col-12",children:s.jsxs("div",{className:"d-flex align-items-center justify-content-between",children:[s.jsx("div",{children:s.jsx("h4",{className:"mt-4 mb-3",children:"Recent Properties"})}),s.jsx("div",{children:s.jsx(d,{to:"/listing",className:"font-18",children:"See More"})})]})}),x?Array.from({length:4}).map((i,t)=>s.jsx("div",{className:"col-12 col-sm-6 col-md-4 col-lg-3",children:s.jsx("div",{className:"card",children:s.jsxs("div",{className:"card-body",children:[s.jsxs("div",{className:"d-flex align-items-center mb-3",children:[s.jsx(e.Avatar,{size:40}),s.jsxs("div",{className:"ms-3 flex-grow-1",children:[s.jsx(e.Input,{style:{width:120,height:16}}),s.jsx("div",{className:"mt-1",children:s.jsx(e.Input,{style:{width:80,height:12}})})]})]}),s.jsx(e.Image,{active:!0,className:"w-100 text-center align-items-center d-flex mb-2",style:{width:"100%",height:200,display:"block"}}),s.jsx(e,{paragraph:{rows:2,width:["100%","80%"]}}),s.jsxs("div",{className:"d-flex justify-content-between align-items-center mt-3",children:[s.jsxs("div",{className:"d-flex gap-3",children:[s.jsx(e.Input,{style:{width:60,height:20}}),s.jsx(e.Input,{style:{width:60,height:20}}),s.jsx(e.Input,{style:{width:60,height:20}})]}),s.jsx(e.Input,{style:{width:40,height:20}})]})]})})},t)):g.map((i,t)=>s.jsx("div",{className:"col-12 col-sm-6 col-md-4 col-lg-3",children:s.jsx(b,{...i})},i.id||t))]}),s.jsxs("div",{className:"row",children:[s.jsx("div",{className:"col-12",children:s.jsxs("div",{className:"d-flex align-items-center justify-content-between",children:[s.jsx("div",{children:s.jsx("h4",{className:"mt-4 mb-3",children:"Recent Questions"})}),s.jsx("div",{children:s.jsx(d,{to:"/contract",className:"font-18",children:"See More"})})]})}),j?Array.from({length:4}).map((i,t)=>s.jsx("div",{className:"col-12 col-md-6 col-lg-4 col-xl-3 mb-4",children:s.jsx("div",{className:"card",children:s.jsxs("div",{className:"card-body",children:[s.jsxs("div",{className:"d-flex align-items-center mb-3",children:[s.jsx(e.Avatar,{size:40}),s.jsxs("div",{className:"ms-3 flex-grow-1",children:[s.jsx(e.Input,{style:{width:120,height:16}}),s.jsx("div",{className:"mt-1",children:s.jsx(e.Input,{style:{width:80,height:12}})})]})]}),s.jsx(e.Image,{active:!0,className:"w-100 text-center align-items-center d-flex mb-2",style:{width:"100%",height:200,display:"block"}}),s.jsx(e,{paragraph:{rows:2,width:["100%","80%"]}}),s.jsxs("div",{className:"d-flex justify-content-between align-items-center mt-3",children:[s.jsxs("div",{className:"d-flex gap-3",children:[s.jsx(e.Input,{style:{width:60,height:20}}),s.jsx(e.Input,{style:{width:60,height:20}}),s.jsx(e.Input,{style:{width:60,height:20}})]}),s.jsx(e.Input,{style:{width:40,height:20}})]})]})})},t)):v.map(i=>s.jsx("div",{className:"col-12 col-md-6 col-lg-4 col-xl-3 mb-4",children:s.jsx(L,{...i,onClick:()=>n(`/contract/${i.id}`)})},i.id))]}),s.jsx("div",{className:"row mt-4 mb-5",children:s.jsx("div",{className:"col-12 w-100",children:s.jsx("img",{src:"/assets/img/home-img.png",alt:"",className:"img-fluid w-100"})})})]})})},q=N.memo(P);export{q as default};
