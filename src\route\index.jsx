import React, { Suspense, useEffect } from "react";
import {
  BrowserRouter as Router,
  Route,
  Routes,
  useNavigate,
} from "react-router-dom";
import { Spin } from "antd";
import { ScopedSearchProvider } from "@/store/scopedsearchcontext";
import { FilterProvider } from "@/store/filtercontext";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import PublicRoute from "@/components/auth/PublicRoute";
import notificationService from "@/services/notification";
import {
  publicRoutes,
  privateRoutes,
  specialRoutes,
  errorRoutes,
} from "@/config/routes";

const loading = (
  <div
    className="lazy-loading"
    style={{
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      height: "100vh",
    }}
  >
    <Spin size="large" tip="Loading...">
      <div style={{ minHeight: "200px" }} />
    </Spin>
  </div>
);

/**
 * Root redirect component that handles initial routing
 */
const RootRedirect = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // Check for token in query parameters (legacy support)
    const urlParams = new URLSearchParams(window.location.search);
    const queryToken = urlParams.get("token");

    if (queryToken) {
      // Redirect to subscription page with token in path
      navigate(`/subscription/${queryToken}`);
      return;
    }

    const timeout = setTimeout(() => {
      // Check if user exists to determine redirect
      if (window.user && Object.keys(window.user).length > 0) {
        navigate("/home");
      } else {
        navigate("/login");
      }
    }, 2000);

    return () => clearTimeout(timeout);
  }, [navigate]);

  // Show banner while redirecting
  const Banner = specialRoutes.find(
    (route) => route.name === "root"
  )?.component;
  return Banner ? <Banner /> : <div>Loading...</div>;
};

/**
 * Main App component with protected routing
 */
const App = () => {
  useEffect(() => {
    // Initialize notification service when app starts
    notificationService.init().catch(console.error);
  }, []);

  return (
    <Suspense fallback={loading}>
      <Router>
        <ScopedSearchProvider>
          <FilterProvider>
            <Routes>
              {/* Root route with smart redirect */}
              <Route path="/" element={<RootRedirect />} />

              {/* Public routes (login, signup, etc.) */}
              {publicRoutes.map(({ path, component: Component, name }) => (
                <Route
                  key={name}
                  path={path}
                  element={
                    <PublicRoute>
                      <Component />
                    </PublicRoute>
                  }
                />
              ))}

              {/* Private routes (require authentication) */}
              {privateRoutes.map(({ path, component: Component, name }) => (
                <Route
                  key={name}
                  path={path}
                  element={
                    <ProtectedRoute>
                      <Component />
                    </ProtectedRoute>
                  }
                />
              ))}

              {/* 404 and error routes */}
              {errorRoutes.map(({ path, component: Component, name }) => (
                <Route
                  key={name || "error"}
                  path={path}
                  element={<Component />}
                />
              ))}
            </Routes>
          </FilterProvider>
        </ScopedSearchProvider>
      </Router>
    </Suspense>
  );
};

export default App;
