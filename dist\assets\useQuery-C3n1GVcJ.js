var ft=e=>{throw TypeError(e)};var G=(e,t,s)=>t.has(e)||ft("Cannot "+s);var i=(e,t,s)=>(G(e,t,"read from private field"),s?s.call(e):t.get(e)),b=(e,t,s)=>t.has(e)?ft("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,s),d=(e,t,s,r)=>(G(e,t,"write to private field"),r?r.call(e,s):t.set(e,s),s),g=(e,t,s)=>(G(e,t,"access private method"),s);import{aD as Ot,aE as dt,aF as Q,aG as X,aH as A,aI as Y,aJ as Z,aK as pt,aL as wt,aM as Et,aN as Pt,aO as gt,aP as vt,r as v,aQ as Qt,o as It,aR as Tt,p as xt}from"./index-Dklazue-.js";var R,n,H,y,F,_,I,T,q,k,N,M,U,x,B,h,j,tt,et,st,it,rt,at,nt,St,Rt,Dt=(Rt=class extends Ot{constructor(t,s){super();b(this,h);b(this,R);b(this,n);b(this,H);b(this,y);b(this,F);b(this,_);b(this,I);b(this,T);b(this,q);b(this,k);b(this,N);b(this,M);b(this,U);b(this,x);b(this,B,new Set);this.options=s,d(this,R,t),d(this,T,null),d(this,I,dt()),this.options.experimental_prefetchInRender||i(this,I).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(s)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(i(this,n).addObserver(this),bt(i(this,n),this.options)?g(this,h,j).call(this):this.updateResult(),g(this,h,it).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return ot(i(this,n),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return ot(i(this,n),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,g(this,h,rt).call(this),g(this,h,at).call(this),i(this,n).removeObserver(this)}setOptions(t){const s=this.options,r=i(this,n);if(this.options=i(this,R).defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof Q(this.options.enabled,i(this,n))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");g(this,h,nt).call(this),i(this,n).setOptions(this.options),s._defaulted&&!X(this.options,s)&&i(this,R).getQueryCache().notify({type:"observerOptionsUpdated",query:i(this,n),observer:this});const c=this.hasListeners();c&&yt(i(this,n),r,this.options,s)&&g(this,h,j).call(this),this.updateResult(),c&&(i(this,n)!==r||Q(this.options.enabled,i(this,n))!==Q(s.enabled,i(this,n))||A(this.options.staleTime,i(this,n))!==A(s.staleTime,i(this,n)))&&g(this,h,tt).call(this);const o=g(this,h,et).call(this);c&&(i(this,n)!==r||Q(this.options.enabled,i(this,n))!==Q(s.enabled,i(this,n))||o!==i(this,x))&&g(this,h,st).call(this,o)}getOptimisticResult(t){const s=i(this,R).getQueryCache().build(i(this,R),t),r=this.createResult(s,t);return Mt(this,r)&&(d(this,y,r),d(this,_,this.options),d(this,F,i(this,n).state)),r}getCurrentResult(){return i(this,y)}trackResult(t,s){return new Proxy(t,{get:(r,c)=>(this.trackProp(c),s==null||s(c),Reflect.get(r,c))})}trackProp(t){i(this,B).add(t)}getCurrentQuery(){return i(this,n)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const s=i(this,R).defaultQueryOptions(t),r=i(this,R).getQueryCache().build(i(this,R),s);return r.fetch().then(()=>this.createResult(r,s))}fetch(t){return g(this,h,j).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),i(this,y)))}createResult(t,s){var lt;const r=i(this,n),c=this.options,o=i(this,y),a=i(this,F),m=i(this,_),p=t!==r?t.state:i(this,H),{state:D}=t;let l={...D},C=!1,f;if(s._optimisticResults){const O=this.hasListeners(),W=!O&&bt(t,s),L=O&&yt(t,r,s,c);(W||L)&&(l={...l,...Pt(D.data,t.options)}),s._optimisticResults==="isRestoring"&&(l.fetchStatus="idle")}let{error:w,errorUpdatedAt:E,status:S}=l;f=l.data;let z=!1;if(s.placeholderData!==void 0&&f===void 0&&S==="pending"){let O;o!=null&&o.isPlaceholderData&&s.placeholderData===(m==null?void 0:m.placeholderData)?(O=o.data,z=!0):O=typeof s.placeholderData=="function"?s.placeholderData((lt=i(this,N))==null?void 0:lt.state.data,i(this,N)):s.placeholderData,O!==void 0&&(S="success",f=gt(o==null?void 0:o.data,O,s),C=!0)}if(s.select&&f!==void 0&&!z)if(o&&f===(a==null?void 0:a.data)&&s.select===i(this,q))f=i(this,k);else try{d(this,q,s.select),f=s.select(f),f=gt(o==null?void 0:o.data,f,s),d(this,k,f),d(this,T,null)}catch(O){d(this,T,O)}i(this,T)&&(w=i(this,T),f=i(this,k),E=Date.now(),S="error");const V=l.fetchStatus==="fetching",J=S==="pending",$=S==="error",ct=J&&V,ut=f!==void 0,P={status:S,fetchStatus:l.fetchStatus,isPending:J,isSuccess:S==="success",isError:$,isInitialLoading:ct,isLoading:ct,data:f,dataUpdatedAt:l.dataUpdatedAt,error:w,errorUpdatedAt:E,failureCount:l.fetchFailureCount,failureReason:l.fetchFailureReason,errorUpdateCount:l.errorUpdateCount,isFetched:l.dataUpdateCount>0||l.errorUpdateCount>0,isFetchedAfterMount:l.dataUpdateCount>p.dataUpdateCount||l.errorUpdateCount>p.errorUpdateCount,isFetching:V,isRefetching:V&&!J,isLoadingError:$&&!ut,isPaused:l.fetchStatus==="paused",isPlaceholderData:C,isRefetchError:$&&ut,isStale:ht(t,s),refetch:this.refetch,promise:i(this,I)};if(this.options.experimental_prefetchInRender){const O=K=>{P.status==="error"?K.reject(P.error):P.data!==void 0&&K.resolve(P.data)},W=()=>{const K=d(this,I,P.promise=dt());O(K)},L=i(this,I);switch(L.status){case"pending":t.queryHash===r.queryHash&&O(L);break;case"fulfilled":(P.status==="error"||P.data!==L.value)&&W();break;case"rejected":(P.status!=="error"||P.error!==L.reason)&&W();break}}return P}updateResult(){const t=i(this,y),s=this.createResult(i(this,n),this.options);if(d(this,F,i(this,n).state),d(this,_,this.options),i(this,F).data!==void 0&&d(this,N,i(this,n)),X(s,t))return;d(this,y,s);const r=()=>{if(!t)return!0;const{notifyOnChangeProps:c}=this.options,o=typeof c=="function"?c():c;if(o==="all"||!o&&!i(this,B).size)return!0;const a=new Set(o??i(this,B));return this.options.throwOnError&&a.add("error"),Object.keys(i(this,y)).some(m=>{const u=m;return i(this,y)[u]!==t[u]&&a.has(u)})};g(this,h,St).call(this,{listeners:r()})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&g(this,h,it).call(this)}},R=new WeakMap,n=new WeakMap,H=new WeakMap,y=new WeakMap,F=new WeakMap,_=new WeakMap,I=new WeakMap,T=new WeakMap,q=new WeakMap,k=new WeakMap,N=new WeakMap,M=new WeakMap,U=new WeakMap,x=new WeakMap,B=new WeakMap,h=new WeakSet,j=function(t){g(this,h,nt).call(this);let s=i(this,n).fetch(this.options,t);return t!=null&&t.throwOnError||(s=s.catch(Y)),s},tt=function(){g(this,h,rt).call(this);const t=A(this.options.staleTime,i(this,n));if(Z||i(this,y).isStale||!pt(t))return;const r=wt(i(this,y).dataUpdatedAt,t)+1;d(this,M,setTimeout(()=>{i(this,y).isStale||this.updateResult()},r))},et=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(i(this,n)):this.options.refetchInterval)??!1},st=function(t){g(this,h,at).call(this),d(this,x,t),!(Z||Q(this.options.enabled,i(this,n))===!1||!pt(i(this,x))||i(this,x)===0)&&d(this,U,setInterval(()=>{(this.options.refetchIntervalInBackground||Et.isFocused())&&g(this,h,j).call(this)},i(this,x)))},it=function(){g(this,h,tt).call(this),g(this,h,st).call(this,g(this,h,et).call(this))},rt=function(){i(this,M)&&(clearTimeout(i(this,M)),d(this,M,void 0))},at=function(){i(this,U)&&(clearInterval(i(this,U)),d(this,U,void 0))},nt=function(){const t=i(this,R).getQueryCache().build(i(this,R),this.options);if(t===i(this,n))return;const s=i(this,n);d(this,n,t),d(this,H,t.state),this.hasListeners()&&(s==null||s.removeObserver(this),t.addObserver(this))},St=function(t){vt.batch(()=>{t.listeners&&this.listeners.forEach(s=>{s(i(this,y))}),i(this,R).getQueryCache().notify({query:i(this,n),type:"observerResultsUpdated"})})},Rt);function Ft(e,t){return Q(t.enabled,e)!==!1&&e.state.data===void 0&&!(e.state.status==="error"&&t.retryOnMount===!1)}function bt(e,t){return Ft(e,t)||e.state.data!==void 0&&ot(e,t,t.refetchOnMount)}function ot(e,t,s){if(Q(t.enabled,e)!==!1&&A(t.staleTime,e)!=="static"){const r=typeof s=="function"?s(e):s;return r==="always"||r!==!1&&ht(e,t)}return!1}function yt(e,t,s,r){return(e!==t||Q(r.enabled,e)===!1)&&(!s.suspense||e.state.status!=="error")&&ht(e,s)}function ht(e,t){return Q(t.enabled,e)!==!1&&e.isStaleByTime(A(t.staleTime,e))}function Mt(e,t){return!X(e.getCurrentResult(),t)}var Ct=v.createContext(!1),Ut=()=>v.useContext(Ct);Ct.Provider;function Lt(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var _t=v.createContext(Lt()),kt=()=>v.useContext(_t),Nt=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},Bt=e=>{v.useEffect(()=>{e.clearReset()},[e])},jt=({result:e,errorResetBoundary:t,throwOnError:s,query:r,suspense:c})=>e.isError&&!t.isReset()&&!e.isFetching&&r&&(c&&e.data===void 0||Qt(s,[e.error,r])),At=e=>{if(e.suspense){const t=r=>r==="static"?r:Math.max(r??1e3,1e3),s=e.staleTime;e.staleTime=typeof s=="function"?(...r)=>t(s(...r)):t(s),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3))}},Ht=(e,t)=>e.isLoading&&e.isFetching&&!t,qt=(e,t)=>(e==null?void 0:e.suspense)&&t.isPending,mt=(e,t,s)=>t.fetchOptimistic(e).catch(()=>{s.clearReset()});function zt(e,t,s){var l,C,f,w,E;const r=Ut(),c=kt(),o=It(),a=o.defaultQueryOptions(e);(C=(l=o.getDefaultOptions().queries)==null?void 0:l._experimental_beforeQuery)==null||C.call(l,a),a._optimisticResults=r?"isRestoring":"optimistic",At(a),Nt(a,c),Bt(c);const m=!o.getQueryCache().get(a.queryHash),[u]=v.useState(()=>new t(o,a)),p=u.getOptimisticResult(a),D=!r&&e.subscribed!==!1;if(v.useSyncExternalStore(v.useCallback(S=>{const z=D?u.subscribe(vt.batchCalls(S)):Y;return u.updateResult(),z},[u,D]),()=>u.getCurrentResult(),()=>u.getCurrentResult()),v.useEffect(()=>{u.setOptions(a)},[a,u]),qt(a,p))throw mt(a,u,c);if(jt({result:p,errorResetBoundary:c,throwOnError:a.throwOnError,query:o.getQueryCache().get(a.queryHash),suspense:a.suspense}))throw p.error;if((w=(f=o.getDefaultOptions().queries)==null?void 0:f._experimental_afterQuery)==null||w.call(f,a,p),a.experimental_prefetchInRender&&!Z&&Ht(p,r)){const S=m?mt(a,u,c):(E=o.getQueryCache().get(a.queryHash))==null?void 0:E.promise;S==null||S.catch(Y).finally(()=>{u.updateResult()})}return a.notifyOnChangeProps?p:u.trackResult(p)}function Wt(e,t){return zt(e,Dt)}function Kt(e,t={}){const{params:s,slug:r,showSuccessNotification:c=!1,token:o,enabled:a,...m}=t,u=Tt[e];if(!u)throw new Error(`API endpoint "${e}" not found`);const p=v.useMemo(()=>{const C=[e];if(r&&C.push(r),s&&Object.keys(s).length>0){const f=Object.keys(s).sort().reduce((w,E)=>(w[E]=s[E],w),{});C.push(JSON.stringify(f))}return C},[e,r,s]),D=v.useMemo(()=>async()=>{try{const f=u.method==="POST"&&c,{data:w,pagination:E}=await xt.request(e,{params:s,slug:r,showSuccessNotification:f,token:o});return{data:w,pagination:E}}catch(C){throw C}},[e,r,s,c,u.method]),l=v.useMemo(()=>a===void 0?!0:typeof a=="function"?a:!!a,[a]);return Wt({queryKey:p,queryFn:D,select:m.select,enabled:l,placeholderData:m.placeholderData,...m})}function Gt(e,t={}){var u;const[s,r]=v.useState(t.initialPage||1),[c,o]=v.useState(t.initialPageSize||10),a={...t.params||{}};e!=="getUser"&&e!=="properties"&&(a.page=s,a.limit=c);const m=Kt(e,{...t,params:a,placeholderData:t.keepPreviousData?p=>p:void 0,refetchOnWindowFocus:!1,staleTime:0,gcTime:0,select:t.select,notifyOnChangeProps:["data","error","isLoading"],showSuccessNotification:t.showSuccessNotification});return{...m,page:s,pageSize:c,setPage:r,setPageSize:o,nextPage:()=>r(p=>p+1),prevPage:()=>r(p=>Math.max(1,p-1)),goToPage:p=>r(p),pagination:((u=m.data)==null?void 0:u.pagination)||window.globalPagination||{}}}export{Gt as a,Kt as u};
