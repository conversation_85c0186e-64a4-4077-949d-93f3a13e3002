import lodash from "lodash";
import constants from "@/config/constants";
import helper from "@/helpers";
import apiClient from "@/services/apiClient";
import { io } from "socket.io-client";
import _ from "lodash";
async function bootstrap() {
  
  window.lodash = lodash;
  window.constants = constants;
  window.helper = helper;
  window.apiClient = apiClient;
  try {
    window.user = await window.helper.getStorageData("session");
  } catch (error) {
    window.user = {};
  }
  if (!_.isEmpty(window.user)) {
    const socket = io(window.constants.chat_url, {
      transports: ["websocket"],
      query: {
        authorization: `${window?.user?.api_token}`,
      },
    });
    
 
    window.socket = socket;
  }
}

export default bootstrap;