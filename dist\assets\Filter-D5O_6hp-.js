import{r as n,t as Rt,v as lt,w as ae,x as he,y as xe,K as J,z as de,A as At,B as qt,C as et,D as Pe,E as Vt,G as It,I as xt,J as Wt,M as Je,N as Yt,O as Xt,P as Ut,Q as ge,S as _t,R as Se,T as Gt,U as Kt,V as Jt,j}from"./index-Dklazue-.js";import{B as Fe}from"./index-BUt89ETK.js";import{u as Qt}from"./useLocationData-CQHRFCOb.js";import{u as Zt}from"./useStartupData-QD8kuEYy.js";import{g as ea}from"./languageUtils-BKYM3hOY.js";import{F as Ke}from"./react-stripe.esm-ypQSOYN5.js";import{B as nt}from"./button-DNhBCuue.js";import{R as ta}from"./searchbar-BCS1MYCc.js";import{M as aa}from"./index-CatU6E6a.js";import{S as ra}from"./index-vmMMvWhJ.js";import{b as na,T as oa}from"./useMutation-BrUrPIzr.js";function it(e,t,l){return(e-t)/(l-t)}function st(e,t,l,o){var i=it(t,l,o),u={};switch(e){case"rtl":u.right="".concat(i*100,"%"),u.transform="translateX(50%)";break;case"btt":u.bottom="".concat(i*100,"%"),u.transform="translateY(50%)";break;case"ttb":u.top="".concat(i*100,"%"),u.transform="translateY(-50%)";break;default:u.left="".concat(i*100,"%"),u.transform="translateX(-50%)";break}return u}function Ae(e,t){return Array.isArray(e)?e[t]:e}var qe=n.createContext({min:0,max:0,direction:"ltr",step:1,includedStart:0,includedEnd:0,tabIndex:0,keyboard:!0,styles:{},classNames:{}}),la=n.createContext({}),ia=["prefixCls","value","valueIndex","onStartMove","onDelete","style","render","dragging","draggingDelete","onOffsetChange","onChangeComplete","onFocus","onMouseEnter"],kt=n.forwardRef(function(e,t){var l=e.prefixCls,o=e.value,i=e.valueIndex,u=e.onStartMove,f=e.onDelete,c=e.style,x=e.render,m=e.dragging,g=e.draggingDelete,S=e.onOffsetChange,_=e.onChangeComplete,b=e.onFocus,h=e.onMouseEnter,s=Rt(e,ia),r=n.useContext(qe),v=r.min,a=r.max,p=r.direction,M=r.disabled,F=r.keyboard,N=r.range,q=r.tabIndex,T=r.ariaLabelForHandle,R=r.ariaLabelledByForHandle,$=r.ariaRequired,k=r.ariaValueTextFormatterForHandle,E=r.styles,I=r.classNames,y="".concat(l,"-handle"),d=function(L){M||u(L,i)},O=function(L){b==null||b(L,i)},P=function(L){h(L,i)},z=function(L){if(!M&&F){var D=null;switch(L.which||L.keyCode){case J.LEFT:D=p==="ltr"||p==="btt"?-1:1;break;case J.RIGHT:D=p==="ltr"||p==="btt"?1:-1;break;case J.UP:D=p!=="ttb"?1:-1;break;case J.DOWN:D=p!=="ttb"?-1:1;break;case J.HOME:D="min";break;case J.END:D="max";break;case J.PAGE_UP:D=2;break;case J.PAGE_DOWN:D=-2;break;case J.BACKSPACE:case J.DELETE:f(i);break}D!==null&&(L.preventDefault(),S(D,i))}},Y=function(L){switch(L.which||L.keyCode){case J.LEFT:case J.RIGHT:case J.UP:case J.DOWN:case J.HOME:case J.END:case J.PAGE_UP:case J.PAGE_DOWN:_==null||_();break}},H=st(p,o,v,a),G={};if(i!==null){var Q;G={tabIndex:M?null:Ae(q,i),role:"slider","aria-valuemin":v,"aria-valuemax":a,"aria-valuenow":o,"aria-disabled":M,"aria-label":Ae(T,i),"aria-labelledby":Ae(R,i),"aria-required":Ae($,i),"aria-valuetext":(Q=Ae(k,i))===null||Q===void 0?void 0:Q(o),"aria-orientation":p==="ltr"||p==="rtl"?"horizontal":"vertical",onMouseDown:d,onTouchStart:d,onFocus:O,onMouseEnter:P,onKeyDown:z,onKeyUp:Y}}var _e=n.createElement("div",lt({ref:t,className:he(y,xe(xe(xe({},"".concat(y,"-").concat(i+1),i!==null&&N),"".concat(y,"-dragging"),m),"".concat(y,"-dragging-delete"),g),I.handle),style:ae(ae(ae({},H),c),E.handle)},G,s));return x&&(_e=x(_e,{index:i,prefixCls:l,value:o,dragging:m,draggingDelete:g})),_e}),sa=["prefixCls","style","onStartMove","onOffsetChange","values","handleRender","activeHandleRender","draggingIndex","draggingDelete","onFocus"],ca=n.forwardRef(function(e,t){var l=e.prefixCls,o=e.style,i=e.onStartMove,u=e.onOffsetChange,f=e.values,c=e.handleRender,x=e.activeHandleRender,m=e.draggingIndex,g=e.draggingDelete,S=e.onFocus,_=Rt(e,sa),b=n.useRef({}),h=n.useState(!1),s=de(h,2),r=s[0],v=s[1],a=n.useState(-1),p=de(a,2),M=p[0],F=p[1],N=function(k){F(k),v(!0)},q=function(k,E){N(E),S==null||S(k)},T=function(k,E){N(E)};n.useImperativeHandle(t,function(){return{focus:function(k){var E;(E=b.current[k])===null||E===void 0||E.focus()},hideHelp:function(){At.flushSync(function(){v(!1)})}}});var R=ae({prefixCls:l,onStartMove:i,onOffsetChange:u,render:c,onFocus:q,onMouseEnter:T},_);return n.createElement(n.Fragment,null,f.map(function($,k){var E=m===k;return n.createElement(kt,lt({ref:function(y){y?b.current[k]=y:delete b.current[k]},dragging:E,draggingDelete:E&&g,style:Ae(o,k),key:k,value:$,valueIndex:k},R))}),x&&r&&n.createElement(kt,lt({key:"a11y"},R,{value:f[M],valueIndex:null,dragging:m!==-1,draggingDelete:g,render:x,style:{pointerEvents:"none"},tabIndex:null,"aria-hidden":!0})))}),ua=function(t){var l=t.prefixCls,o=t.style,i=t.children,u=t.value,f=t.onClick,c=n.useContext(qe),x=c.min,m=c.max,g=c.direction,S=c.includedStart,_=c.includedEnd,b=c.included,h="".concat(l,"-text"),s=st(g,u,x,m);return n.createElement("span",{className:he(h,xe({},"".concat(h,"-active"),b&&S<=u&&u<=_)),style:ae(ae({},s),o),onMouseDown:function(v){v.stopPropagation()},onClick:function(){f(u)}},i)},da=function(t){var l=t.prefixCls,o=t.marks,i=t.onClick,u="".concat(l,"-mark");return o.length?n.createElement("div",{className:u},o.map(function(f){var c=f.value,x=f.style,m=f.label;return n.createElement(ua,{key:c,prefixCls:u,style:x,value:c,onClick:i},m)})):null},va=function(t){var l=t.prefixCls,o=t.value,i=t.style,u=t.activeStyle,f=n.useContext(qe),c=f.min,x=f.max,m=f.direction,g=f.included,S=f.includedStart,_=f.includedEnd,b="".concat(l,"-dot"),h=g&&S<=o&&o<=_,s=ae(ae({},st(m,o,c,x)),typeof i=="function"?i(o):i);return h&&(s=ae(ae({},s),typeof u=="function"?u(o):u)),n.createElement("span",{className:he(b,xe({},"".concat(b,"-active"),h)),style:s})},fa=function(t){var l=t.prefixCls,o=t.marks,i=t.dots,u=t.style,f=t.activeStyle,c=n.useContext(qe),x=c.min,m=c.max,g=c.step,S=n.useMemo(function(){var _=new Set;if(o.forEach(function(h){_.add(h.value)}),i&&g!==null)for(var b=x;b<=m;)_.add(b),b+=g;return Array.from(_)},[x,m,g,i,o]);return n.createElement("div",{className:"".concat(l,"-step")},S.map(function(_){return n.createElement(va,{prefixCls:l,key:_,value:_,style:u,activeStyle:f})}))},$t=function(t){var l=t.prefixCls,o=t.style,i=t.start,u=t.end,f=t.index,c=t.onStartMove,x=t.replaceCls,m=n.useContext(qe),g=m.direction,S=m.min,_=m.max,b=m.disabled,h=m.range,s=m.classNames,r="".concat(l,"-track"),v=it(i,S,_),a=it(u,S,_),p=function(q){!b&&c&&c(q,-1)},M={};switch(g){case"rtl":M.right="".concat(v*100,"%"),M.width="".concat(a*100-v*100,"%");break;case"btt":M.bottom="".concat(v*100,"%"),M.height="".concat(a*100-v*100,"%");break;case"ttb":M.top="".concat(v*100,"%"),M.height="".concat(a*100-v*100,"%");break;default:M.left="".concat(v*100,"%"),M.width="".concat(a*100-v*100,"%")}var F=x||he(r,xe(xe({},"".concat(r,"-").concat(f+1),f!==null&&h),"".concat(l,"-track-draggable"),c),s.track);return n.createElement("div",{className:F,style:ae(ae({},M),o),onMouseDown:p,onTouchStart:p})},ga=function(t){var l=t.prefixCls,o=t.style,i=t.values,u=t.startPoint,f=t.onStartMove,c=n.useContext(qe),x=c.included,m=c.range,g=c.min,S=c.styles,_=c.classNames,b=n.useMemo(function(){if(!m){if(i.length===0)return[];var s=u??g,r=i[0];return[{start:Math.min(s,r),end:Math.max(s,r)}]}for(var v=[],a=0;a<i.length-1;a+=1)v.push({start:i[a],end:i[a+1]});return v},[i,m,u,g]);if(!x)return null;var h=b!=null&&b.length&&(_.tracks||S.tracks)?n.createElement($t,{index:null,prefixCls:l,start:b[0].start,end:b[b.length-1].end,replaceCls:he(_.tracks,"".concat(l,"-tracks")),style:S.tracks}):null;return n.createElement(n.Fragment,null,h,b.map(function(s,r){var v=s.start,a=s.end;return n.createElement($t,{index:r,prefixCls:l,style:ae(ae({},Ae(o,r)),S.track),start:v,end:a,key:r,onStartMove:f})}))},ma=130;function Et(e){var t="targetTouches"in e?e.targetTouches[0]:e;return{pageX:t.pageX,pageY:t.pageY}}function ha(e,t,l,o,i,u,f,c,x,m,g){var S=n.useState(null),_=de(S,2),b=_[0],h=_[1],s=n.useState(-1),r=de(s,2),v=r[0],a=r[1],p=n.useState(!1),M=de(p,2),F=M[0],N=M[1],q=n.useState(l),T=de(q,2),R=T[0],$=T[1],k=n.useState(l),E=de(k,2),I=E[0],y=E[1],d=n.useRef(null),O=n.useRef(null),P=n.useRef(null),z=n.useContext(la),Y=z.onDragStart,H=z.onDragChange;qt(function(){v===-1&&$(l)},[l,v]),n.useEffect(function(){return function(){document.removeEventListener("mousemove",d.current),document.removeEventListener("mouseup",O.current),P.current&&(P.current.removeEventListener("touchmove",d.current),P.current.removeEventListener("touchend",O.current))}},[]);var G=function(D,X,U){X!==void 0&&h(X),$(D);var ne=D;U&&(ne=D.filter(function(W,Z){return Z!==v})),f(ne),H&&H({rawValues:D,deleteIndex:U?v:-1,draggingIndex:v,draggingValue:X})},Q=et(function(L,D,X){if(L===-1){var U=I[0],ne=I[I.length-1],W=o-U,Z=i-ne,ie=D*(i-o);ie=Math.max(ie,W),ie=Math.min(ie,Z);var ve=u(U+ie);ie=ve-U;var be=I.map(function(me){return me+ie});G(be)}else{var ye=(i-o)*D,Ce=Pe(R);Ce[L]=I[L];var ke=x(Ce,ye,L,"dist");G(ke.values,ke.value,X)}}),_e=function(D,X,U){D.stopPropagation();var ne=U||l,W=ne[X];a(X),h(W),y(ne),$(ne),N(!1);var Z=Et(D),ie=Z.pageX,ve=Z.pageY,be=!1;Y&&Y({rawValues:ne,draggingIndex:X,draggingValue:W});var ye=function(me){me.preventDefault();var se=Et(me),we=se.pageX,ze=se.pageY,Ee=we-ie,fe=ze-ve,A=e.current.getBoundingClientRect(),ee=A.width,$e=A.height,pe,ce;switch(t){case"btt":pe=-fe/$e,ce=Ee;break;case"ttb":pe=fe/$e,ce=Ee;break;case"rtl":pe=-Ee/ee,ce=fe;break;default:pe=Ee/ee,ce=fe}be=m?Math.abs(ce)>ma&&g<R.length:!1,N(be),Q(X,pe,be)},Ce=function ke(me){me.preventDefault(),document.removeEventListener("mouseup",ke),document.removeEventListener("mousemove",ye),P.current&&(P.current.removeEventListener("touchmove",d.current),P.current.removeEventListener("touchend",O.current)),d.current=null,O.current=null,P.current=null,c(be),a(-1),N(!1)};document.addEventListener("mouseup",Ce),document.addEventListener("mousemove",ye),D.currentTarget.addEventListener("touchend",Ce),D.currentTarget.addEventListener("touchmove",ye),d.current=ye,O.current=Ce,P.current=D.currentTarget},re=n.useMemo(function(){var L=Pe(l).sort(function(W,Z){return W-Z}),D=Pe(R).sort(function(W,Z){return W-Z}),X={};D.forEach(function(W){X[W]=(X[W]||0)+1}),L.forEach(function(W){X[W]=(X[W]||0)-1});var U=m?1:0,ne=Object.values(X).reduce(function(W,Z){return W+Math.abs(Z)},0);return ne<=U?R:l},[l,R,m]);return[v,b,F,re,_e]}function ba(e,t,l,o,i,u){var f=n.useCallback(function(b){return Math.max(e,Math.min(t,b))},[e,t]),c=n.useCallback(function(b){if(l!==null){var h=e+Math.round((f(b)-e)/l)*l,s=function(p){return(String(p).split(".")[1]||"").length},r=Math.max(s(l),s(t),s(e)),v=Number(h.toFixed(r));return e<=v&&v<=t?v:null}return null},[l,e,t,f]),x=n.useCallback(function(b){var h=f(b),s=o.map(function(a){return a.value});l!==null&&s.push(c(b)),s.push(e,t);var r=s[0],v=t-e;return s.forEach(function(a){var p=Math.abs(h-a);p<=v&&(r=a,v=p)}),r},[e,t,o,l,f,c]),m=function b(h,s,r){var v=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"unit";if(typeof s=="number"){var a,p=h[r],M=p+s,F=[];o.forEach(function($){F.push($.value)}),F.push(e,t),F.push(c(p));var N=s>0?1:-1;v==="unit"?F.push(c(p+N*l)):F.push(c(M)),F=F.filter(function($){return $!==null}).filter(function($){return s<0?$<=p:$>=p}),v==="unit"&&(F=F.filter(function($){return $!==p}));var q=v==="unit"?p:M;a=F[0];var T=Math.abs(a-q);if(F.forEach(function($){var k=Math.abs($-q);k<T&&(a=$,T=k)}),a===void 0)return s<0?e:t;if(v==="dist")return a;if(Math.abs(s)>1){var R=Pe(h);return R[r]=a,b(R,s-N,r,v)}return a}else{if(s==="min")return e;if(s==="max")return t}},g=function(h,s,r){var v=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"unit",a=h[r],p=m(h,s,r,v);return{value:p,changed:p!==a}},S=function(h){return u===null&&h===0||typeof u=="number"&&h<u},_=function(h,s,r){var v=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"unit",a=h.map(x),p=a[r],M=m(a,s,r,v);if(a[r]=M,i===!1){var F=u||0;r>0&&a[r-1]!==p&&(a[r]=Math.max(a[r],a[r-1]+F)),r<a.length-1&&a[r+1]!==p&&(a[r]=Math.min(a[r],a[r+1]-F))}else if(typeof u=="number"||u===null){for(var N=r+1;N<a.length;N+=1)for(var q=!0;S(a[N]-a[N-1])&&q;){var T=g(a,1,N);a[N]=T.value,q=T.changed}for(var R=r;R>0;R-=1)for(var $=!0;S(a[R]-a[R-1])&&$;){var k=g(a,-1,R-1);a[R-1]=k.value,$=k.changed}for(var E=a.length-1;E>0;E-=1)for(var I=!0;S(a[E]-a[E-1])&&I;){var y=g(a,-1,E-1);a[E-1]=y.value,I=y.changed}for(var d=0;d<a.length-1;d+=1)for(var O=!0;S(a[d+1]-a[d])&&O;){var P=g(a,1,d+1);a[d+1]=P.value,O=P.changed}}return{value:a[r],values:a}};return[x,_]}function ya(e){return n.useMemo(function(){if(e===!0||!e)return[!!e,!1,!1,0];var t=e.editable,l=e.draggableTrack,o=e.minCount,i=e.maxCount;return[!0,t,!t&&l,o||0,i]},[e])}var Ca=n.forwardRef(function(e,t){var l=e.prefixCls,o=l===void 0?"rc-slider":l,i=e.className,u=e.style,f=e.classNames,c=e.styles,x=e.id,m=e.disabled,g=m===void 0?!1:m,S=e.keyboard,_=S===void 0?!0:S,b=e.autoFocus,h=e.onFocus,s=e.onBlur,r=e.min,v=r===void 0?0:r,a=e.max,p=a===void 0?100:a,M=e.step,F=M===void 0?1:M,N=e.value,q=e.defaultValue,T=e.range,R=e.count,$=e.onChange,k=e.onBeforeChange,E=e.onAfterChange,I=e.onChangeComplete,y=e.allowCross,d=y===void 0?!0:y,O=e.pushable,P=O===void 0?!1:O,z=e.reverse,Y=e.vertical,H=e.included,G=H===void 0?!0:H,Q=e.startPoint,_e=e.trackStyle,re=e.handleStyle,L=e.railStyle,D=e.dotStyle,X=e.activeDotStyle,U=e.marks,ne=e.dots,W=e.handleRender,Z=e.activeHandleRender,ie=e.track,ve=e.tabIndex,be=ve===void 0?0:ve,ye=e.ariaLabelForHandle,Ce=e.ariaLabelledByForHandle,ke=e.ariaRequired,me=e.ariaValueTextFormatterForHandle,se=n.useRef(null),we=n.useRef(null),ze=n.useMemo(function(){return Y?z?"ttb":"btt":z?"rtl":"ltr"},[z,Y]),Ee=ya(T),fe=de(Ee,5),A=fe[0],ee=fe[1],$e=fe[2],pe=fe[3],ce=fe[4],ue=n.useMemo(function(){return isFinite(v)?v:0},[v]),Me=n.useMemo(function(){return isFinite(p)?p:100},[p]),Re=n.useMemo(function(){return F!==null&&F<=0?1:F},[F]),te=n.useMemo(function(){return typeof P=="boolean"?P?Re:!1:P>=0?P:!1},[P,Re]),oe=n.useMemo(function(){return Object.keys(U||{}).map(function(w){var C=U[w],B={value:Number(w)};return C&&Vt(C)==="object"&&!n.isValidElement(C)&&("label"in C||"style"in C)?(B.style=C.style,B.label=C.label):B.label=C,B}).filter(function(w){var C=w.label;return C||typeof C=="number"}).sort(function(w,C){return w.value-C.value})},[U]),tt=ba(ue,Me,Re,oe,d,te),We=de(tt,2),Be=We[0],Ye=We[1],Xe=na(q,{value:N}),ct=de(Xe,2),He=ct[0],Dt=ct[1],le=n.useMemo(function(){var w=He==null?[]:Array.isArray(He)?He:[He],C=de(w,1),B=C[0],V=B===void 0?ue:B,K=He===null?[]:[V];if(A){if(K=Pe(w),R||He===void 0){var Ne=R>=0?R+1:2;for(K=K.slice(0,Ne);K.length<Ne;){var Oe;K.push((Oe=K[K.length-1])!==null&&Oe!==void 0?Oe:ue)}}K.sort(function(De,je){return De-je})}return K.forEach(function(De,je){K[je]=Be(De)}),K},[He,A,ue,R,Be]),Ve=function(C){return A?C:C[0]},Qe=et(function(w){var C=Pe(w).sort(function(B,V){return B-V});$&&!It(C,le,!0)&&$(Ve(C)),Dt(C)}),ut=et(function(w){w&&se.current.hideHelp();var C=Ve(le);E==null||E(C),xt(!E,"[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead."),I==null||I(C)}),jt=function(C){if(!(g||!ee||le.length<=pe)){var B=Pe(le);B.splice(C,1),k==null||k(Ve(B)),Qe(B);var V=Math.max(0,C-1);se.current.hideHelp(),se.current.focus(V)}},Ft=ha(we,ze,le,ue,Me,Be,Qe,ut,Ye,ee,pe),Ue=de(Ft,5),dt=Ue[0],Pt=Ue[1],wt=Ue[2],at=Ue[3],vt=Ue[4],ft=function(C,B){if(!g){var V=Pe(le),K=0,Ne=0,Oe=Me-ue;le.forEach(function(Te,Ze){var St=Math.abs(C-Te);St<=Oe&&(Oe=St,K=Ze),Te<C&&(Ne=Ze)});var De=K;ee&&Oe!==0&&(!ce||le.length<ce)?(V.splice(Ne+1,0,C),De=Ne+1):V[K]=C,A&&!le.length&&R===void 0&&V.push(C);var je=Ve(V);if(k==null||k(je),Qe(V),B){var Le,Ie;(Le=document.activeElement)===null||Le===void 0||(Ie=Le.blur)===null||Ie===void 0||Ie.call(Le),se.current.focus(De),vt(B,De,V)}else E==null||E(je),xt(!E,"[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead."),I==null||I(je)}},zt=function(C){C.preventDefault();var B=we.current.getBoundingClientRect(),V=B.width,K=B.height,Ne=B.left,Oe=B.top,De=B.bottom,je=B.right,Le=C.clientX,Ie=C.clientY,Te;switch(ze){case"btt":Te=(De-Ie)/K;break;case"ttb":Te=(Ie-Oe)/K;break;case"rtl":Te=(je-Le)/V;break;default:Te=(Le-Ne)/V}var Ze=ue+Te*(Me-ue);ft(Be(Ze),C)},Bt=n.useState(null),gt=de(Bt,2),rt=gt[0],mt=gt[1],Ht=function(C,B){if(!g){var V=Ye(le,C,B);k==null||k(Ve(le)),Qe(V.values),mt(V.value)}};n.useEffect(function(){if(rt!==null){var w=le.indexOf(rt);w>=0&&se.current.focus(w)}mt(null)},[rt]);var Nt=n.useMemo(function(){return $e&&Re===null?!1:$e},[$e,Re]),ht=et(function(w,C){vt(w,C),k==null||k(Ve(le))}),bt=dt!==-1;n.useEffect(function(){if(!bt){var w=le.lastIndexOf(Pt);se.current.focus(w)}},[bt]);var Ge=n.useMemo(function(){return Pe(at).sort(function(w,C){return w-C})},[at]),Tt=n.useMemo(function(){return A?[Ge[0],Ge[Ge.length-1]]:[ue,Ge[0]]},[Ge,A,ue]),yt=de(Tt,2),Ct=yt[0],pt=yt[1];n.useImperativeHandle(t,function(){return{focus:function(){se.current.focus(0)},blur:function(){var C,B=document,V=B.activeElement;(C=we.current)!==null&&C!==void 0&&C.contains(V)&&(V==null||V.blur())}}}),n.useEffect(function(){b&&se.current.focus(0)},[]);var Lt=n.useMemo(function(){return{min:ue,max:Me,direction:ze,disabled:g,keyboard:_,step:Re,included:G,includedStart:Ct,includedEnd:pt,range:A,tabIndex:be,ariaLabelForHandle:ye,ariaLabelledByForHandle:Ce,ariaRequired:ke,ariaValueTextFormatterForHandle:me,styles:c||{},classNames:f||{}}},[ue,Me,ze,g,_,Re,G,Ct,pt,A,be,ye,Ce,ke,me,c,f]);return n.createElement(qe.Provider,{value:Lt},n.createElement("div",{ref:we,className:he(o,i,xe(xe(xe(xe({},"".concat(o,"-disabled"),g),"".concat(o,"-vertical"),Y),"".concat(o,"-horizontal"),!Y),"".concat(o,"-with-marks"),oe.length)),style:u,onMouseDown:zt,id:x},n.createElement("div",{className:he("".concat(o,"-rail"),f==null?void 0:f.rail),style:ae(ae({},L),c==null?void 0:c.rail)}),ie!==!1&&n.createElement(ga,{prefixCls:o,style:_e,values:le,startPoint:Q,onStartMove:Nt?ht:void 0}),n.createElement(fa,{prefixCls:o,marks:oe,dots:ne,style:D,activeStyle:X}),n.createElement(ca,{ref:se,prefixCls:o,style:re,values:at,draggingIndex:dt,draggingDelete:wt,onStartMove:ht,onOffsetChange:Ht,onFocus:h,onBlur:s,handleRender:W,activeHandleRender:Z,onChangeComplete:ut,onDelete:ee?jt:void 0}),n.createElement(da,{prefixCls:o,marks:oe,onClick:ft})))});const pa=n.createContext({}),Mt=n.forwardRef((e,t)=>{const{open:l,draggingDelete:o,value:i}=e,u=n.useRef(null),f=l&&!o,c=n.useRef(null);function x(){Je.cancel(c.current),c.current=null}function m(){c.current=Je(()=>{var g;(g=u.current)===null||g===void 0||g.forceAlign(),c.current=null})}return n.useEffect(()=>(f?m():x(),x),[f,e.title,i]),n.createElement(oa,Object.assign({ref:Wt(u,t)},e,{open:f}))}),Sa=e=>{const{componentCls:t,antCls:l,controlSize:o,dotSize:i,marginFull:u,marginPart:f,colorFillContentHover:c,handleColorDisabled:x,calc:m,handleSize:g,handleSizeHover:S,handleActiveColor:_,handleActiveOutlineColor:b,handleLineWidth:h,handleLineWidthHover:s,motionDurationMid:r}=e;return{[t]:Object.assign(Object.assign({},Ut(e)),{position:"relative",height:o,margin:`${ge(f)} ${ge(u)}`,padding:0,cursor:"pointer",touchAction:"none","&-vertical":{margin:`${ge(u)} ${ge(f)}`},[`${t}-rail`]:{position:"absolute",backgroundColor:e.railBg,borderRadius:e.borderRadiusXS,transition:`background-color ${r}`},[`${t}-track,${t}-tracks`]:{position:"absolute",transition:`background-color ${r}`},[`${t}-track`]:{backgroundColor:e.trackBg,borderRadius:e.borderRadiusXS},[`${t}-track-draggable`]:{boxSizing:"content-box",backgroundClip:"content-box",border:"solid rgba(0,0,0,0)"},"&:hover":{[`${t}-rail`]:{backgroundColor:e.railHoverBg},[`${t}-track`]:{backgroundColor:e.trackHoverBg},[`${t}-dot`]:{borderColor:c},[`${t}-handle::after`]:{boxShadow:`0 0 0 ${ge(h)} ${e.colorPrimaryBorderHover}`},[`${t}-dot-active`]:{borderColor:e.dotActiveBorderColor}},[`${t}-handle`]:{position:"absolute",width:g,height:g,outline:"none",userSelect:"none","&-dragging-delete":{opacity:0},"&::before":{content:'""',position:"absolute",insetInlineStart:m(h).mul(-1).equal(),insetBlockStart:m(h).mul(-1).equal(),width:m(g).add(m(h).mul(2)).equal(),height:m(g).add(m(h).mul(2)).equal(),backgroundColor:"transparent"},"&::after":{content:'""',position:"absolute",insetBlockStart:0,insetInlineStart:0,width:g,height:g,backgroundColor:e.colorBgElevated,boxShadow:`0 0 0 ${ge(h)} ${e.handleColor}`,outline:"0px solid transparent",borderRadius:"50%",cursor:"pointer",transition:`
            inset-inline-start ${r},
            inset-block-start ${r},
            width ${r},
            height ${r},
            box-shadow ${r},
            outline ${r}
          `},"&:hover, &:active, &:focus":{"&::before":{insetInlineStart:m(S).sub(g).div(2).add(s).mul(-1).equal(),insetBlockStart:m(S).sub(g).div(2).add(s).mul(-1).equal(),width:m(S).add(m(s).mul(2)).equal(),height:m(S).add(m(s).mul(2)).equal()},"&::after":{boxShadow:`0 0 0 ${ge(s)} ${_}`,outline:`6px solid ${b}`,width:S,height:S,insetInlineStart:e.calc(g).sub(S).div(2).equal(),insetBlockStart:e.calc(g).sub(S).div(2).equal()}}},[`&-lock ${t}-handle`]:{"&::before, &::after":{transition:"none"}},[`${t}-mark`]:{position:"absolute",fontSize:e.fontSize},[`${t}-mark-text`]:{position:"absolute",display:"inline-block",color:e.colorTextDescription,textAlign:"center",wordBreak:"keep-all",cursor:"pointer",userSelect:"none","&-active":{color:e.colorText}},[`${t}-step`]:{position:"absolute",background:"transparent",pointerEvents:"none"},[`${t}-dot`]:{position:"absolute",width:i,height:i,backgroundColor:e.colorBgElevated,border:`${ge(h)} solid ${e.dotBorderColor}`,borderRadius:"50%",cursor:"pointer",transition:`border-color ${e.motionDurationSlow}`,pointerEvents:"auto","&-active":{borderColor:e.dotActiveBorderColor}},[`&${t}-disabled`]:{cursor:"not-allowed",[`${t}-rail`]:{backgroundColor:`${e.railBg} !important`},[`${t}-track`]:{backgroundColor:`${e.trackBgDisabled} !important`},[`
          ${t}-dot
        `]:{backgroundColor:e.colorBgElevated,borderColor:e.trackBgDisabled,boxShadow:"none",cursor:"not-allowed"},[`${t}-handle::after`]:{backgroundColor:e.colorBgElevated,cursor:"not-allowed",width:g,height:g,boxShadow:`0 0 0 ${ge(h)} ${x}`,insetInlineStart:0,insetBlockStart:0},[`
          ${t}-mark-text,
          ${t}-dot
        `]:{cursor:"not-allowed !important"}},[`&-tooltip ${l}-tooltip-inner`]:{minWidth:"unset"}})}},Ot=(e,t)=>{const{componentCls:l,railSize:o,handleSize:i,dotSize:u,marginFull:f,calc:c}=e,x=t?"paddingBlock":"paddingInline",m=t?"width":"height",g=t?"height":"width",S=t?"insetBlockStart":"insetInlineStart",_=t?"top":"insetInlineStart",b=c(o).mul(3).sub(i).div(2).equal(),h=c(i).sub(o).div(2).equal(),s=t?{borderWidth:`${ge(h)} 0`,transform:`translateY(${ge(c(h).mul(-1).equal())})`}:{borderWidth:`0 ${ge(h)}`,transform:`translateX(${ge(e.calc(h).mul(-1).equal())})`};return{[x]:o,[g]:c(o).mul(3).equal(),[`${l}-rail`]:{[m]:"100%",[g]:o},[`${l}-track,${l}-tracks`]:{[g]:o},[`${l}-track-draggable`]:Object.assign({},s),[`${l}-handle`]:{[S]:b},[`${l}-mark`]:{insetInlineStart:0,top:0,[_]:c(o).mul(3).add(t?0:f).equal(),[m]:"100%"},[`${l}-step`]:{insetInlineStart:0,top:0,[_]:o,[m]:"100%",[g]:o},[`${l}-dot`]:{position:"absolute",[S]:c(o).sub(u).div(2).equal()}}},xa=e=>{const{componentCls:t,marginPartWithMark:l}=e;return{[`${t}-horizontal`]:Object.assign(Object.assign({},Ot(e,!0)),{[`&${t}-with-marks`]:{marginBottom:l}})}},_a=e=>{const{componentCls:t}=e;return{[`${t}-vertical`]:Object.assign(Object.assign({},Ot(e,!1)),{height:"100%"})}},ka=e=>{const l=e.controlHeightLG/4,o=e.controlHeightSM/2,i=e.lineWidth+1,u=e.lineWidth+1*1.5,f=e.colorPrimary,c=new _t(f).setA(.2).toRgbString();return{controlSize:l,railSize:4,handleSize:l,handleSizeHover:o,dotSize:8,handleLineWidth:i,handleLineWidthHover:u,railBg:e.colorFillTertiary,railHoverBg:e.colorFillSecondary,trackBg:e.colorPrimaryBorder,trackHoverBg:e.colorPrimaryBorderHover,handleColor:e.colorPrimaryBorder,handleActiveColor:f,handleActiveOutlineColor:c,handleColorDisabled:new _t(e.colorTextDisabled).onBackground(e.colorBgContainer).toHexString(),dotBorderColor:e.colorBorderSecondary,dotActiveBorderColor:e.colorPrimaryBorder,trackBgDisabled:e.colorBgContainerDisabled}},$a=Yt("Slider",e=>{const t=Xt(e,{marginPart:e.calc(e.controlHeight).sub(e.controlSize).div(2).equal(),marginFull:e.calc(e.controlSize).div(2).equal(),marginPartWithMark:e.calc(e.controlHeightLG).sub(e.controlSize).equal()});return[Sa(t),xa(t),_a(t)]},ka);function ot(){const[e,t]=n.useState(!1),l=n.useRef(null),o=()=>{Je.cancel(l.current)},i=u=>{o(),u?t(u):l.current=Je(()=>{t(u)})};return n.useEffect(()=>o,[]),[e,i]}var Ea=function(e,t){var l={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(l[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,o=Object.getOwnPropertySymbols(e);i<o.length;i++)t.indexOf(o[i])<0&&Object.prototype.propertyIsEnumerable.call(e,o[i])&&(l[o[i]]=e[o[i]]);return l};function Ma(e,t){return e||e===null?e:t||t===null?t:l=>typeof l=="number"?l.toString():""}const Ra=Se.forwardRef((e,t)=>{const{prefixCls:l,range:o,className:i,rootClassName:u,style:f,disabled:c,tooltipPrefixCls:x,tipFormatter:m,tooltipVisible:g,getTooltipPopupContainer:S,tooltipPlacement:_,tooltip:b={},onChangeComplete:h,classNames:s,styles:r}=e,v=Ea(e,["prefixCls","range","className","rootClassName","style","disabled","tooltipPrefixCls","tipFormatter","tooltipVisible","getTooltipPopupContainer","tooltipPlacement","tooltip","onChangeComplete","classNames","styles"]),{vertical:a}=e,{getPrefixCls:p,direction:M,className:F,style:N,classNames:q,styles:T,getPopupContainer:R}=Gt("slider"),$=Se.useContext(Kt),k=c??$,{handleRender:E,direction:I}=Se.useContext(pa),d=(I||M)==="rtl",[O,P]=ot(),[z,Y]=ot(),H=Object.assign({},b),{open:G,placement:Q,getPopupContainer:_e,prefixCls:re,formatter:L}=H,D=G??g,X=(O||z)&&D!==!1,U=Ma(L,m),[ne,W]=ot(),Z=A=>{h==null||h(A),W(!1)},ie=(A,ee)=>A||(ee?d?"left":"right":"top"),ve=p("slider",l),[be,ye,Ce]=$a(ve),ke=he(i,F,q.root,s==null?void 0:s.root,u,{[`${ve}-rtl`]:d,[`${ve}-lock`]:ne},ye,Ce);d&&!v.vertical&&(v.reverse=!v.reverse),Se.useEffect(()=>{const A=()=>{Je(()=>{Y(!1)},1)};return document.addEventListener("mouseup",A),()=>{document.removeEventListener("mouseup",A)}},[]);const me=o&&!D,se=E||((A,ee)=>{const{index:$e}=ee,pe=A.props;function ce(te,oe,tt){var We,Be,Ye,Xe;tt&&((Be=(We=v)[te])===null||Be===void 0||Be.call(We,oe)),(Xe=(Ye=pe)[te])===null||Xe===void 0||Xe.call(Ye,oe)}const ue=Object.assign(Object.assign({},pe),{onMouseEnter:te=>{P(!0),ce("onMouseEnter",te)},onMouseLeave:te=>{P(!1),ce("onMouseLeave",te)},onMouseDown:te=>{Y(!0),W(!0),ce("onMouseDown",te)},onFocus:te=>{var oe;Y(!0),(oe=v.onFocus)===null||oe===void 0||oe.call(v,te),ce("onFocus",te,!0)},onBlur:te=>{var oe;Y(!1),(oe=v.onBlur)===null||oe===void 0||oe.call(v,te),ce("onBlur",te,!0)}}),Me=Se.cloneElement(A,ue),Re=(!!D||X)&&U!==null;return me?Me:Se.createElement(Mt,Object.assign({},H,{prefixCls:p("tooltip",re??x),title:U?U(ee.value):"",value:ee.value,open:Re,placement:ie(Q??_,a),key:$e,classNames:{root:`${ve}-tooltip`},getPopupContainer:_e||S||R}),Me)}),we=me?(A,ee)=>{const $e=Se.cloneElement(A,{style:Object.assign(Object.assign({},A.props.style),{visibility:"hidden"})});return Se.createElement(Mt,Object.assign({},H,{prefixCls:p("tooltip",re??x),title:U?U(ee.value):"",open:U!==null&&X,placement:ie(Q??_,a),key:"tooltip",classNames:{root:`${ve}-tooltip`},getPopupContainer:_e||S||R,draggingDelete:ee.draggingDelete}),$e)}:void 0,ze=Object.assign(Object.assign(Object.assign(Object.assign({},T.root),N),r==null?void 0:r.root),f),Ee=Object.assign(Object.assign({},T.tracks),r==null?void 0:r.tracks),fe=he(q.tracks,s==null?void 0:s.tracks);return be(Se.createElement(Ca,Object.assign({},v,{classNames:Object.assign({handle:he(q.handle,s==null?void 0:s.handle),rail:he(q.rail,s==null?void 0:s.rail),track:he(q.track,s==null?void 0:s.track)},fe?{tracks:fe}:{}),styles:Object.assign({handle:Object.assign(Object.assign({},T.handle),r==null?void 0:r.handle),rail:Object.assign(Object.assign({},T.rail),r==null?void 0:r.rail),track:Object.assign(Object.assign({},T.track),r==null?void 0:r.track)},Object.keys(Ee).length?{tracks:Ee}:{}),step:v.step,range:o,className:ke,style:ze,disabled:k,ref:t,prefixCls:ve,handleRender:se,activeHandleRender:we,onChangeComplete:Z})))}),La=({fields:e=[]})=>{const{filters:t,updateFilters:l,clearFilters:o,isFilterModalOpen:i,setIsFilterModalOpen:u}=Jt(),[f]=Ke.useForm(),{data:c}=Zt(),x=c==null?void 0:c.data,{selectedState:m,stateOptions:g,cityOptions:S,statesLoading:_,citiesLoading:b,handleStateChange:h,updateSelectedState:s}=Qt(),r=Se.useMemo(()=>ea(x==null?void 0:x.languages),[x==null?void 0:x.languages]),v=new Date().getFullYear(),a={property_size:{min:0,max:25e3,step:1,defaultValue:[0,5e3],formatter:y=>`${y}`},lot_size:{min:0,max:2e4,step:1,defaultValue:[0,1e4],formatter:y=>`${y} Sq.ft`},price:{min:0,max:25e3,step:1,defaultValue:[0,15e3],formatter:y=>`$${y.toLocaleString()}`},year_built:{min:1900,max:v,step:1,defaultValue:[1900,v],formatter:y=>y.toString()}},[p,M]=n.useState({property_size:a.property_size.defaultValue,lot_size:a.lot_size.defaultValue,price:a.price.defaultValue,year_built:a.year_built.defaultValue});Se.useEffect(()=>{const y={...t,min_size:t.min_size??a.property_size.defaultValue[0],max_size:t.max_size??a.property_size.defaultValue[1],min_lot_size:t.min_lot_size??a.lot_size.defaultValue[0],max_lot_size:t.max_lot_size??a.lot_size.defaultValue[1],min_price:t.min_price??a.price.defaultValue[0],max_price:t.max_price??a.price.defaultValue[1],min_year_built:t.min_year_built??a.year_built.defaultValue[0],max_year_built:t.max_year_built??a.year_built.defaultValue[1]};f.setFieldsValue(y),M({property_size:[y.min_size,y.max_size],lot_size:[y.min_lot_size,y.max_lot_size],price:[y.min_price,y.max_price],year_built:[y.min_year_built,y.max_year_built]}),t.state?s(t.state):s("")},[t,f,s]);const F=()=>{const y=f.getFieldsValue(),d=Object.entries(y).reduce((O,[P,z])=>(z!=null&&z!==""&&(O[P]=z),O),{});d.max_bed&&(d.min_bed=d.max_bed,delete d.max_bed),d.max_bath&&(d.min_bath=d.max_bath,delete d.max_bath),d.min_garage=0,d.size_type&&(d.lot_size_type=d.size_type),l(d),u(!1)},N=()=>{f.resetFields(),o(),M({property_size:a.property_size.defaultValue,lot_size:a.lot_size.defaultValue,price:a.price.defaultValue,year_built:a.year_built.defaultValue}),s(""),u(!1)},q=y=>{h(y,f)},T=Ke.useWatch("size_type",f)||"sqft",R=T==="sqft"?"Sq.ft":T==="sq.m"?"Sq.M":T==="sq.yd"?"Sq.Yd":T==="acres"?"Acres":"Sq.ft",$=(y,d,O,P)=>{const z=a[y],Y=p[y],H=y==="property_size"?G=>`${G} ${R}`:z.formatter;return j.jsxs("div",{className:"col-12 col-md-6 mb-3",children:[j.jsx("div",{className:"form-item-label",children:j.jsx("label",{children:P})}),j.jsx(Ra,{range:!0,min:z.min,max:z.max,step:z.step,value:Y,onChange:G=>{M(Q=>({...Q,[y]:G})),f.setFieldsValue({[d]:G[0],[O]:G[1]})},tooltip:{formatter:H}}),j.jsxs("div",{className:"d-flex justify-content-between mt-2",children:[j.jsx("span",{children:H(Y[0])}),j.jsx("span",{children:H(Y[1])})]}),j.jsx(Ke.Item,{name:d,hidden:!0,children:j.jsx("input",{})}),j.jsx(Ke.Item,{name:O,hidden:!0,children:j.jsx("input",{})})]},`${d}_${O}`)},k=y=>{const{name:d,label:O,type:P,placeholder:z,options:Y,...H}=y;if(d==="state")return j.jsx(Fe,{name:d,label:O,type:"select",placeholder:z,options:g,loading:_,handlechange:q,showSearch:!0,...H},d);if(d==="city")return j.jsx(Fe,{name:d,label:O,type:"select",placeholder:z,options:S,disabled:!m,loading:b,showSearch:!0,...H},d);if(d==="professional_type")return j.jsx(Fe,{name:d,label:O,type:"radio",options:[{value:"broker",label:"Real Estate Broker"},{value:"lender",label:"Lender/ mtg Broker"},{value:"commercial",label:"Commercial Agent"}],...H},d);if(d==="languages")return j.jsx(Fe,{name:d,label:O,type:"select",placeholder:z,mode:"multiple",options:r,...H},d);if(d==="multi_state_license")return j.jsx(Fe,{name:d,label:O,type:"radio",options:[{value:!0,label:"Yes"},{value:!1,label:"No"}],...H},d);switch(P){case"select":return j.jsx(Fe,{name:d,label:O,type:"select",placeholder:z,options:Y,loading:H.loading,...H},d);case"radio":return j.jsx(Fe,{name:d,label:O,type:"radio",options:Y,...H},d);case"datepiker":return j.jsx(Fe,{name:d,label:O,type:"datepicker",placeholder:z,...H},d);case"input":default:return j.jsx(Fe,{name:d,label:O,placeholder:z,type:P==="input"?"number":void 0,...H},d)}},I=["type_id","home_style_id","state","city","zip","basement","max_bed","max_bath","max_garage","size_type"].map(y=>e.find(d=>d.name===y)).filter(Boolean);return j.jsxs(j.Fragment,{children:[j.jsx(nt,{icon:j.jsx(ta,{}),onClick:()=>u(!0),type:"default",className:"d-none",children:"Filter"}),j.jsx(aa,{title:"Filter",open:i,onCancel:()=>u(!1),footer:null,width:1e3,children:j.jsxs(Ke,{form:f,layout:"vertical",onFinish:F,preserve:!1,children:[j.jsxs("div",{className:"row",children:[I.map(y=>j.jsx("div",{className:"col-12 col-md-6 mb-3",children:k(y)},y.name)),$("property_size","min_size","max_size",`Property Size (${R})`),$("lot_size","min_lot_size","max_lot_size","Lot Size (Sq.ft)"),$("price","min_price","max_price","Price ($)"),$("year_built","min_year_built","max_year_built","Year Built")]}),j.jsx("div",{className:"d-flex justify-content-end gap-2 mt-4",children:j.jsxs(ra,{children:[j.jsx(nt,{onClick:N,children:"Reset"}),j.jsx(nt,{type:"primary",htmlType:"submit",children:"Apply Filters"})]})})]})})]})};export{La as F};
