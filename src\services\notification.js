import React from 'react';
import { getDeviceToken, showNotification, messaging } from '../firebase';
import { notification as antdNotification } from 'antd';
import { onMessage } from 'firebase/messaging';

// Global notification tracker to prevent duplicates across all instances
const globalNotificationTracker = new Set();

class NotificationService {
  constructor() {
    this.isInitialized = false;
    this.deviceToken = null;
    this.foregroundListenerActive = false;
  }

  async init() {
    if (this.isInitialized && this.deviceToken) return this.deviceToken;

    try {
      this.deviceToken = await getDeviceToken();
      
      if (this.deviceToken) {
        this.setupForegroundListener();
        this.isInitialized = true;
        return this.deviceToken;
      } else {
        const fallbackToken = `service_fallback_${Date.now()}`;
        this.deviceToken = fallbackToken;
        localStorage.setItem('device_token', fallbackToken);
        this.isInitialized = true;
        return fallbackToken;
      }
    } catch (error) {
      const errorToken = `service_error_${Date.now()}`;
      this.deviceToken = errorToken;
      localStorage.setItem('device_token', errorToken);
      this.isInitialized = true;
      return errorToken;
    }
  }

  setupForegroundListener() {
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    
    // Prevent multiple listeners
    if (this.foregroundListenerActive) return;
    
    if (!isSafari && !isIOS) {
      this.foregroundListenerActive = true;
      
      onMessage(messaging, (payload) => {
        if (document.visibilityState === 'visible') {
         
          // this.handleForegroundMessage(payload);
        }
      });
    }
  }

  // handleForegroundMessage(payload) {
  
  //   const { notification, data } = payload;
  //   const messageId = data?.messageId || `${notification?.title}-${notification?.body}-${Date.now()}`;
    
  //   // Check global tracker to prevent duplicates
  //   if (globalNotificationTracker.has(messageId)) return;
    
  //   globalNotificationTracker.add(messageId);
    
  //   if (notification) {
  //     // Show Ant Design success notification for foreground
  //     antdNotification.success({
  //       message: notification.title,
  //       description: notification.body,
  //       duration: 5,
  //       placement: 'topRight',
  //     });
     
  //   }
    
  //   // Clean up after 30 seconds
  //   setTimeout(() => {
  //     globalNotificationTracker.delete(messageId);
  //   }, 30000);
  // }

  getToken() {
    return this.deviceToken || localStorage.getItem('device_token');
  }

  showManualNotification(title, body, data = {}) {
    const messageId = `manual-${title}-${body}-${Date.now()}`;
    
    if (globalNotificationTracker.has(messageId)) return;
    
    globalNotificationTracker.add(messageId);
    
    showNotification(title, {
      body: body,
      data: data
    });
    
    setTimeout(() => {
      globalNotificationTracker.delete(messageId);
    }, 30000);
  }

  isSupported() {
    return 'Notification' in window;
  }

  isSafariOrIOS() {
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    return isSafari || isIOS;
  }
}

export default new NotificationService();