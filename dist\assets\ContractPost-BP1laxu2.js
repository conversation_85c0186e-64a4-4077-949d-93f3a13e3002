import{u as V,j as e,i as L,k as X}from"./index-Dklazue-.js";import{u as Y,b as w,R as Z,a as q,c as _}from"./useLikePost-BsM_zOKm.js";import{D as I}from"./index-vmMMvWhJ.js";import{R as O}from"./EditOutlined-DxjsqgjX.js";import{R as ee}from"./DeleteOutlined-BjE_e6LE.js";L.extend(X);const xe=({id:d,user:P,created_at:p,content:f,image:j,video:S,likes_count:g=0,comments_count:z=0,reposts_count:se=0,original_post:i,time:$,question:o,body:A,likes:T,comments:D,shares:te,reactions:ie,onClick:E,img:F,type:ne,onEdit:u,onDelete:N,showActions:ce=!1,is_liked:J=!1,user_id:le})=>{V();const n=!!i,l=n?i:{content:f,image:j,video:S},c=n?i.user:P,M=f||o||A,y=j||F,U=z??D??0,W=d,B=n?i.is_liked:J,G=n?g:g??T??0,{isLiked:r,likesCount:H,isToggling:v,handleLikeToggle:K}=Y(W,B,G),m=((c==null?void 0:c.id)||(c==null?void 0:c.user_id))===window.user.id,x=(s,t)=>{var C;const a=s?L(s).fromNow():$||"Just now",R=(t==null?void 0:t.state)||((C=t==null?void 0:t.location)==null?void 0:C.state)||"";return R?`${R} · ${a}`:a},h=[];m&&h.push({key:"edit",icon:e.jsx(O,{}),label:"Edit"},{key:"delete",icon:e.jsx(ee,{}),label:"Delete"});const k=s=>{var t;if((t=s.domEvent)==null||t.stopPropagation(),s.key==="edit"){const a=n?i.id:d;u&&u(a)}else s.key==="delete"&&N&&N(d)},b=s=>{s.stopPropagation()},Q=s=>{s.stopPropagation(),K()};return e.jsxs("div",{className:"post-card sphere-card p-3 rounded border bg-white d-flex flex-column ",onClick:E,children:[n?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"d-flex align-items-start mb-2 pb-2 border-bottom",children:e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("img",{src:"/assets/img/placeholder.jpg",alt:"Anonymous",className:"rounded-circle",width:40,height:40,style:{cursor:"default"}}),e.jsxs("div",{className:"ms-2",children:[e.jsxs("h6",{className:"mb-0",children:["Anonymous",e.jsx("span",{className:"color-light font-14",children:"Repost this"})]}),e.jsx("small",{className:"text-muted",children:x(p,{})})]})]})}),e.jsxs("div",{className:"d-flex justify-content-between align-items-center py-2",children:[e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("img",{src:"/assets/img/placeholder.jpg",alt:"Anonymous",className:"rounded-circle",width:40,height:40,style:{cursor:"default"}}),e.jsxs("div",{className:"ms-2",children:[e.jsx("h6",{className:"mb-0",children:"Anonymous"}),e.jsx("small",{className:"text-muted",children:x(i==null?void 0:i.created_at,{})})]})]}),m&&e.jsx(I,{menu:{items:h,onClick:k},trigger:["click"],placement:"bottomRight",children:e.jsx(w,{style:{fontSize:"20px",cursor:"pointer",padding:"4px",borderRadius:"50%"},onClick:b})})]})]}):e.jsxs("div",{className:"d-flex justify-content-between align-items-center pb-2 border-bottom",children:[e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("img",{src:"/assets/img/placeholder.jpg",alt:"Anonymous",className:"rounded-circle",width:40,height:40,style:{cursor:"default"}}),e.jsxs("div",{className:"ms-2",children:[e.jsx("h6",{className:"mb-0",children:"Anonymous"}),e.jsx("small",{className:"text-muted",children:x(p,{})})]})]}),m&&e.jsx(I,{menu:{items:h,onClick:k},trigger:["click"],placement:"bottomRight",children:e.jsx(w,{style:{fontSize:"20px",cursor:"pointer",padding:"4px",borderRadius:"50%"},onClick:b})})]}),l.video?e.jsx("div",{className:n?"sphere-post mb-3":"sphere-post mb-3 mt-2",children:e.jsx("video",{src:l.video,controls:!0,className:"w-100 rounded",style:{height:"100%",objectFit:"cover"}})}):(l.image||y)&&e.jsx("div",{className:n?"sphere-post mb-3":"sphere-post mb-3 mt-2",children:e.jsx("img",{src:l.image||y,alt:"Post content",className:"img-fluid"})}),e.jsx("div",{className:"mt-4",children:e.jsx("h4",{className:"font-18",children:o==null?void 0:o.question})}),e.jsx("div",{className:"mb-3 mt-3",children:e.jsx("p",{children:l.content||M})}),e.jsxs("div",{className:"card-footer",children:[e.jsxs("div",{className:"d-flex align-items-center justify-content-between text-muted mb-2",style:{fontSize:14},children:[e.jsx("div",{children:e.jsxs("span",{className:"me-3",children:[H," Likes"]})}),e.jsx("div",{children:e.jsxs("span",{className:"me-3",children:[U," Comments"]})})]}),e.jsxs("div",{className:"d-flex align-items-center justify-content-between text-muted action-box",style:{fontSize:14},children:[e.jsxs("div",{className:"d-flex align-items-center cursor-pointer py-2 px-3 rounded hover-bg-light",onClick:Q,style:{color:r?"#1890ff":void 0,opacity:v?.6:1,pointerEvents:v?"none":"auto"},children:[r?e.jsx(Z,{style:{fontSize:"18px",marginRight:"8px"}}):e.jsx(q,{style:{fontSize:"18px",marginRight:"8px"}}),e.jsx("span",{children:r?"Liked":"Like"})]}),e.jsxs("div",{className:"d-flex align-items-center cursor-pointer py-2 px-3 rounded hover-bg-light",children:[e.jsx(_,{style:{fontSize:"18px",marginRight:"8px"}}),e.jsx("span",{children:"Comment"})]})]})]})]})};export{xe as C};
