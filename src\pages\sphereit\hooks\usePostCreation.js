import { useState, useEffect, useCallback } from "react";
import { message } from "antd";
import { useMutation } from "@/hooks/reactQuery";

export const usePostCreation = (editPost = null, onEditComplete = null) => {
  const [content, setContent] = useState("");
  const [selectedFile, setSelectedFile] = useState(null);
  const [filePreview, setFilePreview] = useState(null);
  const [fileType, setFileType] = useState(null);

  const isEditMode = !!editPost;

  const { mutate: createPost, isPending: isCreatingPost } = useMutation(
    "addPost",
    {
      useFormData: true,
      showSuccessNotification: false,
      invalidateQueries: [
        { queryKey: ["postItem"], type: "paginated" },
        { queryKey: ["postItem"], type: "all" },
        { queryKey: ["getComments"], type: "all" },
      ],
      onSuccess: (data) => {
        resetForm();
      },
    }
  );

  const { mutate: updatePost, isPending: isUpdatingPost } = useMutation(
    "updatePost",
    {
      useFormData: true,
      showSuccessNotification: true,
      invalidateQueries: [
        { queryKey: ["postItem"], type: "paginated" },
        { queryKey: ["postItem"], type: "all" }, // Invalidate all postItem queries including detail views
        { queryKey: ["getComments"], type: "all" }, // Invalidate comments cache
      ],
      onSuccess: (data) => {
        resetForm();
        if (onEditComplete) {
          onEditComplete();
        }
      },
    }
  );

  const isProcessing = isCreatingPost || isUpdatingPost;

  const resetForm = useCallback(() => {
    setContent("");
    setSelectedFile(null);
    if (filePreview) {
      URL.revokeObjectURL(filePreview);
    }
    setFilePreview(null);
    setFileType(null);
  }, [filePreview]);

  const handleFileSelect = useCallback(
    (file, type) => {
      const isValidImage = type === "image" && file.type.startsWith("image/");
      const isValidVideo = type === "video" && file.type.startsWith("video/");

      if (!isValidImage && !isValidVideo) {
        message.error(`Please select a valid ${type} file`);
        return false;
      }

      const maxSize = 5;
      const isValidSize = file.size / 1024 / 1024 < maxSize;

      if (!isValidSize) {
        message.error(
          `${
            type === "image" ? "Image" : "Video"
          } must be smaller than ${maxSize}MB`
        );
        return false;
      }

      if (filePreview) {
        URL.revokeObjectURL(filePreview);
      }

      setSelectedFile(file);
      setFileType(type);
      const preview = URL.createObjectURL(file);
      setFilePreview(preview);

      return false;
    },
    [filePreview]
  );

  const handleImageUpload = useCallback(
    ({ file }) => {
      return handleFileSelect(file, "image");
    },
    [handleFileSelect]
  );

  const handleVideoUpload = useCallback(
    ({ file }) => {
      return handleFileSelect(file, "video");
    },
    [handleFileSelect]
  );

  const removeFile = useCallback(() => {
    if (filePreview) {
      URL.revokeObjectURL(filePreview);
    }
    setSelectedFile(null);
    setFilePreview(null);
    setFileType(null);
  }, [filePreview]);

  const handleSubmit = useCallback(() => {
    if (!content.trim()) {
      message.error("Please enter some content for your post");
      return;
    }

    const payload = {
      content: content.trim(),
    };

    if (selectedFile) {
      if (fileType === "image") {
        payload.image = selectedFile;
      } else if (fileType === "video") {
        payload.video = selectedFile;
      }
    }

    if (isEditMode) {
      updatePost({ slug: editPost.id, data: payload });
    } else {
      createPost(payload);
    }
  }, [
    content,
    selectedFile,
    fileType,
    isEditMode,
    editPost,
    createPost,
    updatePost,
  ]);

  useEffect(() => {
    if (editPost) {
      setContent(editPost.content || "");

      if (editPost.image) {
        setFileType("image");
        setFilePreview(editPost.image);
        setSelectedFile(null);
      } else if (editPost.video) {
        setFileType("video");
        setFilePreview(editPost.video);
        setSelectedFile(null);
      }
    }
  }, [editPost]);

  useEffect(() => {
    return () => {
      if (filePreview) {
        URL.revokeObjectURL(filePreview);
      }
    };
  }, [filePreview]);

  const isFormValid = content.trim().length > 0;
  const handleSharePost = (originalPostId) => {
    if (!originalPostId) {
      message.error("Post ID is missing");
      return;
    }

    const payload = {
      original_post_id: originalPostId,
    };

    createPost(payload);
  };
  return {
    content,
    selectedFile,
    filePreview,
    fileType,
    isCreatingPost: isProcessing,
    isFormValid,
    isEditMode,
    setContent,
    handleImageUpload,
    handleVideoUpload,
    removeFile,
    handleSubmit,
    resetForm,
    handleSharePost,
  };
};

export default usePostCreation;
