import{f as q,u as K,r as O,j as s,h as G,i as W,k as $}from"./index-Dklazue-.js";import{I as d,u as H}from"./index-vmMMvWhJ.js";import{C as Y}from"./ContractPost-BP1laxu2.js";import{u as J,C as U}from"./useContractPostCreation-DUI14t5S.js";import{B as V}from"./index-BUt89ETK.js";import{u as y}from"./useQuery-C3n1GVcJ.js";import{u as w}from"./useMutation-BrUrPIzr.js";import{R as X,u as Z}from"./useLikeComment-CwMXCmiT.js";import{F as ss}from"./flatbutton-B_tUS4QM.js";import{F as C}from"./react-stripe.esm-ypQSOYN5.js";import{S as t}from"./Skeleton--lTrbnrp.js";import{E as c}from"./index-CHbHgJvR.js";import{B as b}from"./button-DNhBCuue.js";import{R as es,a as ts}from"./useLikePost-BsM_zOKm.js";import"./index-Cj6uPc4c.js";import"./EditOutlined-DxjsqgjX.js";import"./DeleteOutlined-BjE_e6LE.js";import"./useStartupData-QD8kuEYy.js";import"./trialUtils-PQN1eD5v.js";import"./index-BNeAK5sW.js";import"./index-ChnPz6Q1.js";import"./fade-B36sOBLs.js";import"./useLocale-BNhrTARD.js";import"./index-Vj938NLd.js";import"./index-CjGjc6T5.js";W.extend($);const Ss=()=>{const{id:r}=q(),m=K(),{showAlert:I}=H(),[p,h]=O.useState(null),[g]=C.useForm(),{data:x,isLoading:P,error:E}=y("postItem",{slug:r,enabled:!!r,staleTime:5*60*1e3,gcTime:10*60*1e3,refetchOnWindowFocus:!1,refetchOnMount:"always"}),{data:u,isLoading:k}=y("getComments",{params:{post_id:r},enabled:!!r,staleTime:2*60*1e3,gcTime:5*60*1e3}),j=J(p,()=>{h(null)}),{mutate:S,isPending:A}=w("comments",{useFormData:!1,showSuccessNotification:!1,onSuccess:()=>{g.resetFields()},invalidateQueries:[{queryKey:["getComments"],type:"all"},{queryKey:["postItem"],type:"all"}]}),{mutate:L,isPending:is}=w("deletePost",{onSuccess:()=>{m("/contract")},invalidateQueries:[{queryKey:["postItem"],type:"all"}]}),l=x==null?void 0:x.data,a=(u==null?void 0:u.data)||[],T=e=>{var i;if(!((i=e.message)!=null&&i.trim())){G.warning("Please enter a comment");return}S({post_id:parseInt(r),message:e.message.trim()})},B=e=>{l&&(h(l),window.scrollTo({top:0,behavior:"smooth"}))},_=()=>{h(null),j.resetForm()},F=async e=>{(await I({title:"Are you sure?",text:"Are you sure you want to delete this post?",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, delete it!",cancelButtonText:"Cancel"})).isConfirmed&&L({slug:e,data:""})},z=e=>{var i,n,o;return((i=e==null?void 0:e.user)==null?void 0:i.id)===((n=window.user)==null?void 0:n.id)||(e==null?void 0:e.user_id)===((o=window.user)==null?void 0:o.id)},R=({comment:e})=>{var f,N,v;const{isLiked:i,likesCount:n,isToggling:o,handleLikeToggle:D}=Z(e==null?void 0:e.id,(e==null?void 0:e.is_liked)||!1,(e==null?void 0:e.likes_count)||0),M=Q=>{Q.stopPropagation(),D()};return s.jsx("div",{className:"comment-item mb-3",children:s.jsxs("div",{className:"d-flex align-items-start",children:[s.jsx("img",{src:(f=e==null?void 0:e.user)==null?void 0:f.image_url,alt:(N=e==null?void 0:e.user)==null?void 0:N.name,className:"rounded-circle me-3",width:40,height:40}),s.jsxs("div",{className:"flex-grow-1",children:[s.jsx("div",{className:"d-flex align-items-center mb-1",children:s.jsx("h6",{className:"mb-0 me-2",children:(v=e==null?void 0:e.user)==null?void 0:v.name})}),s.jsx("p",{className:"mb-2",children:e==null?void 0:e.message}),s.jsx("div",{className:"d-flex align-items-center",children:s.jsxs("div",{className:"d-flex align-items-center cursor-pointer py-1 px-2 rounded hover-bg-light",onClick:M,style:{color:i?"#1890ff":"#8c8c8c",opacity:o?.6:1,pointerEvents:o?"none":"auto",fontSize:"14px"},children:[i?s.jsx(es,{style:{fontSize:"16px",marginRight:"4px"}}):s.jsx(ts,{style:{fontSize:"16px",marginRight:"4px"}}),s.jsx("span",{style:{fontSize:"14px"},children:i?"Liked":"Like"}),n>0&&s.jsx("span",{className:"ms-2",style:{fontSize:"14px",color:"#8c8c8c"},children:n})]})})]})]})})};return P?s.jsx(d,{children:s.jsx("div",{className:"container-fluid",children:s.jsx("div",{className:"row",children:s.jsxs("div",{className:"col-12 mt-4",children:[s.jsxs("div",{className:"post-card sphere-card p-3 rounded border bg-white mb-4",children:[s.jsxs("div",{className:"d-flex align-items-center mb-3",children:[s.jsx(t.Avatar,{size:40}),s.jsxs("div",{className:"ms-3 flex-grow-1",children:[s.jsx(t.Input,{style:{width:120,height:16}}),s.jsx("div",{className:"mt-1",children:s.jsx(t.Input,{style:{width:80,height:12}})})]})]}),s.jsx(t.Image,{active:!0,className:"w-100 mb-3",style:{width:"100%",height:300}}),s.jsx(t,{paragraph:{rows:3}}),s.jsx("div",{className:"d-flex justify-content-between align-items-center mt-3",children:s.jsxs("div",{className:"d-flex gap-3",children:[s.jsx(t.Input,{style:{width:60,height:20}}),s.jsx(t.Input,{style:{width:60,height:20}}),s.jsx(t.Input,{style:{width:60,height:20}})]})})]}),s.jsxs("div",{className:"comments-section bg-white border rounded p-3",children:[s.jsx(t.Input,{style:{width:120,height:20},className:"mb-3"}),Array.from({length:3}).map((e,i)=>s.jsxs("div",{className:"d-flex align-items-start mb-3",children:[s.jsx(t.Avatar,{size:32}),s.jsxs("div",{className:"ms-3 flex-grow-1",children:[s.jsx(t.Input,{style:{width:100,height:14}}),s.jsx("div",{className:"mt-1",children:s.jsx(t,{paragraph:{rows:1,width:"80%"}})})]})]},i))]})]})})})}):E?s.jsx(d,{children:s.jsx("div",{className:"container-fluid",children:s.jsx("div",{className:"row",children:s.jsx("div",{className:"col-12 mt-4",children:s.jsx(c,{description:"Failed to load post. Please try again.",image:c.PRESENTED_IMAGE_SIMPLE,children:s.jsx(b,{type:"primary",onClick:()=>m("/contract"),children:"Back to Contract Q&A"})})})})})}):l?s.jsx(d,{children:s.jsx("div",{className:"container-fluid",children:s.jsxs("div",{className:"row post-detail",children:[p&&s.jsx("div",{className:"col-12",children:s.jsx(U,{postCreationHook:j,onCancelEdit:_})}),s.jsxs("div",{className:"col-12 mt-4",children:[s.jsx(Y,{...l,showActions:z(l),onEdit:B,onDelete:F}),s.jsxs("div",{className:"comments-section bg-white border rounded p-3 mt-3",children:[k?s.jsx("div",{children:Array.from({length:3}).map((e,i)=>s.jsxs("div",{className:"d-flex align-items-start mb-3",children:[s.jsx(t.Avatar,{size:40}),s.jsxs("div",{className:"ms-3 flex-grow-1",children:[s.jsx(t.Input,{style:{width:100,height:14}}),s.jsx("div",{className:"mt-1",children:s.jsx(t,{paragraph:{rows:1,width:"80%"}})})]})]},i))}):(a==null?void 0:a.length)>0?a==null?void 0:a.map(e=>s.jsx(R,{comment:e},e.id)):s.jsx("p",{className:"text-muted mb-3",children:"No comments yet. Be the first to comment!"}),s.jsx("div",{className:"mt-4",children:s.jsx(C,{form:g,onFinish:T,layout:"vertical",children:s.jsxs("div",{className:"d-flex align-items-end gap-2",children:[s.jsx("div",{className:"flex-grow-1",children:s.jsx(V,{name:"message",type:"text",placeholder:"Write a comment...",className:"w-100",style:{marginBottom:0}})}),s.jsx(ss,{type:"primary",htmlType:"submit",icon:s.jsx(X,{}),loading:A,className:"post-comment-btn",children:"Post"})]})})})]})]})]})})}):s.jsx(d,{children:s.jsx("div",{className:"container-fluid",children:s.jsx("div",{className:"row",children:s.jsx("div",{className:"col-12 mt-4",children:s.jsx(c,{description:"Post not found",image:c.PRESENTED_IMAGE_SIMPLE,children:s.jsx(b,{type:"primary",onClick:()=>m("/contract"),children:"Back to Contract Q&A"})})})})})})};export{Ss as default};
