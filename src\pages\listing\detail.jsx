import React from "react";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import InnerLayout from "@/components/shared/layout/innerlayout";
import { HeartFilled } from "@ant-design/icons";
import { Skeleton } from "antd";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import apiClient from "@/services/apiClient";

import PropertyCard from "@/components/shared/card/propertycard";
import SearchBar from "@/components/shared/inputs/searchbar";
import IconButton from "@/components/shared/button/iconbutton";
import MapComponent from "@/components/shared/map";
import ImageSlider from "@/components/shared/imageslider";
import { useQuery } from "@/hooks/reactQuery";

const ListingDetail = () => {
  const { id } = useParams();
  const location = useLocation();
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  // Fetch property data using the ID from URL
  const {
    data: response,
    isLoading,
    dataUpdatedAt,
    refetch,
  } = useQuery("getProperty", {
    slug: id,
    staleTime: 0, // Always consider data stale to ensure fresh data
    gcTime: 0, // Don't cache the data at all
    refetchOnMount: true, // Always refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window gains focus
  });

  // Extract property data from response
  const property = response?.data || response;
  console.log("property", property);
  // Clean and simple favorite mutation
  const favoriteMutation = useMutation({
    mutationFn: async (newFavoriteStatus) => {
      return await apiClient.request("addProperty", {
        slug: `${id}/favorite`,
        data: { value: newFavoriteStatus },
      });
    },
    onSuccess: () => {
      // Only invalidate queries - React Query will refetch when needed
      queryClient.invalidateQueries({
        queryKey: ["properties"],
        exact: false,
      });
      queryClient.invalidateQueries({
        queryKey: ["getProperty"],
        exact: false,
      });
    },
  });

  const handleFavoriteClick = (e) => {
    e.preventDefault();
    e.stopPropagation();

    // Prevent multiple clicks while API call is in progress
    if (favoriteMutation.isPending) return;

    const newValue = !property.is_favorite;
    favoriteMutation.mutate(newValue);
  };

  // Force refetch when component mounts, ID changes, or when navigating back from edit
  React.useEffect(() => {
    refetch();
  }, [id, location.pathname, refetch]);

  // Listen for storage events that might indicate property updates
  React.useEffect(() => {
    const handleStorageChange = (e) => {
      if (e.key === `property_updated_${id}`) {
        refetch();
      }
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, [id, refetch]);

  // Helper function to remove decimal places
  const formatNumber = (value) => {
    if (!value) return value;
    const num = parseFloat(value);
    return isNaN(num) ? value : Math.floor(num).toString();
  };

  // Helper function to format price
  const formatPrice = (price) => {
    if (!price) return "$0";
    const numPrice = parseFloat(price);
    if (isNaN(numPrice)) return price;
    return `${Math.floor(numPrice).toLocaleString()}`;
  };
  const handleMessageClick = (e) => {
    e.stopPropagation(); // Prevent card click navigation
    // Navigate to chat with agent info
    navigate("/inbox", {
      state: {
        directMessage: true,
        targetUser: property?.user,
      },
    });
  };
  if (isLoading) {
    return (
      <InnerLayout>
        <div className="container-fluid">
          <div className="row mt-5">
            <div className="col-12">
              <Skeleton active paragraph={{ rows: 10 }} />
            </div>
          </div>
        </div>
      </InnerLayout>
    );
  }

  // Create dynamic section data based on property data
  const dynamicSectionData = {
    leftItems: [
      {
        label: "Basement",
        value: property.basement ? "Yes" : "No",
        icon: "/assets/img/base-1.png",
      },
      {
        label: "Garage",
        value: formatNumber(property.garage) || "No",
        icon: "/assets/img/base-2.png",
      },
      {
        label: "Lot Size",
        value: property.lot_size
          ? `${formatNumber(property.lot_size)} ${property.lot_type || "sqft"}`
          : "No",
        icon: "/assets/img/base-3.png",
      },
      {
        label: "Property Type",
        value: property.type?.name || "No",
        icon: "/assets/img/base-4.png",
      },
      {
        label: "Zip Code",
        value: property.zip || "No",
        icon: "/assets/img/base-5.png",
      },
      {
        label: "HOA",
        value: property.hoa
          ? property.hoa_price
            ? `${formatNumber(property.hoa_price)}/${
                property.hoa_type || "month"
              }`
            : "Yes"
          : "No",
        icon: "/assets/img/base-6.png",
      },
      {
        label: "Buy-Side Compensation",
        value: property.buy_side_compensation
          ? `${property.buy_side_compensation} ${
              property.buy_side_compensation_type || ""
            }`
          : "No",
        icon: "/assets/img/buy-side.png",
      },
    ],
    rightItems: [
      {
        label: "State",
        value: property.state || "No",
        icon: "/assets/img/base-7.png",
      },
      {
        label: "City",
        value: property.city || "No",
        icon: "/assets/img/base-7.png",
      },
      {
        label: "Property Size",
        value: property.size
          ? `${formatNumber(property.size)} ${property.size_type || "sqft"}`
          : "No",
        icon: "/assets/img/base-3.png",
      },
      {
        label: "Home Style",
        value: property.home_style?.name || "No",
        icon: "/assets/img/base-8.png",
      },
      {
        label: "Year Built",
        value: formatNumber(property.year_built) || "No",
        icon: "/assets/img/base-9.png",
      },
      {
        label: "MLS ID",
        value: property.mls_id || "No",
        icon: "/assets/img/base-10.png",
      },
      {
        label: "Phone",
        value: property.mobile_no,
        icon: "/assets/img/phone-rounded.png",
      },
    ],
  };

  return (
    <InnerLayout>
      <div className="container-fluid">
        <div className="row mt-5">
          <div className="col-12">
            <div className="text-end fav-icon">
              <div
                onClick={handleFavoriteClick}
                style={{
                  cursor: favoriteMutation.isPending
                    ? "not-allowed"
                    : "pointer",
                  opacity: favoriteMutation.isPending ? 0.7 : 1,
                  transition: "opacity 0.2s ease",
                  display: "inline-block",
                }}
              >
                <img
                  src={
                    property.is_favorite
                      ? "/assets/img/heart-active.png"
                      : "/assets/img/heart-icon.png"
                  }
                  alt="favorite"
                />
              </div>
            </div>
            <div className="d-flex align-items-center justify-content-between">
              <div>
                <p className="font-32 font-600">
                  {property.name ||
                    `${formatNumber(property.bed)} Bed ${formatNumber(
                      property.bath
                    )} Bath ${property.type?.name || "Property"}`}
                </p>
                <p>
                  {property.address
                    ? `${property.address}, ${property.city}, ${property.state}`
                    : `${property.city || ""}, ${property.state || ""}`}
                </p>
              </div>
              <div className="text-end">
                <p className="font-32 font-600">
                  ${formatPrice(property.price)}
                </p>
                <p className="font-18">
                  {property.size && property.price
                    ? `$${formatNumber(
                        parseFloat(property.price) / parseFloat(property.size)
                      )} /${property.size_type}`
                    : ""}
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="row mt-3">
          <div className="col-12 col-sm-7 col-md-7 col-lg-8 col-xl-12">
            <ImageSlider
              images={property?.images || []}
              alt={property?.name || "Property"}
              className="property-image-slider"
            />
          </div>
        </div>
        <div className="row mt-4">
          <div className="col-12 col-sm-7">
            <div className="col-12 col-md-11">
              <p className="font-24 font-600 mb-3">Property details</p>
              <div className="property-detail-area">
                <div className="d-flex align-items-center">
                  <div>
                    <img src="/assets/img/area-icon.png" alt="" />
                  </div>
                  <div className="ms-2">
                    <p className="font-16">Total area</p>
                    <p className="font-14">
                      {formatNumber(property.size) || "N/A"}{" "}
                      {property.size_type || "Sq. Ft"}
                    </p>
                  </div>
                </div>
                <div className="d-flex align-items-center">
                  <div>
                    <img src="/assets/img/beddetail-icon.png" alt="" />
                  </div>
                  <div className="ms-2">
                    <p className="font-16">Bedrooms</p>
                    <p className="font-14">
                      {formatNumber(property.bed) || "N/A"}
                    </p>
                  </div>
                </div>
                <div className="d-flex align-items-center">
                  <div>
                    <img src="/assets/img/bath-tub-icon.png" alt="" />
                  </div>
                  <div className="ms-2">
                    <p className="font-16">Bathrooms</p>
                    <p className="font-14">
                      {formatNumber(property.bath) || "N/A"}
                    </p>
                  </div>
                </div>
              </div>
              <div className="row mt-5  ">
                <div className="col-12 col-md-6">
                  {dynamicSectionData.leftItems.map((item, index) => (
                    <div
                      key={index}
                      className="d-flex align-items-center justify-content-between item-border-bottom mb-2 pb-2"
                    >
                      <div className="d-flex align-items-center">
                        <div className="base-img">
                          <img src={item.icon} alt={item.label} />
                        </div>
                        <div className="ms-2">
                          <p className="color-grey">{item.label}</p>
                        </div>
                      </div>
                      <div>
                        <p className="color-black">{item.value}</p>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="col-12 col-md-6">
                  {dynamicSectionData.rightItems.map((item, index) => (
                    <div
                      key={index}
                      className="d-flex align-items-center justify-content-between item-border-bottom mb-2 pb-2"
                    >
                      <div className="d-flex align-items-center">
                        <div className="base-img">
                          <img src={item.icon} alt={item.label} />
                        </div>
                        <div className="ms-2">
                          <p className="color-grey">{item.label}</p>
                        </div>
                      </div>
                      <div>
                        <p className="color-black">{item.value}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              <div className="row mt-4 mb-5">
                <div className="col-12">
                  <p className="font-24 font-600 color-black">Description</p>
                  <p className="color-grey font-16 mt-2">
                    {property.description || "No description available."}
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div className="col-12 col-sm-5">
            <div className="mt-5">
              <div className="profile-detail-area">
                <div className="d-flex align-items-center mb-3">
                  <div className="profile">
                    <img src={property?.user?.image_url} alt="" />
                  </div>
                  <div className="ms-3 detail">
                    <p className="font-16">
                      {property?.user?.name || "Agent Name"}
                    </p>
                    <p className="white-light">
                      {window.helper.getLabel(
                        "professional_type",
                        property?.user?.professional_type
                      )}
                    </p>
                  </div>
                </div>
                <div className="d-flex align-items-center">
                  <div className="me-3">
                    <IconButton
                      icon={<img src="/assets/img/call-icon.png" />}
                      title={property?.user?.mobile_no || "***** 456789"}
                      className="number-button"
                    />
                  </div>
                  <div className="me-3">
                    <IconButton
                      icon={<img src="/assets/img/phone-rounded.png" />}
                      title="Call"
                      className="number-button"
                    />
                  </div>
                  <div className="me-3">
                    <IconButton
                      icon={<img src="/assets/img/chat.png" />}
                      title="Chat"
                      className="number-button"
                      onClick={handleMessageClick}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </InnerLayout>
  );
};

export default ListingDetail;
