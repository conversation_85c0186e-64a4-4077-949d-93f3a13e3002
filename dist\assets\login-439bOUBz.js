import{r as i,u as C,j as a,L as w,n as m,H as p,g as F,a as S,s as T,b,c as G}from"./index-Dklazue-.js";import{A as P}from"./index-CB9TBJHF.js";import{B as x,c as k,C as O,v as d}from"./index-BUt89ETK.js";import{F as U}from"./flatbutton-B_tUS4QM.js";import{u as A}from"./useMutation-BrUrPIzr.js";import{F as y}from"./react-stripe.esm-ypQSOYN5.js";import"./index-CjGjc6T5.js";import"./button-DNhBCuue.js";import"./index-CHbHgJvR.js";import"./useLocale-BNhrTARD.js";const M=()=>{const f=C(),[n]=y.useForm(),[h,I]=i.useState(!1),[j,_]=i.useState({}),[r,v]=i.useState(null),[c,s]=i.useState(!1);i.useEffect(()=>{(async()=>{try{const t=await m.init();v(t)}catch{const l=`web_init_error_${Date.now()}`;v(l),localStorage.setItem("device_token",l)}})(),localStorage.getItem("googleSignInLoading")==="true"&&s(!0)},[]);const{mutate:g,isPending:u}=A("login",{useFormData:!1,onSuccess:async e=>{if(e){await p.setStorageData("session",e.data),window.user=e.data,localStorage.removeItem("googleSignupData"),localStorage.removeItem("googleUserData");const o=n.getFieldsValue();h?await p.setStorageData("rememberedCredentials",{email:o.email,password:o.password,rememberMe:!0,savedAt:Date.now()}):localStorage.removeItem("rememberedCredentials"),f("/home",{replace:!0})}},onError:(e,o)=>{if(console.log("error",e),o.type==="social"&&(e==null?void 0:e.status)===400){const t=JSON.parse(localStorage.getItem("googleUserData")||"{}");if(t.email){localStorage.setItem("googleSignupData",JSON.stringify({email:t.email,full_name:t.displayName||t.name||"",social_id:t.uid,social_platform:"google"})),localStorage.removeItem("googleUserData"),f("/signup",{replace:!0});return}}localStorage.removeItem("googleSignInLoading"),s(!1)}});i.useEffect(()=>{(async()=>{try{const o=await p.getStorageData("rememberedCredentials");if(o&&o.email){const t={email:o.email,password:o.password||""};_(t),n.setFieldsValue(t),I(!0)}}catch{}})()},[n]),i.useEffect(()=>{(async()=>{try{const o=await F(S);if(o){const t=o.user;let l=r||m.getToken()||localStorage.getItem("device_token");l||(l=`web_final_fallback_${Date.now()}`,localStorage.setItem("device_token",l)),localStorage.setItem("googleUserData",JSON.stringify({uid:t.uid,email:t.email,displayName:t.displayName,name:t.displayName}));const R={type:"social",social_id:t.uid,social_platform:"google",email:t.email,device:"web",device_token:l};localStorage.removeItem("googleSignInLoading"),g(R)}else localStorage.getItem("googleSignInLoading")==="true"&&(localStorage.removeItem("googleSignInLoading"),s(!1))}catch(o){console.error("Google Sign-In Redirect Error:",o),localStorage.removeItem("googleSignInLoading"),s(!1)}})()},[r,g]);const N=e=>{let o=r||m.getToken()||localStorage.getItem("device_token");o||(o=`web_final_fallback_${Date.now()}`,localStorage.setItem("device_token",o));const t={...e,device:"web",device_token:o,type:"email"};g(t)},D=e=>{const o=e.target.checked;I(o),o||localStorage.removeItem("rememberedCredentials")},L=async e=>{let o=r||m.getToken()||localStorage.getItem("device_token");o||(o=`web_final_fallback_${Date.now()}`,localStorage.setItem("device_token",o)),localStorage.setItem("googleUserData",JSON.stringify({uid:e.uid,email:e.email,displayName:e.displayName,name:e.displayName}));const t={type:"social",social_id:e.uid,social_platform:"google",email:e.email,device:"web",device_token:o};localStorage.removeItem("googleSignInLoading"),s(!1),g(t)},E=async()=>{s(!0);try{const e=await T(S,b);await L(e.user)}catch(e){if(console.log("Popup failed, trying redirect:",e.code),e.code==="auth/popup-blocked"||e.code==="auth/popup-closed-by-user"||e.code==="auth/cancelled-popup-request"||e.message.includes("Cross-Origin-Opener-Policy"))try{localStorage.setItem("googleSignInLoading","true"),await G(S,b)}catch(o){console.error("Both popup and redirect failed:",o),s(!1),localStorage.removeItem("googleSignInLoading")}else console.error("Google Sign-In Error:",e),s(!1),localStorage.removeItem("googleSignInLoading")}};return a.jsxs(P,{showSidebar:!1,children:[a.jsx("div",{className:"text-center logo mb-3",children:a.jsx("img",{src:"/assets/img/suscribtion-img.png",alt:"Auth Logo"})}),a.jsxs("div",{className:"col-12 text-center",children:[a.jsx("h1",{className:"font-46 color-black mb-1",children:"SPHERE"}),a.jsx("h1",{className:"font-46 color-black mb-1",children:"Real Estate Network"}),a.jsx("p",{children:"Log in to continue using your account"})]}),a.jsxs(y,{form:n,name:"login",layout:"vertical",onFinish:N,initialValues:j,autoComplete:"off",children:[a.jsx(x,{name:"email",placeholder:"Email",label:"Email Address",rules:k("email",d.required,d.email)}),a.jsx(x,{type:"password",name:"password",placeholder:"Password",label:"Password",rules:k("password",d.required,d.password)}),a.jsxs("div",{className:"d-flex align-items-center justify-content-between mt-0",children:[a.jsx("div",{className:"mt-2 check-item",children:a.jsx(O,{checked:h,onChange:D,children:"Remember me"})}),a.jsx("div",{children:a.jsx(w,{to:"/forgetpassword",className:"mt-1 font-600 font-14 d-block color-blue ",children:"Forgot Password?"})})]}),a.jsx("div",{children:a.jsx(U,{title:u?"Sign In...":"Sign In",className:"mx-auto mt-4 signin-btn mt-5",htmlType:"submit",loading:u,disabled:u})}),a.jsx("div",{className:"login-p",children:a.jsx("p",{children:"Or login with"})}),a.jsxs("div",{className:"socail-login",onClick:E,style:{cursor:c?"not-allowed":"pointer",opacity:c?.6:1,pointerEvents:c?"none":"auto"},children:[a.jsx("div",{children:a.jsx("img",{src:"/assets/img/google.png",alt:""})}),a.jsx("div",{children:a.jsx("p",{children:c?"Signing in...":"Sign in with Google"})})]}),a.jsx("div",{children:a.jsxs("p",{className:"signup-text",children:["Don't have an account?",a.jsx(w,{to:"/signup",className:"color-blue font-600 font-16 ms-1",children:"Sign up"})]})})]})]})},X=i.memo(M);export{X as default};
