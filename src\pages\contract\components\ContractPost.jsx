import React from "react";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { Dropdown, Menu } from "antd";
import {
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
  LikeOutlined,
  LikeFilled,
  MessageOutlined,
} from "@ant-design/icons";
import useLikePost from "@/hooks/useLikePost";
import { useNavigate } from "react-router-dom";

dayjs.extend(relativeTime);

const ContractPost = ({
  id,
  user,
  created_at,
  content,
  image,
  video,
  likes_count = 0,
  comments_count = 0,
  reposts_count = 0,
  original_post,
  time,
  question,
  body,
  likes,
  comments,
  shares,
  reactions,
  onClick,
  img,
  type,
  onEdit,
  onDelete,
  showActions = false,
  is_liked = false,
  user_id,
}) => {
  const navigate = useNavigate();
  const isSharedPost = !!original_post;

  const displayPost = isSharedPost
    ? original_post
    : {
        id,
        user,
        created_at,
        content,
        image,
        video,
        likes_count,
        comments_count,
        reposts_count,
      };

  const sharedByUser = isSharedPost ? user : null;
  const originalAuthor = isSharedPost ? original_post.user : user;

  const postUser = originalAuthor;
  const postContent = content || question || body;
  const postImage = image || img;
  const postComments = comments_count ?? comments ?? 0;

  // Use the original post ID for likes if it's a shared post
  const likePostId = id;
  const initialLiked = isSharedPost ? original_post.is_liked : is_liked;
  const initialLikesCount = isSharedPost
    ? likes_count
    : likes_count ?? likes ?? 0;

  // Use the like hook
  const { isLiked, likesCount, isToggling, handleLikeToggle } = useLikePost(
    likePostId,
    initialLiked,
    initialLikesCount
  );

  const isCurrentUserPost =
    (originalAuthor?.id || originalAuthor?.user_id) === window.user.id;

  const formatTimeWithLocation = (timestamp, userObj) => {
    const timeAgo = timestamp ? dayjs(timestamp).fromNow() : time || "Just now";
    const location = userObj?.state || userObj?.location?.state || "";

    if (location) {
      return `${location} · ${timeAgo}`;
    }
    return timeAgo;
  };

  const menuItems = [];

  if (isCurrentUserPost) {
    menuItems.push(
      {
        key: "edit",
        icon: <EditOutlined />,
        label: "Edit",
      },
      {
        key: "delete",
        icon: <DeleteOutlined />,
        label: "Delete",
      }
    );
  }

  const handleMenuClick = (e) => {
    e.domEvent?.stopPropagation();

    if (e.key === "edit") {
      // For reposted posts, use the original post's ID for editing
      const editId = isSharedPost ? original_post.id : id;
      onEdit && onEdit(editId);
    } else if (e.key === "delete") {
      // For delete, always use the current post's ID (repost ID for reposts, original ID for originals)
      onDelete && onDelete(id);
    }
  };

  const handleDropdownClick = (e) => {
    e.stopPropagation();
  };

  const handleLikeClick = (e) => {
    e.stopPropagation();
    handleLikeToggle();
  };

  // Profile picture click handlers - show Anonymous for contract posts
  const handleUserProfileClick = (e) => {
    e.stopPropagation();
    // Don't navigate for anonymous posts
  };

  return (
    <div
      className="post-card sphere-card p-3 rounded border bg-white d-flex flex-column "
      onClick={onClick}
    >
      {isSharedPost ? (
        <>
          <div className="d-flex align-items-start mb-2 pb-2 border-bottom">
            <div className="d-flex align-items-center">
              <img
                src="/assets/img/logo.png"
                alt="Anonymous"
                className="rounded-circle"
                width={40}
                height={40}
                style={{ cursor: "default" }}
              />
              <div className="ms-2">
                <h6 className="mb-0">
                  Anonymous
                  <span className="color-light font-14">Repost this</span>
                </h6>
                <small className="text-muted">
                  {formatTimeWithLocation(created_at, {})}
                </small>
              </div>
            </div>
          </div>

          <div className="d-flex justify-content-between align-items-center py-2">
            <div className="d-flex align-items-center">
              <img
                src="/assets/img/logo.png"
                alt="Anonymous"
                className="rounded-circle"
                width={40}
                height={40}
                style={{ cursor: "default" }}
              />
              <div className="ms-2">
                <h6 className="mb-0">Anonymous</h6>
                <small className="text-muted">
                  {formatTimeWithLocation(original_post?.created_at, {})}
                </small>
              </div>
            </div>
            {isCurrentUserPost && (
              <Dropdown
                menu={{
                  items: menuItems,
                  onClick: handleMenuClick,
                }}
                trigger={["click"]}
                placement="bottomRight"
              >
                <MoreOutlined
                  style={{
                    fontSize: "20px",
                    cursor: "pointer",
                    padding: "4px",
                    borderRadius: "50%",
                  }}
                  onClick={handleDropdownClick}
                />
              </Dropdown>
            )}
          </div>
        </>
      ) : (
        <div className="d-flex justify-content-between align-items-center pb-2 border-bottom">
          <div className="d-flex align-items-center">
            <img
              src="/assets/img/logo.png"
              alt="Anonymous"
              className="rounded-circle"
              width={40}
              height={40}
              style={{ cursor: "default" }}
            />
            <div className="ms-2">
              <h6 className="mb-0">Anonymous</h6>
              <small className="text-muted">
                {formatTimeWithLocation(created_at, {})}
              </small>
            </div>
          </div>
          {isCurrentUserPost && (
            <Dropdown
              menu={{
                items: menuItems,
                onClick: handleMenuClick,
              }}
              trigger={["click"]}
              placement="bottomRight"
            >
              <MoreOutlined
                style={{
                  fontSize: "20px",
                  cursor: "pointer",
                  padding: "4px",
                  borderRadius: "50%",
                }}
                onClick={handleDropdownClick}
              />
            </Dropdown>
          )}
        </div>
      )}

      {displayPost.video ? (
        <div
          className={
            isSharedPost ? "sphere-post mb-3" : "sphere-post mb-3 mt-2"
          }
        >
          <video
            src={displayPost.video}
            controls
            className="w-100 rounded"
            style={{ height: "100%", objectFit: "cover" }}
          />
        </div>
      ) : (
        (displayPost.image || postImage) && (
          <div
            className={
              isSharedPost ? "sphere-post mb-3" : "sphere-post mb-3 mt-2"
            }
          >
            <img
              src={displayPost.image || postImage}
              alt="Post content"
              className="img-fluid"
            />
          </div>
        )
      )}
      <div className="mt-4">
        <h4 className="font-18">{question?.question}</h4>
      </div>
      <div className="mb-3 mt-3">
        <p>{displayPost.content || postContent}</p>
      </div>
      <div className="card-footer">
        <div
          className="d-flex align-items-center justify-content-between text-muted mb-2"
          style={{ fontSize: 14 }}
        >
          <div>
            <span className="me-3">{likesCount} Likes</span>
          </div>
          <div>
            <span className="me-3">{postComments} Comments</span>
          </div>
        </div>

        <div
          className="d-flex align-items-center justify-content-between text-muted action-box"
          style={{ fontSize: 14 }}
        >
          <div
            className="d-flex align-items-center cursor-pointer py-2 px-3 rounded hover-bg-light"
            onClick={handleLikeClick}
            style={{
              color: isLiked ? "#1890ff" : undefined,
              opacity: isToggling ? 0.6 : 1,
              pointerEvents: isToggling ? "none" : "auto",
            }}
          >
            {isLiked ? (
              <LikeFilled style={{ fontSize: "18px", marginRight: "8px" }} />
            ) : (
              <LikeOutlined style={{ fontSize: "18px", marginRight: "8px" }} />
            )}
            <span>{isLiked ? "Liked" : "Like"}</span>
          </div>
          <div className="d-flex align-items-center cursor-pointer py-2 px-3 rounded hover-bg-light">
            <MessageOutlined style={{ fontSize: "18px", marginRight: "8px" }} />
            <span>Comment</span>
          </div>
          {/* Share button is intentionally removed for contract Q&A posts */}
        </div>
      </div>
    </div>
  );
};

export default ContractPost;
