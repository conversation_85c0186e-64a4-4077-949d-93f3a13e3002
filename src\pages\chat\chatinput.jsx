import React, { useState } from "react";
import { Input } from "antd";

const { TextArea } = Input;

const ChatInput = ({
  placeholder = "Type a message...",
  onSendMessage,

  disabled = false,
}) => {
  const [message, setMessage] = useState("");

  const handleInputChange = (e) => {
    const value = e.target.value;
    setMessage(value);
  };

  const handleSendMessage = () => {
    if (message.trim() && !disabled) {
      onSendMessage(message);
      setMessage("");
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div
      style={{
        maxWidth: "100%",
        border: "1px solid #ddd",
        borderRadius: "10px",
      }}
      className="mt-4"
    >
      <div
        style={{
          backgroundColor: "#fff",
          padding: "10px",
          borderRadius: "10px",
        }}
      >
        <TextArea
          value={message}
          onChange={handleInputChange}
          onKeyPress={handleKeyPress}
          placeholder={placeholder}
          autoSize={{ minRows: 1, maxRows: 3 }}
          style={{
            border: "none",
            resize: "none",
            marginBottom: "10px",
            background: "transparent",
          }}
          disabled={disabled}
        />

        <div className="d-flex justify-content-end">
          <img
            src="/assets/img/chat-send-icon.png"
            alt="Send"
            style={{
              cursor: disabled || !message.trim() ? "not-allowed" : "pointer",
              opacity: disabled || !message.trim() ? 0.5 : 1,
            }}
            onClick={handleSendMessage}
          />
        </div>
      </div>
    </div>
  );
};

export default ChatInput;
