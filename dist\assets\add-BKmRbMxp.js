import{r as j,h as L,j as e,q as se,u as te,f as ae,o as re,i as ie}from"./index-Dklazue-.js";import{I as oe}from"./index-vmMMvWhJ.js";import{B as m,c as u,P as ne,D as ce,v as n}from"./index-BUt89ETK.js";import{F as D}from"./flatbutton-B_tUS4QM.js";import{u as E}from"./useMutation-BrUrPIzr.js";import{U as de}from"./index-Vj938NLd.js";import{R as $}from"./InboxOutlined-Cxk4AEr6.js";import{B as me}from"./button-DNhBCuue.js";import{R as ue}from"./DeleteOutlined-BjE_e6LE.js";import{u as pe}from"./useStartupData-QD8kuEYy.js";import{u as he}from"./useLocationData-CQHRFCOb.js";import{u as ge}from"./useQuery-C3n1GVcJ.js";import{F as B}from"./react-stripe.esm-ypQSOYN5.js";import"./index-Cj6uPc4c.js";import"./index-CjGjc6T5.js";import"./index-CHbHgJvR.js";import"./useLocale-BNhrTARD.js";import"./fade-B36sOBLs.js";const{Dragger:ye}=de,be=({onChange:x,multiple:w=!0,maxCount:o=10,value:d=[],type:Q="property_image"})=>{const[y,v]=j.useState([]),[P,f]=j.useState(new Set),{mutate:_,isPending:R}=E("fileUpload",{useFormData:!0,showSuccessNotification:!1,onSuccess:(l,s)=>{const{file:r,fileIndex:i}=s;console.log("File upload success:",l),v(p=>{const b=p.map(c=>{var h;return c.uid===r.uid?{...c,status:"done",uploadedId:((h=l.data)==null?void 0:h.id)||l.id,response:l}:c});return f(c=>{const h=new Set(c);return h.delete(r.uid),h}),F(b),b})},onError:(l,s)=>{const{file:r}=s;console.error("File upload error:",l),L.error(`Failed to upload ${r.name}`),v(i=>{const p=i.map(b=>b.uid===r.uid?{...b,status:"error",error:l.message||"Upload failed"}:b);return f(b=>{const c=new Set(b);return c.delete(r.uid),c}),p})}}),F=l=>{if(x){const s=l.filter(i=>i.uploadedId).map(i=>({id:i.uploadedId,name:i.name,url:i.url||i.thumbUrl})),r=l.filter(i=>!i.isNew&&!i.uploadedId&&(i.id||i.url)).map(i=>({id:i.id,name:i.name,url:i.url||i.thumbUrl}));x([...r,...s])}};j.useEffect(()=>{if(console.log("DraggerUpload: Initializing with value:",d),d&&d.length>0){const l=d.map((s,r)=>{console.log(`Processing file ${r}:`,s);let i={uid:s.id?`existing-${s.id}`:`existing-${r}`,name:s.name||s.original_name||`Image ${r+1}`,status:"done",id:s.id};if(typeof s=="string")i.url=s,i.thumbUrl=s,console.log(`File ${r} is string URL:`,s);else if(s.url)i.url=s.url,i.thumbUrl=s.url,console.log(`File ${r} has url property:`,s.url);else if(s.image_url)i.url=s.image_url,i.thumbUrl=s.image_url,console.log(`File ${r} has image_url property:`,s.image_url);else if(s instanceof File)try{i.thumbUrl=URL.createObjectURL(s),i.originFileObj=s,console.log(`File ${r} is File object, created blob URL:`,i.thumbUrl)}catch(p){console.error(`Error creating object URL for file ${r}:`,p)}else console.warn(`File ${r} format not recognized:`,s);return i});v(l)}else console.log("No value provided, clearing fileList"),v([])},[d]);const S=y.length>=o,C={name:"file",multiple:w,maxCount:o,disabled:S,beforeUpload:(l,s)=>y.length>=o?(L.error(`Maximum ${o} images allowed!`),!1):l.type.startsWith("image/")?l.size/1024/1024<5?(y.length+s.length>o&&L.error(`You can only upload up to ${o} images!`),!1):(L.error("Image must be smaller than 5MB!"),!1):(L.error("You can only upload image files!"),!1),onChange:l=>{const{fileList:s}=l,i=s.filter(c=>{var O;if(c.status==="error")return!1;const h=c.originFileObj||c,N=(O=h.type)==null?void 0:O.startsWith("image/"),U=(h.size||0)/1024/1024<5;return N&&U}).map(c=>{const h=c.originFileObj||c;return{uid:c.uid,name:c.name||h.name,status:"uploading",originFileObj:h,thumbUrl:URL.createObjectURL(h),isNew:!0}}),b=[...y.filter(c=>!c.isNew),...i];v(b),i.forEach((c,h)=>{if(c.originFileObj instanceof File){f(U=>new Set(U).add(c.uid));const N=new FormData;N.append("file",c.originFileObj),N.append("type",Q),_({data:N,file:c,fileIndex:h})}})},showUploadList:!1,fileList:[],onDrop(l){console.log("Dropped files",l.dataTransfer.files)}},I=l=>{const s=y.filter(r=>r.uid!==l.uid);v(s),l.thumbUrl&&l.thumbUrl.startsWith("blob:")&&URL.revokeObjectURL(l.thumbUrl),f(r=>{const i=new Set(r);return i.delete(l.uid),i}),F(s),x&&x(s.length>0?s.filter(r=>r.uploadedId||!r.isNew&&r.id).map(r=>({id:r.uploadedId||r.id,name:r.name,url:r.url||r.thumbUrl})):[],l)};return j.useEffect(()=>()=>{y.forEach(l=>{l.thumbUrl&&l.thumbUrl.startsWith("blob:")&&URL.revokeObjectURL(l.thumbUrl)})},[]),e.jsxs(e.Fragment,{children:[e.jsxs(ye,{...C,style:{height:200,opacity:S?.5:1},children:[e.jsx("p",{className:"ant-upload-drag-icon mt-4 ",children:e.jsx($,{})}),S?e.jsxs(e.Fragment,{children:[e.jsxs("p",{className:"ant-upload-text mb-3",style:{color:"#ff4d4f"},children:["Maximum ",o," images reached"]}),e.jsx("p",{className:"or-line",style:{color:"#999"},children:"Remove an image to upload more"})]}):e.jsxs(e.Fragment,{children:[e.jsx("p",{className:"ant-upload-text mb-3",children:"Drag your file(s) to start uploading"}),e.jsx("p",{className:"or-line",children:"Or"}),e.jsx(D,{title:"Browse File",className:"browse-file mt-3 mb-3",disabled:S})]})]}),y.length>0&&e.jsxs("div",{style:{marginTop:20},children:[e.jsxs("div",{style:{marginBottom:10,fontWeight:"bold",color:S?"#ff4d4f":y.length>=o-1?"#faad14":"#000"},children:["Selected Images (",y.length,"/",o,")",S&&e.jsx("span",{style:{fontSize:"12px",marginLeft:"8px",color:"#ff4d4f"},children:"- Maximum reached"})]}),y.map(l=>e.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:8,border:"1px solid #ddd",padding:10,borderRadius:4,backgroundColor:"#fafafa"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[l.thumbUrl||l.url?e.jsxs(e.Fragment,{children:[e.jsx("img",{src:l.thumbUrl||l.url,alt:l.name,style:{width:50,height:50,marginRight:10,objectFit:"cover",borderRadius:4,border:"1px solid #ddd"},onError:s=>{console.error("Failed to load image:",l.thumbUrl||l.url),s.target.style.display="none",s.target.nextSibling.style.display="flex"}}),e.jsx("div",{style:{display:"none",width:50,height:50,marginRight:10,backgroundColor:"#f0f0f0",borderRadius:4,alignItems:"center",justifyContent:"center"},children:e.jsx($,{style:{fontSize:20,color:"#999"}})})]}):e.jsx("div",{style:{display:"flex",width:50,height:50,marginRight:10,backgroundColor:"#f0f0f0",borderRadius:4,alignItems:"center",justifyContent:"center"},children:e.jsx($,{style:{fontSize:20,color:"#999"}})}),e.jsxs("div",{style:{flex:1},children:[e.jsx("div",{style:{fontWeight:500},children:l.name}),e.jsx("div",{style:{fontSize:12,color:"#666"},children:l.status==="uploading"?e.jsxs("span",{style:{color:"#1890ff"},children:[e.jsx(se,{style:{marginRight:4}}),"Uploading..."]}):l.status==="error"?e.jsxs("span",{style:{color:"#ff4d4f"},children:["Upload failed: ",l.error]}):l.status==="done"&&l.uploadedId?e.jsx("span",{style:{color:"#52c41a"},children:"Upload complete"}):l.isNew?"New upload":""})]})]}),e.jsx(me,{type:"text",danger:!0,icon:e.jsx(ue,{}),onClick:()=>I(l),size:"small",title:"Remove image",disabled:P.has(l.uid)})]},l.uid))]})]})},Ce=()=>{var Y,X;const[x]=B.useForm(),w=te(),{id:o}=ae(),d=re(),[Q,y]=j.useState(!0),[v,P]=j.useState(!0),[f,_]=j.useState([]),[R,F]=j.useState([]),[S,C]=j.useState([]),{data:I,loading:l}=pe(),s=I==null?void 0:I.data,{data:r,isLoading:i}=ge("properties",{slug:o,staleTime:5*60*1e3,gcTime:10*60*1e3,enabled:!!o}),p=r==null?void 0:r.data,{selectedState:b,stateOptions:c,cityOptions:h,statesLoading:N,citiesLoading:U,handleStateChange:O,updateSelectedState:K}=he(),{mutate:k,isPending:H}=E("addProperty",{useFormData:!1,onSuccess:async t=>{var g;await d.invalidateQueries({queryKey:["properties"],exact:!1}),await d.invalidateQueries({queryKey:["getProperty"],exact:!1}),await d.invalidateQueries({queryKey:["getUser"],exact:!1}),d.removeQueries({queryKey:["getProperty"],exact:!1});const a=((g=t==null?void 0:t.data)==null?void 0:g.id)||(t==null?void 0:t.id);a&&(console.log("Invalidating specific property query for ID:",a),await d.invalidateQueries({queryKey:["getProperty",a.toString()]}),await d.invalidateQueries({queryKey:["getProperty",a]})),await d.refetchQueries({queryKey:["properties"]}),localStorage.setItem(`property_updated_${o}`,Date.now().toString()),localStorage.removeItem(`property_updated_${o}`),t&&w("/listing")}}),{mutate:V,isPending:W}=E("updateProperty",{useFormData:!1,onSuccess:async t=>{await d.invalidateQueries({queryKey:["properties"],exact:!1}),await d.invalidateQueries({queryKey:["getProperty"],exact:!1}),await d.invalidateQueries({queryKey:["getUser"],exact:!1}),d.removeQueries({queryKey:["getProperty"],exact:!1}),o&&(console.log("Invalidating specific property query for ID:",o),d.removeQueries({queryKey:["getProperty",o.toString()]}),d.removeQueries({queryKey:["getProperty",o]}),await d.invalidateQueries({queryKey:["getProperty",o.toString()],exact:!1}),await d.invalidateQueries({queryKey:["getProperty",o],exact:!1}),await d.refetchQueries({queryKey:["getProperty",o.toString()],type:"active"}),await d.refetchQueries({queryKey:["getProperty",o],type:"active"})),await d.refetchQueries({queryKey:["properties"]}),t&&w("/listing")}}),T=H||W,G=t=>{O(t,x)},Z=[{value:"$",label:"$"},{value:"%",label:"%"}],A=[{value:"sqft",label:"Sq.Ft"},{value:"acres",label:"Acres"}],M=Array.from({length:10},(t,a)=>({value:a+1,label:a+1})),J=Array.from({length:10},(t,a)=>({value:a+1,label:`${a+1} Car${a+1>1?"s":""}`})),ee=t=>{const a={...t,basement:t.basement===!0||t.basement==="true",hoa:t.hoa===!0||t.hoa==="true"};if(delete a.images,a.year_built&&(a.year_built=parseInt(a.year_built.format("YYYY"),10)),f&&f.length>0){const g=f.map(z=>z.id||z.uploadedId).filter(Boolean);g.length>0&&(a.images=g)}o&&R.length>0&&(a.remove_image_ids=R),a.hoa||(delete a.hoa_price,delete a.hoa_type),console.log("Form data:",a),o?V({slug:o,data:a}):k(a)},le=(t,a=null)=>{const g=t.filter(q=>q instanceof File),z=t.filter(q=>!(q instanceof File));_([...z,...g]),a&&a.id&&o&&F(q=>[...q,a.id])};return j.useEffect(()=>{if(p&&o){const t={...p,year_built:p.year_built?ie().year(p.year_built):null};y(p.basement||!1),P(p.hoa||!1),_(p.images||[]),C(p.images||[]),F([]),p.state&&K(p.state),setTimeout(()=>{x.setFieldsValue(t)},100)}},[p,o,x,K]),e.jsx(oe,{children:e.jsx("div",{className:"container-fluid",children:e.jsxs("div",{className:"row mt-5",children:[e.jsx("div",{className:"col-12",children:e.jsxs("h2",{children:[o?"Edit":"Add"," Listing"]})}),e.jsx("div",{className:"col-12",children:e.jsxs(B,{form:x,name:"propertyForm",layout:"vertical",onFinish:ee,initialValues:{hoa:!0,basement:!0},scrollToFirstError:!0,autoComplete:"off",className:"add-listing",children:[e.jsxs("div",{className:"row gx-5",children:[e.jsx("div",{className:"col-12",children:e.jsx("p",{className:"font-18 mt-4",children:"Property Details"})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(m,{name:"type_id",placeholder:"Select Property Type",label:"Property Type",type:"select",rules:u("Property Type",n.required),options:(Y=s==null?void 0:s.propertyTypes)==null?void 0:Y.map(t=>({value:t.id,label:t.name})),loading:l})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(m,{name:"home_style_id",placeholder:"Select Home Style",label:"Home Style",type:"select",rules:u("Home Style",n.required),options:(X=s==null?void 0:s.homeStyles)==null?void 0:X.map(t=>({value:t.id,label:t.name})),loading:l})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(ne,{name:"mobile_no",placeholder:"(XXX) XXX-XXXX",label:"Showing Number",rules:u("mobile-number",n.required,n.phone)})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(m,{name:"mls_id",placeholder:"Enter MLS ID",label:"MLS ID (optional)"})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(m,{name:"address",placeholder:"xyz street",label:"Address",rules:u("Address",n.required)})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-2",children:e.jsx(m,{name:"state",placeholder:"Select State",options:c,loading:N,handlechange:G,showSearch:!0,label:"State",type:"select",rules:u("State",n.required),filterOption:(t,a)=>{var g;return(g=a==null?void 0:a.label)==null?void 0:g.toLowerCase().includes(t.toLowerCase())}})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-2",children:e.jsx(m,{name:"city",placeholder:"Select City",options:h,loading:U,disabled:!b,showSearch:!0,rules:u("City",n.required),label:"City",type:"select",filterOption:(t,a)=>{var g;return(g=a==null?void 0:a.label)==null?void 0:g.toLowerCase().includes(t.toLowerCase())}})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(m,{name:"zip",placeholder:"Enter Zip Code",label:"Zip Code"})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(m,{name:"price",placeholder:"Enter Price",label:"Price",type:"number",prefix:"$",rules:u("Price",n.required,n.greaterThanOne)})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-8",children:e.jsx(m,{name:"buy_side_compensation",placeholder:"Enter Compensation",label:"Buy-Side Compensation",rules:u("Buy Side Compensation",n.required,n.greaterThanOne),type:"number"})}),e.jsx("div",{className:"col-4",children:e.jsx(m,{name:"buy_side_compensation_type",placeholder:"Enter Compensation",label:"Type",options:Z,rules:u("Buy Side Compensation Type",n.required),type:"select"})})]})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-8",children:e.jsx(m,{name:"size",placeholder:"Enter Property Size",label:"Property Size",type:"number",rules:u("Property Size",n.required,n.greaterThanOne)})}),e.jsx("div",{className:"col-4",children:e.jsx(m,{placeholder:"Unit",options:A,name:"size_type",label:"Size Type",rules:u("Property Size Type",n.required),type:"select"})})]})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-8",children:e.jsx(m,{name:"lot_size",placeholder:"Enter Lot Size",label:"Lot Size",type:"number",rules:u("Lot Size",n.required,n.greaterThanOne)})}),e.jsx("div",{className:"col-4",children:e.jsx(m,{placeholder:"Select Type",label:"Size Type",options:A,rules:u("Lot Size Type",n.required),name:"lot_type",type:"select"})})]})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(B.Item,{name:"year_built",label:"Year Built",rules:u("Year Built",n.required),children:e.jsx(ce,{picker:"year",placeholder:"Select Year",style:{width:"100%"}})})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(m,{name:"bed",placeholder:"No. of Bed",label:"Bed",type:"select",options:M,rules:u("Bed",n.required)})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(m,{name:"bath",placeholder:"No. of Bath",label:"Bath",type:"select",options:M,rules:u("Bath",n.required)})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(m,{name:"garage",placeholder:"No. of Cars",label:"Garage",type:"select",options:J,rules:u("Garage",n.required)})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-3",children:e.jsx(m,{type:"radio",name:"hoa",label:"HOA",options:[{value:!0,label:"Yes"},{value:!1,label:"No"}],rules:u("HOA",n.required),handlechange:t=>{P(t),t||x.setFieldsValue({hoa_price:void 0,hoa_type:void 0})}})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-3",children:e.jsx(m,{type:"radio",name:"basement",label:"Basement",options:[{value:!0,label:"Yes"},{value:!1,label:"No"}],rules:u("basement",n.required)})}),e.jsx(B.Item,{noStyle:!0,shouldUpdate:(t,a)=>t.hoa!==a.hoa,children:({getFieldValue:t})=>t("hoa")===!0?e.jsx("div",{className:"col-12 col-sm-6 col-md-6",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-8",children:e.jsx(m,{name:"hoa_price",placeholder:"Enter Price",label:"HOA Price",type:"number",prefix:"$",rules:u("HOA Price",n.required,n.greaterThanOne)})}),e.jsx("div",{className:"col-4",children:e.jsx(m,{placeholder:"Select Type",label:"Type",options:[{value:"month",label:"Month"},{value:"year",label:"Year"}],name:"hoa_type",type:"select",rules:u("HOA Type",n.required)})})]})}):null})]}),e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12",children:e.jsx("p",{className:"font-18 mt-4",children:"Property Description"})}),e.jsx("div",{className:"col-12",children:e.jsx(m,{name:"description",placeholder:"Enter detailed property description...",label:"Description",type:"textarea",rows:5,rules:u("description",n.required)})})]}),e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12",children:e.jsx("p",{className:"font-18 mt-4",children:"Property Images"})}),e.jsxs("div",{className:"col-12 mt-4 mb-5",children:[e.jsx("div",{className:"form-item-label",children:e.jsx("label",{children:"Upload Images"})}),e.jsx(be,{onChange:le,multiple:!1,maxCount:15,value:f,type:"property_image"})]})]}),e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-12 mt-3 mb-3 text-end",children:[e.jsx(D,{title:"Cancel",className:"gray-btn",onClick:()=>w(-1),disabled:T}),e.jsx(D,{title:T?"Saving...":o?"Update":"Save",className:"blue-btn ms-3",htmlType:"submit",disabled:T})]})})]})})]})})})};export{Ce as default};
