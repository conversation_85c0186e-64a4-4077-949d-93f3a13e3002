// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import { getMessaging, getToken, onMessage } from "firebase/messaging";
import { getAuth, GoogleAuthProvider } from "firebase/auth";
const firebaseConfig = {
  apiKey: "AIzaSyCliv4clsA2gcuT5fg3sQs6Ds9KGV0UySc",
  authDomain: "sphereiconx-d68a6.firebaseapp.com",
  projectId: "sphereiconx-d68a6",
  storageBucket: "sphereiconx-d68a6.firebasestorage.app",
  messagingSenderId: "30096115243",
  appId: "1:30096115243:web:a589ee94a612498ccecc6e",
  measurementId: "G-FJWSZ0F0JG"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const analytics = getAnalytics(app);

// Initialize Firebase Cloud Messaging and get a reference to the service
const messaging = getMessaging(app);

// Function to get device token
export const getDeviceToken = async () => {
  try {
    if (!('Notification' in window)) {
      const fallbackToken = `web_no_notification_${Date.now()}`;
      localStorage.setItem('device_token', fallbackToken);
      return fallbackToken;
    }

    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    
    if (isSafari || isIOS) {
      const fallbackToken = `safari_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      localStorage.setItem('device_token', fallbackToken);
      return fallbackToken;
    }

    const permission = await Notification.requestPermission();
    
    if (permission === 'granted') {
      try {
        const currentToken = await getToken(messaging);
        
        if (currentToken) {
          localStorage.setItem('device_token', currentToken);
          return currentToken;
        } else {
          const fallbackToken = `web_firebase_fallback_${Date.now()}`;
          localStorage.setItem('device_token', fallbackToken);
          return fallbackToken;
        }
      } catch (tokenError) {
        const fallbackToken = `web_firebase_error_${Date.now()}`;
        localStorage.setItem('device_token', fallbackToken);
        return fallbackToken;
      }
    } else {
      const fallbackToken = `web_permission_denied_${Date.now()}`;
      localStorage.setItem('device_token', fallbackToken);
      return fallbackToken;
    }
  } catch (err) {
    const fallbackToken = `web_error_${Date.now()}`;
    localStorage.setItem('device_token', fallbackToken);
    return fallbackToken;
  }
};

// Function to handle foreground messages
export const onMessageListener = () =>
  new Promise((resolve) => {
    onMessage(messaging, (payload) => {
      // Only resolve if the page is visible to prevent background duplicates
      if (document.visibilityState === 'visible') {
        resolve(payload);
      }
    });
  });

// Function to show notification (works for Safari/iOS)
export const showNotification = (title, options = {}) => {
  if ('Notification' in window && Notification.permission === 'granted') {
    const notification = new Notification(title, {
      icon: '/assets/img/logo.png',
      badge: '/assets/img/logo.png',
      ...options
    });
    
    // Auto close after 5 seconds
    setTimeout(() => {
      notification.close();
    }, 5000);
    
    return notification;
  }
};
// Initialize Firebase Auth
const auth = getAuth(app);

export const googleProvider = new GoogleAuthProvider();
export { app, analytics, messaging, auth };