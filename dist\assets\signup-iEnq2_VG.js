import{u as G,R as v,r as x,j as e,L as w,n as S,d as p,H as z}from"./index-Dklazue-.js";import{A as Y}from"./index-CB9TBJHF.js";import{c as a,B as o,P as K,v as l,C as Q}from"./index-BUt89ETK.js";import{F as W}from"./flatbutton-B_tUS4QM.js";import{C as X}from"./index-CE0p5b5u.js";import{u as Z}from"./useMutation-BrUrPIzr.js";import{u as ee}from"./useStartupData-QD8kuEYy.js";import{u as se}from"./useLocationData-CQHRFCOb.js";import{g as le,t as P}from"./languageUtils-BKYM3hOY.js";import{F as b}from"./react-stripe.esm-ypQSOYN5.js";import"./index-CjGjc6T5.js";import"./button-DNhBCuue.js";import"./index-CHbHgJvR.js";import"./useLocale-BNhrTARD.js";import"./index-Vj938NLd.js";import"./fade-B36sOBLs.js";import"./DeleteOutlined-BjE_e6LE.js";import"./index-Cj6uPc4c.js";import"./InboxOutlined-Cxk4AEr6.js";import"./useQuery-C3n1GVcJ.js";const ke=()=>{var k;const I=G(),[_]=b.useForm(),{data:m}=ee(),[h,L]=v.useState([]),[u,C]=x.useState(null),[D,j]=x.useState(null),{selectedState:F,stateOptions:A,cityOptions:y,statesLoading:E,citiesLoading:q,handleStateChange:T}=se(),{mutate:N,isPending:f}=Z("signup",{onSuccess:async s=>{s&&(await z.setStorageData("session",s.data),window.user=s.data,localStorage.removeItem("googleSignupData"),localStorage.removeItem("googleUserData"),I("/home"))}});x.useEffect(()=>{(async()=>{try{const r=await S.init();j(r)}catch{const t=`web_init_error_${Date.now()}`;j(t),localStorage.setItem("device_token",t)}})()},[]),x.useEffect(()=>{const s=localStorage.getItem("googleSignupData");if(s)try{const r=JSON.parse(s);C(r),_.setFieldsValue({email:r.email,name:r.full_name})}catch(r){console.error("Error parsing Google signup data:",r),localStorage.removeItem("googleSignupData")}},[_]);const B=v.useMemo(()=>{var s;return le((s=m==null?void 0:m.data)==null?void 0:s.languages)},[(k=m==null?void 0:m.data)==null?void 0:k.languages]),O=s=>{let r=D||S.getToken()||localStorage.getItem("device_token");if(r||(r=`web_final_fallback_${Date.now()}`,localStorage.setItem("device_token",r)),u){const i={type:"social",social_id:u.social_id,social_platform:u.social_platform,...s,device:"web",device_token:r},c=[];s.professional_type&&s.professional_type.length>0&&s.professional_type.forEach(g=>{const d={name:g};g==="broker"&&s.broker_license_number?(d.license_number=s.broker_license_number,d.license_expiry_date=s.broker_license_expiry_date?p.formatForAPI(s.broker_license_expiry_date):""):g==="lender"&&s.lender_license_number?(d.license_number=s.lender_license_number,d.license_expiry_date=s.lender_license_expiry_date?p.formatForAPI(s.lender_license_expiry_date):""):g==="commercial"&&s.commercial_license_number&&(d.license_number=s.commercial_license_number,d.license_expiry_date=s.commercial_license_expiry_date?p.formatForAPI(s.commercial_license_expiry_date):""),c.push(d)}),i.professional_types=JSON.stringify(c),delete i.professional_type,delete i.broker_license_number,delete i.broker_license_expiry_date,delete i.lender_license_number,delete i.lender_license_expiry_date,delete i.commercial_license_number,delete i.commercial_license_expiry_date,delete i.languages;const V=P(s.languages),$={...i,...V};N($);return}const t=[];s.professional_type&&s.professional_type.length>0&&s.professional_type.forEach(i=>{const c={name:i};i==="broker"&&s.broker_license_number?(c.license_number=s.broker_license_number,c.license_expiry_date=s.broker_license_expiry_date?p.formatForAPI(s.broker_license_expiry_date):""):i==="lender"&&s.lender_license_number?(c.license_number=s.lender_license_number,c.license_expiry_date=s.lender_license_expiry_date?p.formatForAPI(s.lender_license_expiry_date):""):i==="commercial"&&s.commercial_license_number&&(c.license_number=s.commercial_license_number,c.license_expiry_date=s.commercial_license_expiry_date?p.formatForAPI(s.commercial_license_expiry_date):""),t.push(c)});const n={...s,professional_types:JSON.stringify(t),device:"web",device_token:r,type:"email"};delete n.professional_type,delete n.broker_license_number,delete n.broker_license_expiry_date,delete n.lender_license_number,delete n.lender_license_expiry_date,delete n.commercial_license_number,delete n.commercial_license_expiry_date,delete n.languages;const U=P(s.languages),H={...n,...U};N(H)},R=s=>{T(s,_)},J=s=>{L(s)},M=s=>{console.log(`checked = ${s.target.checked}`)};return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"text-center sign-up-logo",children:e.jsx("img",{src:"../assets/img/logo.png",alt:"Auth Logo"})}),e.jsxs(Y,{showSidebar:!1,pageType:"signup",children:[e.jsxs("div",{className:"col-12 text-center",children:[e.jsx("h1",{className:"font-36 color-black mb-4",children:"Sign Up"}),e.jsx("h1",{className:"font-36 color-black",children:"Create An Account"}),e.jsx("p",{children:"Register yourself here to continue"})]}),e.jsxs(b,{name:"register",layout:"vertical",form:_,onFinish:O,scrollToFirstError:!0,initialValues:{remember:!0},autoComplete:"off",children:[e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-12 text-center mt-4",children:e.jsx(X,{static_img:"/assets/img/avatar-img.svg",fileType:"image",multiple:!1,maxSize:5,uploadAction:"",useFormItem:!0,formItemProps:{name:"image_url",rules:a("profile-image",l.required)},callback:s=>{_.setFieldsValue({image_url:s.fileObj||s.originFileObj})}})})}),e.jsxs("div",{className:"row mt-5 justify-content-center",children:[e.jsx("div",{className:"col-12 col-md-6 col-lg-5 offset-lg-1",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12",children:e.jsx("h4",{className:"",children:"Personal Info"})}),e.jsx("div",{className:"col-12 col-md-12 ",children:e.jsx(o,{name:"name",placeholder:"e.g John Doe",label:"Full Name",rules:a("full-name",l.required,l.minLength(2),l.maxLength(25))})}),e.jsx("div",{className:"col-12 col-md-12 ",children:e.jsx(o,{name:"email",placeholder:"<EMAIL>",label:"Email",rules:a("email",l.required,l.email)})}),e.jsx("div",{className:"col-12 col-md-12 ",children:e.jsx(K,{name:"mobile_no",label:"Phone Number",rules:a("Phone Number",l.required,l.phone)})}),!u&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"col-12 col-md-12 ",children:e.jsx(o,{name:"password",placeholder:"***",label:"Password",type:"password",rules:a("password",l.required,l.password)})}),e.jsx("div",{className:"col-12 col-md-12 ",children:e.jsx(o,{name:"confrimpassword",placeholder:"***",label:"Confirm Password",type:"password",rules:[l.required("confirm-password"),({getFieldValue:s})=>({validator:(r,t)=>{const n=s("password");return!t||!n?Promise.resolve():t!==n?Promise.reject(new Error("Confirm Passwords does not match")):Promise.resolve()}})],dependencies:["password"]})})]})]})}),e.jsx("div",{className:"col-12 col-md-6 col-lg-5 offset-lg-1",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12",children:e.jsx("h4",{children:"Professional Info"})}),e.jsx("div",{className:"col-12 col-md-12 ",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-6 col-md-6",children:e.jsx(o,{name:"state",placeholder:"State",label:"Location",type:"select",rules:a("state",l.required),options:A,loading:E,handlechange:R,showSearch:!0})}),e.jsx("div",{className:"col-6 col-md-6",children:e.jsx(o,{name:"city",placeholder:"City",label:" ",type:"select",options:y,rules:y.length>0?a("city",l.required):[],disabled:!F,loading:q,showSearch:!0})})]})}),e.jsx("div",{className:"col-6 col-md-6",children:e.jsx(o,{name:"languages",placeholder:"Select Language",label:"Languages Spoken",type:"select",rules:a("language",l.required),mode:"multiple",options:B})}),e.jsx("div",{className:"col-12 col-md-6 ",children:e.jsx("div",{className:"ant-form-item d-block",children:e.jsx("div",{className:"mt-2",children:e.jsx(o,{type:"radio",name:"multi_state_license",label:"Multi State License",options:[{value:!0,label:"Yes"},{value:!1,label:"No"}],rules:a("multi-state-license",l.required)})})})}),e.jsx("div",{className:"col-12 ",children:e.jsx(o,{type:"checkboxgroup",name:"professional_type",label:"Profession Type",options:[{value:"broker",label:"Real Estate Broker"},{value:"lender",label:"Lender Mortgage Broker"},{value:"commercial",label:"Commercial Agent"}],onChange:J,rules:a("profession-type",l.required)})}),h.includes("broker")&&e.jsxs("div",{className:"col-12",children:[e.jsx("h5",{className:"mt-3 font-18",children:"Real Estate Broker License"}),e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(o,{name:"broker_license_number",placeholder:"1ABC234",label:"License No.",rules:a("license",l.required)})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(o,{name:"broker_license_expiry_date",placeholder:"Expiry Date",label:"Expiry Date",type:"datepicker",rules:a("expiry",l.required)})})]})]}),h.includes("lender")&&e.jsxs("div",{className:"col-12",children:[e.jsx("h5",{className:"font-18 mt-3",children:"Lender/ mtg Broker License"}),e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(o,{name:"lender_license_number",placeholder:"1ABC234",label:"License No.",rules:a("license",l.required)})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(o,{name:"lender_license_expiry_date",placeholder:"Expiry Date",label:"Expiry Date",type:"datepicker",rules:a("expiry",l.required)})})]})]}),h.includes("commercial")&&e.jsxs("div",{className:"col-12",children:[e.jsx("h5",{className:"font-18 mt-3",children:"Commercial Agent License"}),e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(o,{name:"commercial_license_number",placeholder:"1ABC234",label:"License No.",rules:a("license",l.required)})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(o,{name:"commercial_license_expiry_date",placeholder:"Expiry Date",label:"Expiry Date",type:"datepicker",rules:a("expiry",l.required)})})]})]})]})})]}),e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-12",children:e.jsx("div",{className:"text-center",children:e.jsx("div",{className:"mt-4 check-item",children:e.jsx(b.Item,{name:"check",valuePropName:"checked",rules:[{validator:(s,r)=>r?Promise.resolve():Promise.reject(new Error("Please accept the Terms and Conditions"))}],children:e.jsxs(Q,{onChange:M,children:["I agree to the",e.jsx(w,{to:"#",className:"color-blue  font-16 ms-1",children:"Terms and Conditions"})]})})})})})}),e.jsx("div",{className:"text-center",children:e.jsx(W,{title:f?"Creating Account...":"Register Now",className:"mx-auto mt-4 signin-btn signup-btn mt-5",htmlType:"submit",loading:f,disabled:f})}),e.jsx("div",{children:e.jsxs("p",{className:"signup-text",children:["Don't have an account?",e.jsx(w,{to:"/login",className:"color-blue  font-16 ms-1",children:"Sign In"})]})})]})]})]})};export{ke as default};
