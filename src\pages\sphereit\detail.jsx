import React, { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import InnerLayout from "@/components/shared/layout/innerlayout";
import SpherePost from "@/components/shared/card/spherepost";
import UserAvatar from "@/components/shared/avatar/useravatar";
import PostInput from "@/components/shared/inputs/postinput";
import BaseInput from "@/components/shared/inputs";
import { useQuery, useMutation } from "@/hooks/reactQuery";
import { Skeleton, Empty, message, Button, Form } from "antd";
import { SendOutlined, LikeOutlined, LikeFilled } from "@ant-design/icons";
import { usePostCreation } from "./hooks";
import useLikeComment from "@/hooks/useLikeComment";
import useSweetAlert from "@/hooks/useSweetAlert";
import FlatButton from "@/components/shared/button/flatbutton";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";

dayjs.extend(relativeTime);

const SphereItDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { showAlert } = useSweetAlert();
  const [editingPost, setEditingPost] = useState(null);
  const [form] = Form.useForm();

  // Fetch single post with optimized React Query
  const {
    data: postResponse,
    isLoading: isPostLoading,
    error: postError,
  } = useQuery("postItem", {
    slug: id,
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: "always",
  });

  // Fetch comments separately
  const { data: commentsResponse, isLoading: isCommentsLoading } = useQuery(
    "getComments",
    {
      params: { post_id: id },
      enabled: !!id,
      staleTime: 2 * 60 * 1000, // 2 minutes for comments
      gcTime: 5 * 60 * 1000, // 5 minutes
    }
  );

  // Post creation hook for editing
  const postCreationHook = usePostCreation(editingPost, () => {
    setEditingPost(null);
  });

  // Comment submission mutation
  const { mutate: submitComment, isPending: isSubmittingComment } = useMutation(
    "comments",
    {
      useFormData: false, // Send as JSON
      showSuccessNotification: false,
      onSuccess: () => {
        form.resetFields();
      },

      invalidateQueries: [
        { queryKey: ["getComments"], type: "all" },
        { queryKey: ["postItem"], type: "all" },
      ],
    }
  );

  // Delete post mutation
  const { mutate: deletePost, isPending: isDeleting } = useMutation(
    "deletePost",
    {
      onSuccess: () => {
        navigate("/sphare-it");
      },

      invalidateQueries: [{ queryKey: ["postItem"], type: "all" }],
    }
  );

  const post = postResponse?.data;
  const comments = commentsResponse?.data || [];

  const handleCommentSubmit = (values) => {
    if (!values.message?.trim()) {
      message.warning("Please enter a comment");
      return;
    }

    submitComment({
      post_id: parseInt(id),
      message: values.message.trim(),
    });
  };

  const handleEditPost = (postId) => {
    if (post) {
      setEditingPost(post);
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  const handleCancelEdit = () => {
    setEditingPost(null);
    postCreationHook.resetForm();
  };

  const handleDeletePost = async (postId) => {
    const result = await showAlert({
      title: "Are you sure?",
      text: "Are you sure you want to delete this post?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "Cancel",
    });

    if (result.isConfirmed) {
      deletePost({ slug: postId, data: "" });
    }
  };

  const checkPostOwnership = (post) => {
    return (
      post?.user?.id === window.user?.id || post?.user_id === window.user?.id
    );
  };

  // Comment Component - Matches Facebook-style UI with exact post listing like behavior
  const CommentItem = ({ comment }) => {
    const { isLiked, likesCount, isToggling, handleLikeToggle } =
      useLikeComment(
        comment?.id,
        comment?.is_liked || false,
        comment?.likes_count || 0
      );

    const handleLikeClick = (e) => {
      e.stopPropagation();
      handleLikeToggle();
    };

    return (
      <div className="comment-item mb-3">
        <div className="d-flex align-items-start">
          <img
            src={comment?.user?.image_url}
            alt={comment?.user?.name}
            className="rounded-circle me-3"
            width={40}
            height={40}
          />
          <div className="flex-grow-1">
            <div className="d-flex align-items-center mb-1">
              <h6 className="mb-0 me-2">{comment?.user?.name}</h6>
            </div>
            <p className="mb-2">{comment?.message}</p>

            {/* Like button with exact same styling as post listing */}
            <div className="d-flex align-items-center">
              <div
                className="d-flex align-items-center cursor-pointer py-1 px-2 rounded hover-bg-light"
                onClick={handleLikeClick}
                style={{
                  color: isLiked ? "#1890ff" : "#8c8c8c",
                  opacity: isToggling ? 0.6 : 1,
                  pointerEvents: isToggling ? "none" : "auto",
                  fontSize: "14px",
                }}
              >
                {isLiked ? (
                  <LikeFilled
                    style={{ fontSize: "16px", marginRight: "4px" }}
                  />
                ) : (
                  <LikeOutlined
                    style={{ fontSize: "16px", marginRight: "4px" }}
                  />
                )}
                <span style={{ fontSize: "14px" }}>
                  {isLiked ? "Liked" : "Like"}
                </span>
                {likesCount > 0 && (
                  <span
                    className="ms-2"
                    style={{ fontSize: "14px", color: "#8c8c8c" }}
                  >
                    {likesCount}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (isPostLoading) {
    return (
      <InnerLayout>
        <div className="container-fluid">
          <div className="row">
            <div className="col-12 mt-4">
              {/* Post Skeleton */}
              <div className="post-card sphere-card p-3 rounded border bg-white mb-4">
                <div className="d-flex align-items-center mb-3">
                  <Skeleton.Avatar size={40} />
                  <div className="ms-3 flex-grow-1">
                    <Skeleton.Input style={{ width: 120, height: 16 }} />
                    <div className="mt-1">
                      <Skeleton.Input style={{ width: 80, height: 12 }} />
                    </div>
                  </div>
                </div>
                <Skeleton.Image
                  active
                  className="w-100 mb-3"
                  style={{ width: "100%", height: 300 }}
                />
                <Skeleton paragraph={{ rows: 3 }} />
                <div className="d-flex justify-content-between align-items-center mt-3">
                  <div className="d-flex gap-3">
                    <Skeleton.Input style={{ width: 60, height: 20 }} />
                    <Skeleton.Input style={{ width: 60, height: 20 }} />
                    <Skeleton.Input style={{ width: 60, height: 20 }} />
                  </div>
                </div>
              </div>

              {/* Comments Skeleton */}
              <div className="comments-section bg-white border rounded p-3">
                <Skeleton.Input
                  style={{ width: 120, height: 20 }}
                  className="mb-3"
                />
                {Array.from({ length: 3 }).map((_, idx) => (
                  <div key={idx} className="d-flex align-items-start mb-3">
                    <Skeleton.Avatar size={32} />
                    <div className="ms-3 flex-grow-1">
                      <Skeleton.Input style={{ width: 100, height: 14 }} />
                      <div className="mt-1">
                        <Skeleton paragraph={{ rows: 1, width: "80%" }} />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </InnerLayout>
    );
  }

  if (postError) {
    return (
      <InnerLayout>
        <div className="container-fluid">
          <div className="row">
            <div className="col-12 mt-4">
              <Empty
                description="Failed to load post. Please try again."
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              >
                <Button type="primary" onClick={() => navigate("/sphare-it")}>
                  Back to Posts
                </Button>
              </Empty>
            </div>
          </div>
        </div>
      </InnerLayout>
    );
  }

  if (!post) {
    return (
      <InnerLayout>
        <div className="container-fluid">
          <div className="row">
            <div className="col-12 mt-4">
              <Empty
                description="Post not found"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              >
                <Button type="primary" onClick={() => navigate("/sphare-it")}>
                  Back to Posts
                </Button>
              </Empty>
            </div>
          </div>
        </div>
      </InnerLayout>
    );
  }

  return (
    <InnerLayout>
      <div className="container-fluid">
        <div className="row post-detail">
          {/* Edit Post Input - Only show when editing */}
          {editingPost && (
            <div className="col-12">
              <PostInput
                postCreationHook={postCreationHook}
                onCancelEdit={handleCancelEdit}
              />
            </div>
          )}

          <div className="col-12 mt-4">
            {/* Post using same UI as listing */}
            <SpherePost
              {...post}
              showActions={checkPostOwnership(post)}
              onEdit={handleEditPost}
              onDelete={handleDeletePost}
            />

            {/* Comments Section - Facebook-style UI */}
            <div className="comments-section bg-white border rounded p-3 mt-3">
              {/* Existing Comments */}
              {isCommentsLoading ? (
                <div>
                  {Array.from({ length: 3 }).map((_, idx) => (
                    <div key={idx} className="d-flex align-items-start mb-3">
                      <Skeleton.Avatar size={40} />
                      <div className="ms-3 flex-grow-1">
                        <Skeleton.Input style={{ width: 100, height: 14 }} />
                        <div className="mt-1">
                          <Skeleton paragraph={{ rows: 1, width: "80%" }} />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : comments?.length > 0 ? (
                comments?.map((comment) => (
                  <CommentItem key={comment.id} comment={comment} />
                ))
              ) : (
                <p className="text-muted mb-3">
                  No comments yet. Be the first to comment!
                </p>
              )}

              {/* Add New Comment - Single Row with BaseInput */}
              <div className="mt-4">
                <Form
                  form={form}
                  onFinish={handleCommentSubmit}
                  layout="vertical"
                >
                  <div className="d-flex align-items-end gap-2">
                    <div className="flex-grow-1">
                      <BaseInput
                        name="message"
                        type="text"
                        placeholder="Write a comment..."
                        className="w-100"
                        style={{ marginBottom: 0 }}
                      />
                    </div>
                    <FlatButton
                      type="primary"
                      htmlType="submit"
                      icon={<SendOutlined />}
                      loading={isSubmittingComment}
                      className="post-comment-btn"
                    >
                      Post
                    </FlatButton>
                  </div>
                </Form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </InnerLayout>
  );
};

export default SphereItDetail;
