import{j as o}from"./index-Dklazue-.js";import{I as i}from"./index-vmMMvWhJ.js";import{O as t}from"./optionlist-DUV6NZmx.js";import"./useMutation-BrUrPIzr.js";import"./button-DNhBCuue.js";import"./index-Cj6uPc4c.js";const m=()=>{const s=[{label:"Terms & Conditions",link:"/about/terms"},{label:"Privacy Policy",link:"/about/privacy"},{label:"FAQs",link:"/about/faq"}];return o.jsx(i,{children:o.jsx("div",{className:"container-fluid",children:o.jsxs("div",{className:"row",children:[o.jsx("div",{className:"col-12 mt-4",children:o.jsx("p",{className:"font-36 font-600 color-black",children:"About"})}),o.jsx("div",{className:"col-12",children:o.jsx(t,{options:s})})]})})})};export{m as default};
