/**
 * Trial utility functions for handling subscription checks
 */

/**
 * Checks if user's trial is active and shows subscription alert if needed
 * @param {Function} showAlert - SweetAlert function from useSweetAlert hook
 * @param {Function} navigate - React Router navigate function
 * @returns {Promise<boolean>} - Returns true if trial check failed (trial ended), false if trial is active
 */
export const handleTrialCheck = async (showAlert, navigate) => {
  // Check if trial is inactive and show subscription alert
  if (window.user && window.user.isTrialActive === false) {
    const result = await showAlert({
      title: "Subscription Required",
      text: "Your 60 Day Trial Has Ended. Stay Connected. Close Deals. Run Your Business Like a Pro. Only $4.99 per month.",
      icon: "info",
      showCancelButton: false,
      confirmButtonText: "Subscribe",
    });

    if (result.isConfirmed) {
      // Redirect to subscription URL
      navigate("/subscription");
    }
    return true; // Trial check failed
  }
  return false; // Trial is active
};

/**
 * Higher-order function that wraps any action with trial check
 * @param {Function} action - The action to execute if trial is active
 * @param {Function} showAlert - SweetAlert function from useSweetAlert hook
 * @param {Function} navigate - React Router navigate function
 * @returns {Function} - Wrapped function that performs trial check before executing action
 */
export const withTrialCheck = (action, showAlert, navigate) => {
  return async (...args) => {
    const trialCheckFailed = await handleTrialCheck(showAlert, navigate);
    if (!trialCheckFailed && action) {
      return action(...args);
    }
    return false;
  };
};

/**
 * Custom hook for trial functionality (optional - for components that need multiple trial-protected actions)
 * @param {Function} showAlert - SweetAlert function from useSweetAlert hook
 * @param {Function} navigate - React Router navigate function
 * @returns {Object} - Object containing trial check function and wrapper
 */
export const useTrialUtils = (showAlert, navigate) => {
  const checkTrial = async () => handleTrialCheck(showAlert, navigate);
  
  const wrapWithTrialCheck = (action) => withTrialCheck(action, showAlert, navigate);
  
  return {
    checkTrial,
    wrapWithTrialCheck,
  };
};