import{r as s,N as U,O as Y,P as Z,Q as ge,J as ue,a4 as Q,ae as ee,x as I,T as me,a6 as te,K as ve}from"./index-Dklazue-.js";import{c as fe,z as ye,R as he,f as be,Q as Ce,m as xe,S as Se,X as Oe,b as Pe,T as $e,d as ze}from"./useMutation-BrUrPIzr.js";import{u as je,P as Ne,t as we}from"./button-DNhBCuue.js";const K=s.createContext({}),Ee=e=>{const{antCls:o,componentCls:n,iconCls:t,avatarBg:r,avatarColor:a,containerSize:p,containerSizeLG:l,containerSizeSM:g,textFontSize:f,textFontSizeLG:c,textFontSizeSM:b,borderRadius:O,borderRadiusLG:u,borderRadiusSM:P,lineWidth:d,lineType:y}=e,z=(i,j,N)=>({width:i,height:i,borderRadius:"50%",[`&${n}-square`]:{borderRadius:N},[`&${n}-icon`]:{fontSize:j,[`> ${t}`]:{margin:0}}});return{[n]:Object.assign(Object.assign(Object.assign(Object.assign({},Z(e)),{position:"relative",display:"inline-flex",justifyContent:"center",alignItems:"center",overflow:"hidden",color:a,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:r,border:`${ge(d)} ${y} transparent`,"&-image":{background:"transparent"},[`${o}-image-img`]:{display:"block"}}),z(p,f,O)),{"&-lg":Object.assign({},z(l,c,u)),"&-sm":Object.assign({},z(g,b,P)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},Be=e=>{const{componentCls:o,groupBorderColor:n,groupOverlapping:t,groupSpace:r}=e;return{[`${o}-group`]:{display:"inline-flex",[o]:{borderColor:n},"> *:not(:first-child)":{marginInlineStart:t}},[`${o}-group-popover`]:{[`${o} + ${o}`]:{marginInlineStart:r}}}},ke=e=>{const{controlHeight:o,controlHeightLG:n,controlHeightSM:t,fontSize:r,fontSizeLG:a,fontSizeXL:p,fontSizeHeading3:l,marginXS:g,marginXXS:f,colorBorderBg:c}=e;return{containerSize:o,containerSizeLG:n,containerSizeSM:t,textFontSize:Math.round((a+p)/2),textFontSizeLG:l,textFontSizeSM:r,groupSpace:f,groupOverlapping:-g,groupBorderColor:c}},oe=U("Avatar",e=>{const{colorTextLightSolid:o,colorTextPlaceholder:n}=e,t=Y(e,{avatarBg:n,avatarColor:o});return[Ee(t),Be(t)]},ke);var Re=function(e,o){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(n[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)o.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(n[t[r]]=e[t[r]]);return n};const ne=s.forwardRef((e,o)=>{const{prefixCls:n,shape:t,size:r,src:a,srcSet:p,icon:l,className:g,rootClassName:f,style:c,alt:b,draggable:O,children:u,crossOrigin:P,gap:d=4,onError:y}=e,z=Re(e,["prefixCls","shape","size","src","srcSet","icon","className","rootClassName","style","alt","draggable","children","crossOrigin","gap","onError"]),[i,j]=s.useState(1),[N,_]=s.useState(!1),[B,k]=s.useState(!0),M=s.useRef(null),w=s.useRef(null),R=ue(o,M),{getPrefixCls:E,avatar:$}=s.useContext(Q),C=s.useContext(K),T=()=>{if(!w.current||!M.current)return;const h=w.current.offsetWidth,v=M.current.offsetWidth;h!==0&&v!==0&&d*2<v&&j(v-d*2<h?(v-d*2)/h:1)};s.useEffect(()=>{_(!0)},[]),s.useEffect(()=>{k(!0),j(1)},[a]),s.useEffect(T,[d]);const V=()=>{(y==null?void 0:y())!==!1&&k(!1)},m=je(h=>{var v,H;return(H=(v=r??(C==null?void 0:C.size))!==null&&v!==void 0?v:h)!==null&&H!==void 0?H:"default"}),G=Object.keys(typeof m=="object"?m||{}:{}).some(h=>["xs","sm","md","lg","xl","xxl"].includes(h)),W=fe(G),D=s.useMemo(()=>{if(typeof m!="object")return{};const h=ye.find(H=>W[H]),v=m[h];return v?{width:v,height:v,fontSize:v&&(l||u)?v/2:18}:{}},[W,m]),x=E("avatar",n),S=ee(x),[A,F,ie]=oe(x,S),le=I({[`${x}-lg`]:m==="large",[`${x}-sm`]:m==="small"}),q=s.isValidElement(a),ce=t||(C==null?void 0:C.shape)||"circle",de=I(x,le,$==null?void 0:$.className,`${x}-${ce}`,{[`${x}-image`]:q||a&&B,[`${x}-icon`]:!!l},ie,S,g,f,F),pe=typeof m=="number"?{width:m,height:m,fontSize:l?m/2:18}:{};let L;if(typeof a=="string"&&B)L=s.createElement("img",{src:a,draggable:O,srcSet:p,onError:V,alt:b,crossOrigin:P});else if(q)L=a;else if(l)L=l;else if(N||i!==1){const h=`scale(${i})`,v={msTransform:h,WebkitTransform:h,transform:h};L=s.createElement(he,{onResize:T},s.createElement("span",{className:`${x}-string`,ref:w,style:Object.assign({},v)},u))}else L=s.createElement("span",{className:`${x}-string`,style:{opacity:0},ref:w},u);return A(s.createElement("span",Object.assign({},z,{style:Object.assign(Object.assign(Object.assign(Object.assign({},pe),D),$==null?void 0:$.style),c),className:de,ref:R}),L))}),X=e=>e?typeof e=="function"?e():e:null,Te=e=>{const{componentCls:o,popoverColor:n,titleMinWidth:t,fontWeightStrong:r,innerPadding:a,boxShadowSecondary:p,colorTextHeading:l,borderRadiusLG:g,zIndexPopup:f,titleMarginBottom:c,colorBgElevated:b,popoverBg:O,titleBorderBottom:u,innerContentPadding:P,titlePadding:d}=e;return[{[o]:Object.assign(Object.assign({},Z(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:f,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"--antd-arrow-background-color":b,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${o}-content`]:{position:"relative"},[`${o}-inner`]:{backgroundColor:O,backgroundClip:"padding-box",borderRadius:g,boxShadow:p,padding:a},[`${o}-title`]:{minWidth:t,marginBottom:c,color:l,fontWeight:r,borderBottom:u,padding:d},[`${o}-inner-content`]:{color:n,padding:P}})},Ce(e,"var(--antd-arrow-background-color)"),{[`${o}-pure`]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow,display:"inline-block",[`${o}-content`]:{display:"inline-block"}}}]},Ie=e=>{const{componentCls:o}=e;return{[o]:Ne.map(n=>{const t=e[`${n}6`];return{[`&${o}-${n}`]:{"--antd-arrow-background-color":t,[`${o}-inner`]:{backgroundColor:t},[`${o}-arrow`]:{background:"transparent"}}}})}},_e=e=>{const{lineWidth:o,controlHeight:n,fontHeight:t,padding:r,wireframe:a,zIndexPopupBase:p,borderRadiusLG:l,marginXS:g,lineType:f,colorSplit:c,paddingSM:b}=e,O=n-t,u=O/2,P=O/2-o,d=r;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:p+30},xe(e)),Se({contentRadius:l,limitVerticalRadius:!0})),{innerPadding:a?0:12,titleMarginBottom:a?0:g,titlePadding:a?`${u}px ${d}px ${P}px`:0,titleBorderBottom:a?`${o}px ${f} ${c}`:"none",innerContentPadding:a?`${b}px ${d}px`:0})},re=U("Popover",e=>{const{colorBgElevated:o,colorText:n}=e,t=Y(e,{popoverBg:o,popoverColor:n});return[Te(t),Ie(t),be(t,"zoom-big")]},_e,{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]});var Me=function(e,o){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(n[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)o.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(n[t[r]]=e[t[r]]);return n};const se=e=>{let{title:o,content:n,prefixCls:t}=e;return!o&&!n?null:s.createElement(s.Fragment,null,o&&s.createElement("div",{className:`${t}-title`},o),n&&s.createElement("div",{className:`${t}-inner-content`},n))},We=e=>{const{hashId:o,prefixCls:n,className:t,style:r,placement:a="top",title:p,content:l,children:g}=e,f=X(p),c=X(l),b=I(o,n,`${n}-pure`,`${n}-placement-${a}`,t);return s.createElement("div",{className:b,style:r},s.createElement("div",{className:`${n}-arrow`}),s.createElement(Oe,Object.assign({},e,{className:o,prefixCls:n}),g||s.createElement(se,{prefixCls:n,title:f,content:c})))},Ae=e=>{const{prefixCls:o,className:n}=e,t=Me(e,["prefixCls","className"]),{getPrefixCls:r}=s.useContext(Q),a=r("popover",o),[p,l,g]=re(a);return p(s.createElement(We,Object.assign({},t,{prefixCls:a,hashId:l,className:I(n,g)})))};var Le=function(e,o){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(n[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)o.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(n[t[r]]=e[t[r]]);return n};const Ve=s.forwardRef((e,o)=>{var n,t;const{prefixCls:r,title:a,content:p,overlayClassName:l,placement:g="top",trigger:f="hover",children:c,mouseEnterDelay:b=.1,mouseLeaveDelay:O=.1,onOpenChange:u,overlayStyle:P={},styles:d,classNames:y}=e,z=Le(e,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle","styles","classNames"]),{getPrefixCls:i,className:j,style:N,classNames:_,styles:B}=me("popover"),k=i("popover",r),[M,w,R]=re(k),E=i(),$=I(l,w,R,j,_.root,y==null?void 0:y.root),C=I(_.body,y==null?void 0:y.body),[T,V]=Pe(!1,{value:(n=e.open)!==null&&n!==void 0?n:e.visible,defaultValue:(t=e.defaultOpen)!==null&&t!==void 0?t:e.defaultVisible}),m=(S,A)=>{V(S,!0),u==null||u(S,A)},G=S=>{S.keyCode===ve.ESC&&m(!1,S)},W=S=>{m(S)},D=X(a),x=X(p);return M(s.createElement($e,Object.assign({placement:g,trigger:f,mouseEnterDelay:b,mouseLeaveDelay:O},z,{prefixCls:k,classNames:{root:$,body:C},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},B.root),N),P),d==null?void 0:d.root),body:Object.assign(Object.assign({},B.body),d==null?void 0:d.body)},ref:o,open:T,onOpenChange:W,overlay:D||x?s.createElement(se,{prefixCls:k,title:D,content:x}):null,transitionName:ze(E,"zoom-big",z.transitionName),"data-popover-inject":!0}),te(c,{onKeyDown:S=>{var A,F;s.isValidElement(c)&&((F=c==null?void 0:(A=c.props).onKeyDown)===null||F===void 0||F.call(A,S)),G(S)}})))}),ae=Ve;ae._InternalPanelDoNotUseOrYouWillBeFired=Ae;const J=e=>{const{size:o,shape:n}=s.useContext(K),t=s.useMemo(()=>({size:e.size||o,shape:e.shape||n}),[e.size,e.shape,o,n]);return s.createElement(K.Provider,{value:t},e.children)},Ge=e=>{var o,n,t,r;const{getPrefixCls:a,direction:p}=s.useContext(Q),{prefixCls:l,className:g,rootClassName:f,style:c,maxCount:b,maxStyle:O,size:u,shape:P,maxPopoverPlacement:d,maxPopoverTrigger:y,children:z,max:i}=e,j=a("avatar",l),N=`${j}-group`,_=ee(j),[B,k,M]=oe(j,_),w=I(N,{[`${N}-rtl`]:p==="rtl"},M,_,g,f,k),R=we(z).map((C,T)=>te(C,{key:`avatar-key-${T}`})),E=(i==null?void 0:i.count)||b,$=R.length;if(E&&E<$){const C=R.slice(0,E),T=R.slice(E,$),V=(i==null?void 0:i.style)||O,m=((o=i==null?void 0:i.popover)===null||o===void 0?void 0:o.trigger)||y||"hover",G=((n=i==null?void 0:i.popover)===null||n===void 0?void 0:n.placement)||d||"top",W=Object.assign(Object.assign({content:T},i==null?void 0:i.popover),{classNames:{root:I(`${N}-popover`,(r=(t=i==null?void 0:i.popover)===null||t===void 0?void 0:t.classNames)===null||r===void 0?void 0:r.root)},placement:G,trigger:m});return C.push(s.createElement(ae,Object.assign({key:"avatar-popover-key",destroyOnHidden:!0},W),s.createElement(ne,{style:V},`+${$-E}`))),B(s.createElement(J,{shape:P,size:u},s.createElement("div",{className:w,style:c},C)))}return B(s.createElement(J,{shape:P,size:u},s.createElement("div",{className:w,style:c},R)))},Fe=ne;Fe.Group=Ge;export{Fe as A};
