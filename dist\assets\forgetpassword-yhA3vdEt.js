import{r as l,u as m,j as s}from"./index-Dklazue-.js";import{A as n}from"./index-CB9TBJHF.js";import{B as c,c as d,v as a}from"./index-BUt89ETK.js";import{F as u}from"./flatbutton-B_tUS4QM.js";import{u as g}from"./useMutation-BrUrPIzr.js";import{F as p}from"./react-stripe.esm-ypQSOYN5.js";import"./index-CjGjc6T5.js";import"./button-DNhBCuue.js";import"./index-CHbHgJvR.js";import"./useLocale-BNhrTARD.js";const x=()=>{const t=m(),{mutate:i,isPending:o}=g("forgotPassword",{useFormData:!1,onSuccess:e=>{e&&t("/login")}}),r=e=>{i(e)};return s.jsxs("div",{className:"forgot-area",children:[s.jsx("div",{className:"text-center sign-up-logo",children:s.jsx("img",{src:"../assets/img/logo.png",alt:"Auth Logo"})}),s.jsxs(n,{showSidebar:!0,src:"/assets/img/forgot-img.png",pageType:"forgot",children:[s.jsx("div",{className:"row",children:s.jsxs("div",{className:"col-12",children:[s.jsx("h1",{className:"font-36 color-black mb-3",children:"Forgot Your Password?"}),s.jsx("p",{children:"Enter the email address associated with your account."})]})}),s.jsxs(p,{name:"ForgetPassword",layout:"vertical",onFinish:r,initialValues:{remember:!0},autoComplete:"off",children:[s.jsxs("div",{className:"row",children:[s.jsx("div",{className:"col-12 col-md-12 col-lg-10",children:s.jsx(c,{name:"email",placeholder:"Email Address",label:"Email",rules:d("email",a.required,a.email)})}),s.jsxs("div",{className:"col-12 col-md-12 col-lg-10",children:[s.jsx(u,{title:o?"Submiting...":"Submit",className:"mx-auto mt-4 signin-btn signup-btn w-100 mt-5",htmlType:"submit",loading:o,disabled:o}),s.jsx("p",{className:"signup-text",children:"We will email you a link to reset your password"})]})]}),s.jsx("div",{})]})]})]})},P=l.memo(x);export{P as default};
