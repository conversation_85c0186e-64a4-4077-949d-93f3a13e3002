import{R as p,j as r,_ as n}from"./index-Dklazue-.js";import{I as l}from"./index-vmMMvWhJ.js";import{t as c,P as d}from"./propertyUtils-DF3QzgTW.js";import{R as f}from"./ReusablePagination-B7_yMXG_.js";import{E as u}from"./EmptyState-YbiQZMha.js";import{u as h}from"./useSearchFilterPagination-BuDUyyEy.js";import{S as x}from"./Skeleton--lTrbnrp.js";import"./useMutation-BrUrPIzr.js";import"./button-DNhBCuue.js";import"./index-Cj6uPc4c.js";import"./EditOutlined-DxjsqgjX.js";import"./DeleteOutlined-BjE_e6LE.js";import"./index-CjGjc6T5.js";import"./index-CHbHgJvR.js";import"./useLocale-BNhrTARD.js";import"./useQuery-C3n1GVcJ.js";const C=()=>{const{data:o,isLoading:a,pagination:i,handlePageChange:m}=h("properties",{pageSize:12,defaultParams:{is_favorite:!0}}),s=p.useMemo(()=>o!=null&&o.data?c(o.data,{showActions:!1}):[],[o]);return r.jsx(l,{children:r.jsxs("div",{className:"container-fluid",children:[r.jsx("div",{className:"row",children:r.jsx("div",{className:"col-12 mt-3",children:r.jsx("p",{className:"font-36 font-600 color-black mb-4",children:"Favourites"})})}),r.jsx("div",{className:"row mt-3",children:a?Array.from({length:12}).map((e,t)=>r.jsx("div",{className:"col-12 col-sm-6 col-md-4 col-lg-3",children:r.jsx(x,{active:!0,paragraph:{rows:4}})},t)):n.isEmpty(s)?r.jsx(u,{title:"No favorite properties found",description:"You haven't added any properties to your favorites yet"}):s.map((e,t)=>r.jsx("div",{className:"col-12 col-sm-6 col-md-4 col-lg-3",children:r.jsx(d,{...e})},e.id||t))}),r.jsx(f,{pagination:i,handlePageChange:m,isLoading:a,itemName:"favorite properties",pageSizeOptions:["12","24","48"]})]})})};export{C as default};
