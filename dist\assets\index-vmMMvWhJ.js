import{r as i,W as De,v as ee,M as He,K as ve,J as An,b4 as Wt,R as le,b8 as Hn,t as ge,z as ie,x as _,y as Z,a$ as Zo,G as Dn,w as ne,D as Ie,I as Ln,aT as _n,av as Jo,aw as er,ax as tr,az as nr,a3 as or,E as rr,A as ir,N as ft,Q as k,a4 as Ee,a6 as vt,ba as ar,Z as lr,aA as sr,O as zt,ah as bn,P as Kn,S as ut,a5 as cr,af as Vn,C as Wn,ae as Fn,$ as hn,a1 as Xn,ag as ur,b7 as dr,T as Gn,bb as mr,bc as gr,bd as pr,be as fr,bf as vr,bg as Ft,bh as br,bi as hr,bj as qn,bk as Cr,bl as $r,bm as yr,bn as Cn,j as $,bo as xr,u as Sr,m as Ir,L as Se,X as wr,bp as Or}from"./index-Dklazue-.js";import{k as Un,b as Ue,F as Ye,M as Mr,O as Nr,T as Rr,C as Pr,g as Er,n as dt,f as Yn,i as Br,p as jr,s as Tr,r as zr,q as kr,Q as Ar,o as $n,S as Hr,m as Dr,U as Lr,y as _r,x as Kr,u as Vr}from"./useMutation-BrUrPIzr.js";import{i as Wr,o as $e,t as bt,b as Fr,C as Xr,a as Gr,B as yn}from"./button-DNhBCuue.js";import{A as xn}from"./index-Cj6uPc4c.js";var qr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"}}]},name:"right",theme:"outlined"},Ur=function(n,t){return i.createElement(De,ee({},n,{ref:t,icon:qr}))},kt=i.forwardRef(Ur),Yr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"}}]},name:"left",theme:"outlined"},Qr=function(n,t){return i.createElement(De,ee({},n,{ref:t,icon:Yr}))},At=i.forwardRef(Qr),Zr=ve.ESC,Jr=ve.TAB;function ei(e){var n=e.visible,t=e.triggerRef,o=e.onVisibleChange,r=e.autoFocus,l=e.overlayRef,a=i.useRef(!1),s=function(){if(n){var d,m;(d=t.current)===null||d===void 0||(m=d.focus)===null||m===void 0||m.call(d),o==null||o(!1)}},c=function(){var d;return(d=l.current)!==null&&d!==void 0&&d.focus?(l.current.focus(),a.current=!0,!0):!1},u=function(d){switch(d.keyCode){case Zr:s();break;case Jr:{var m=!1;a.current||(m=c()),m?d.preventDefault():s();break}}};i.useEffect(function(){return n?(window.addEventListener("keydown",u),r&&He(c,3),function(){window.removeEventListener("keydown",u),a.current=!1}):function(){a.current=!1}},[n])}var ti=i.forwardRef(function(e,n){var t=e.overlay,o=e.arrow,r=e.prefixCls,l=i.useMemo(function(){var s;return typeof t=="function"?s=t():s=t,s},[t]),a=An(n,Wt(l));return le.createElement(le.Fragment,null,o&&le.createElement("div",{className:"".concat(r,"-arrow")}),le.cloneElement(l,{ref:Hn(l)?a:void 0}))}),ze={adjustX:1,adjustY:1},ke=[0,0],ni={topLeft:{points:["bl","tl"],overflow:ze,offset:[0,-4],targetOffset:ke},top:{points:["bc","tc"],overflow:ze,offset:[0,-4],targetOffset:ke},topRight:{points:["br","tr"],overflow:ze,offset:[0,-4],targetOffset:ke},bottomLeft:{points:["tl","bl"],overflow:ze,offset:[0,4],targetOffset:ke},bottom:{points:["tc","bc"],overflow:ze,offset:[0,4],targetOffset:ke},bottomRight:{points:["tr","br"],overflow:ze,offset:[0,4],targetOffset:ke}},oi=["arrow","prefixCls","transitionName","animation","align","placement","placements","getPopupContainer","showAction","hideAction","overlayClassName","overlayStyle","visible","trigger","autoFocus","overlay","children","onVisibleChange"];function ri(e,n){var t,o=e.arrow,r=o===void 0?!1:o,l=e.prefixCls,a=l===void 0?"rc-dropdown":l,s=e.transitionName,c=e.animation,u=e.align,g=e.placement,d=g===void 0?"bottomLeft":g,m=e.placements,b=m===void 0?ni:m,p=e.getPopupContainer,f=e.showAction,v=e.hideAction,I=e.overlayClassName,M=e.overlayStyle,y=e.visible,x=e.trigger,h=x===void 0?["hover"]:x,C=e.autoFocus,R=e.overlay,S=e.children,w=e.onVisibleChange,O=ge(e,oi),z=le.useState(),j=ie(z,2),T=j[0],N=j[1],P="visible"in e?y:T,F=le.useRef(null),E=le.useRef(null),A=le.useRef(null);le.useImperativeHandle(n,function(){return F.current});var W=function(B){N(B),w==null||w(B)};ei({visible:P,triggerRef:A,onVisibleChange:W,autoFocus:C,overlayRef:E});var X=function(B){var L=e.onOverlayClick;N(!1),L&&L(B)},q=function(){return le.createElement(ti,{ref:E,overlay:R,prefixCls:a,arrow:r})},G=function(){return typeof R=="function"?q:q()},K=function(){var B=e.minOverlayWidthMatchTrigger,L=e.alignPoint;return"minOverlayWidthMatchTrigger"in e?B:!L},H=function(){var B=e.openClassName;return B!==void 0?B:"".concat(a,"-open")},D=le.cloneElement(S,{className:_((t=S.props)===null||t===void 0?void 0:t.className,P&&H()),ref:Hn(S)?An(A,Wt(S)):void 0}),te=v;return!te&&h.indexOf("contextMenu")!==-1&&(te=["click"]),le.createElement(Un,ee({builtinPlacements:b},O,{prefixCls:a,ref:F,popupClassName:_(I,Z({},"".concat(a,"-show-arrow"),r)),popupStyle:M,action:h,showAction:f,hideAction:te,popupPlacement:d,popupAlign:u,popupTransitionName:s,popupAnimation:c,popupVisible:P,stretch:K()?"minWidth":"",popup:G(),onPopupVisibleChange:W,onPopupClick:X,getPopupContainer:p}),D)}const ii=le.forwardRef(ri),ai=e=>typeof e!="object"&&typeof e!="function"||e===null;var Qn=i.createContext(null);function Zn(e,n){return e===void 0?null:"".concat(e,"-").concat(n)}function Jn(e){var n=i.useContext(Qn);return Zn(n,e)}var li=["children","locked"],fe=i.createContext(null);function si(e,n){var t=ne({},e);return Object.keys(n).forEach(function(o){var r=n[o];r!==void 0&&(t[o]=r)}),t}function Qe(e){var n=e.children,t=e.locked,o=ge(e,li),r=i.useContext(fe),l=Zo(function(){return si(r,o)},[r,o],function(a,s){return!t&&(a[0]!==s[0]||!Dn(a[1],s[1],!0))});return i.createElement(fe.Provider,{value:l},n)}var ci=[],eo=i.createContext(null);function ht(){return i.useContext(eo)}var to=i.createContext(ci);function Le(e){var n=i.useContext(to);return i.useMemo(function(){return e!==void 0?[].concat(Ie(n),[e]):n},[n,e])}var no=i.createContext(null),Xt=i.createContext({});function Sn(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(Wr(e)){var t=e.nodeName.toLowerCase(),o=["input","select","textarea","button"].includes(t)||e.isContentEditable||t==="a"&&!!e.getAttribute("href"),r=e.getAttribute("tabindex"),l=Number(r),a=null;return r&&!Number.isNaN(l)?a=l:o&&a===null&&(a=0),o&&e.disabled&&(a=null),a!==null&&(a>=0||n&&a<0)}return!1}function ui(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,t=Ie(e.querySelectorAll("*")).filter(function(o){return Sn(o,n)});return Sn(e,n)&&t.unshift(e),t}var Ht=ve.LEFT,Dt=ve.RIGHT,Lt=ve.UP,st=ve.DOWN,ct=ve.ENTER,oo=ve.ESC,Xe=ve.HOME,Ge=ve.END,In=[Lt,st,Ht,Dt];function di(e,n,t,o){var r,l="prev",a="next",s="children",c="parent";if(e==="inline"&&o===ct)return{inlineTrigger:!0};var u=Z(Z({},Lt,l),st,a),g=Z(Z(Z(Z({},Ht,t?a:l),Dt,t?l:a),st,s),ct,s),d=Z(Z(Z(Z(Z(Z({},Lt,l),st,a),ct,s),oo,c),Ht,t?s:c),Dt,t?c:s),m={inline:u,horizontal:g,vertical:d,inlineSub:u,horizontalSub:d,verticalSub:d},b=(r=m["".concat(e).concat(n?"":"Sub")])===null||r===void 0?void 0:r[o];switch(b){case l:return{offset:-1,sibling:!0};case a:return{offset:1,sibling:!0};case c:return{offset:-1,sibling:!1};case s:return{offset:1,sibling:!1};default:return null}}function mi(e){for(var n=e;n;){if(n.getAttribute("data-menu-list"))return n;n=n.parentElement}return null}function gi(e,n){for(var t=e||document.activeElement;t;){if(n.has(t))return t;t=t.parentElement}return null}function Gt(e,n){var t=ui(e,!0);return t.filter(function(o){return n.has(o)})}function wn(e,n,t){var o=arguments.length>3&&arguments[3]!==void 0?arguments[3]:1;if(!e)return null;var r=Gt(e,n),l=r.length,a=r.findIndex(function(s){return t===s});return o<0?a===-1?a=l-1:a-=1:o>0&&(a+=1),a=(a+l)%l,r[a]}var _t=function(n,t){var o=new Set,r=new Map,l=new Map;return n.forEach(function(a){var s=document.querySelector("[data-menu-id='".concat(Zn(t,a),"']"));s&&(o.add(s),l.set(s,a),r.set(a,s))}),{elements:o,key2element:r,element2key:l}};function pi(e,n,t,o,r,l,a,s,c,u){var g=i.useRef(),d=i.useRef();d.current=n;var m=function(){He.cancel(g.current)};return i.useEffect(function(){return function(){m()}},[]),function(b){var p=b.which;if([].concat(In,[ct,oo,Xe,Ge]).includes(p)){var f=l(),v=_t(f,o),I=v,M=I.elements,y=I.key2element,x=I.element2key,h=y.get(n),C=gi(h,M),R=x.get(C),S=di(e,a(R,!0).length===1,t,p);if(!S&&p!==Xe&&p!==Ge)return;(In.includes(p)||[Xe,Ge].includes(p))&&b.preventDefault();var w=function(E){if(E){var A=E,W=E.querySelector("a");W!=null&&W.getAttribute("href")&&(A=W);var X=x.get(E);s(X),m(),g.current=He(function(){d.current===X&&A.focus()})}};if([Xe,Ge].includes(p)||S.sibling||!C){var O;!C||e==="inline"?O=r.current:O=mi(C);var z,j=Gt(O,M);p===Xe?z=j[0]:p===Ge?z=j[j.length-1]:z=wn(O,M,C,S.offset),w(z)}else if(S.inlineTrigger)c(R);else if(S.offset>0)c(R,!0),m(),g.current=He(function(){v=_t(f,o);var F=C.getAttribute("aria-controls"),E=document.getElementById(F),A=wn(E,v.elements);w(A)},5);else if(S.offset<0){var T=a(R,!0),N=T[T.length-2],P=y.get(N);c(N,!1),w(P)}}u==null||u(b)}}function fi(e){Promise.resolve().then(e)}var qt="__RC_UTIL_PATH_SPLIT__",On=function(n){return n.join(qt)},vi=function(n){return n.split(qt)},Kt="rc-menu-more";function bi(){var e=i.useState({}),n=ie(e,2),t=n[1],o=i.useRef(new Map),r=i.useRef(new Map),l=i.useState([]),a=ie(l,2),s=a[0],c=a[1],u=i.useRef(0),g=i.useRef(!1),d=function(){g.current||t({})},m=i.useCallback(function(y,x){var h=On(x);r.current.set(h,y),o.current.set(y,h),u.current+=1;var C=u.current;fi(function(){C===u.current&&d()})},[]),b=i.useCallback(function(y,x){var h=On(x);r.current.delete(h),o.current.delete(y)},[]),p=i.useCallback(function(y){c(y)},[]),f=i.useCallback(function(y,x){var h=o.current.get(y)||"",C=vi(h);return x&&s.includes(C[0])&&C.unshift(Kt),C},[s]),v=i.useCallback(function(y,x){return y.filter(function(h){return h!==void 0}).some(function(h){var C=f(h,!0);return C.includes(x)})},[f]),I=function(){var x=Ie(o.current.keys());return s.length&&x.push(Kt),x},M=i.useCallback(function(y){var x="".concat(o.current.get(y)).concat(qt),h=new Set;return Ie(r.current.keys()).forEach(function(C){C.startsWith(x)&&h.add(r.current.get(C))}),h},[]);return i.useEffect(function(){return function(){g.current=!0}},[]),{registerPath:m,unregisterPath:b,refreshOverflowKeys:p,isSubPathKey:v,getKeyPath:f,getKeys:I,getSubPathKeys:M}}function qe(e){var n=i.useRef(e);n.current=e;var t=i.useCallback(function(){for(var o,r=arguments.length,l=new Array(r),a=0;a<r;a++)l[a]=arguments[a];return(o=n.current)===null||o===void 0?void 0:o.call.apply(o,[n].concat(l))},[]);return e?t:void 0}var hi=Math.random().toFixed(5).toString().slice(2),Mn=0;function Ci(e){var n=Ue(e,{value:e}),t=ie(n,2),o=t[0],r=t[1];return i.useEffect(function(){Mn+=1;var l="".concat(hi,"-").concat(Mn);r("rc-menu-uuid-".concat(l))},[]),o}function ro(e,n,t,o){var r=i.useContext(fe),l=r.activeKey,a=r.onActive,s=r.onInactive,c={active:l===e};return n||(c.onMouseEnter=function(u){t==null||t({key:e,domEvent:u}),a(e)},c.onMouseLeave=function(u){o==null||o({key:e,domEvent:u}),s(e)}),c}function io(e){var n=i.useContext(fe),t=n.mode,o=n.rtl,r=n.inlineIndent;if(t!=="inline")return null;var l=e;return o?{paddingRight:l*r}:{paddingLeft:l*r}}function ao(e){var n=e.icon,t=e.props,o=e.children,r;return n===null||n===!1?null:(typeof n=="function"?r=i.createElement(n,ne({},t)):typeof n!="boolean"&&(r=n),r||o||null)}var $i=["item"];function mt(e){var n=e.item,t=ge(e,$i);return Object.defineProperty(t,"item",{get:function(){return Ln(!1,"`info.item` is deprecated since we will move to function component that not provides React Node instance in future."),n}}),t}var yi=["title","attribute","elementRef"],xi=["style","className","eventKey","warnKey","disabled","itemIcon","children","role","onMouseEnter","onMouseLeave","onClick","onKeyDown","onFocus"],Si=["active"],Ii=function(e){Jo(t,e);var n=er(t);function t(){return tr(this,t),n.apply(this,arguments)}return nr(t,[{key:"render",value:function(){var r=this.props,l=r.title,a=r.attribute,s=r.elementRef,c=ge(r,yi),u=$e(c,["eventKey","popupClassName","popupOffset","onTitleClick"]);return Ln(!a,"`attribute` of Menu.Item is deprecated. Please pass attribute directly."),i.createElement(Ye.Item,ee({},a,{title:typeof l=="string"?l:void 0},u,{ref:s}))}}]),t}(i.Component),wi=i.forwardRef(function(e,n){var t=e.style,o=e.className,r=e.eventKey;e.warnKey;var l=e.disabled,a=e.itemIcon,s=e.children,c=e.role,u=e.onMouseEnter,g=e.onMouseLeave,d=e.onClick,m=e.onKeyDown,b=e.onFocus,p=ge(e,xi),f=Jn(r),v=i.useContext(fe),I=v.prefixCls,M=v.onItemClick,y=v.disabled,x=v.overflowDisabled,h=v.itemIcon,C=v.selectedKeys,R=v.onActive,S=i.useContext(Xt),w=S._internalRenderMenuItem,O="".concat(I,"-item"),z=i.useRef(),j=i.useRef(),T=y||l,N=_n(n,j),P=Le(r),F=function(L){return{key:r,keyPath:Ie(P).reverse(),item:z.current,domEvent:L}},E=a||h,A=ro(r,T,u,g),W=A.active,X=ge(A,Si),q=C.includes(r),G=io(P.length),K=function(L){if(!T){var oe=F(L);d==null||d(mt(oe)),M(oe)}},H=function(L){if(m==null||m(L),L.which===ve.ENTER){var oe=F(L);d==null||d(mt(oe)),M(oe)}},D=function(L){R(r),b==null||b(L)},te={};e.role==="option"&&(te["aria-selected"]=q);var Y=i.createElement(Ii,ee({ref:z,elementRef:N,role:c===null?"none":c||"menuitem",tabIndex:l?null:-1,"data-menu-id":x&&f?null:f},$e(p,["extra"]),X,te,{component:"li","aria-disabled":l,style:ne(ne({},G),t),className:_(O,Z(Z(Z({},"".concat(O,"-active"),W),"".concat(O,"-selected"),q),"".concat(O,"-disabled"),T),o),onClick:K,onKeyDown:H,onFocus:D}),s,i.createElement(ao,{props:ne(ne({},e),{},{isSelected:q}),icon:E}));return w&&(Y=w(Y,e,{selected:q})),Y});function Oi(e,n){var t=e.eventKey,o=ht(),r=Le(t);return i.useEffect(function(){if(o)return o.registerPath(t,r),function(){o.unregisterPath(t,r)}},[r]),o?null:i.createElement(wi,ee({},e,{ref:n}))}const Ct=i.forwardRef(Oi);var Mi=["className","children"],Ni=function(n,t){var o=n.className,r=n.children,l=ge(n,Mi),a=i.useContext(fe),s=a.prefixCls,c=a.mode,u=a.rtl;return i.createElement("ul",ee({className:_(s,u&&"".concat(s,"-rtl"),"".concat(s,"-sub"),"".concat(s,"-").concat(c==="inline"?"inline":"vertical"),o),role:"menu"},l,{"data-menu-list":!0,ref:t}),r)},Ut=i.forwardRef(Ni);Ut.displayName="SubMenuList";function Yt(e,n){return bt(e).map(function(t,o){if(i.isValidElement(t)){var r,l,a=t.key,s=(r=(l=t.props)===null||l===void 0?void 0:l.eventKey)!==null&&r!==void 0?r:a,c=s==null;c&&(s="tmp_key-".concat([].concat(Ie(n),[o]).join("-")));var u={key:s,eventKey:s};return i.cloneElement(t,u)}return t})}var ae={adjustX:1,adjustY:1},Ri={topLeft:{points:["bl","tl"],overflow:ae},topRight:{points:["br","tr"],overflow:ae},bottomLeft:{points:["tl","bl"],overflow:ae},bottomRight:{points:["tr","br"],overflow:ae},leftTop:{points:["tr","tl"],overflow:ae},leftBottom:{points:["br","bl"],overflow:ae},rightTop:{points:["tl","tr"],overflow:ae},rightBottom:{points:["bl","br"],overflow:ae}},Pi={topLeft:{points:["bl","tl"],overflow:ae},topRight:{points:["br","tr"],overflow:ae},bottomLeft:{points:["tl","bl"],overflow:ae},bottomRight:{points:["tr","br"],overflow:ae},rightTop:{points:["tr","tl"],overflow:ae},rightBottom:{points:["br","bl"],overflow:ae},leftTop:{points:["tl","tr"],overflow:ae},leftBottom:{points:["bl","br"],overflow:ae}};function lo(e,n,t){if(n)return n;if(t)return t[e]||t.other}var Ei={horizontal:"bottomLeft",vertical:"rightTop","vertical-left":"rightTop","vertical-right":"leftTop"};function Bi(e){var n=e.prefixCls,t=e.visible,o=e.children,r=e.popup,l=e.popupStyle,a=e.popupClassName,s=e.popupOffset,c=e.disabled,u=e.mode,g=e.onVisibleChange,d=i.useContext(fe),m=d.getPopupContainer,b=d.rtl,p=d.subMenuOpenDelay,f=d.subMenuCloseDelay,v=d.builtinPlacements,I=d.triggerSubMenuAction,M=d.forceSubMenuRender,y=d.rootClassName,x=d.motion,h=d.defaultMotions,C=i.useState(!1),R=ie(C,2),S=R[0],w=R[1],O=b?ne(ne({},Pi),v):ne(ne({},Ri),v),z=Ei[u],j=lo(u,x,h),T=i.useRef(j);u!=="inline"&&(T.current=j);var N=ne(ne({},T.current),{},{leavedClassName:"".concat(n,"-hidden"),removeOnLeave:!1,motionAppear:!0}),P=i.useRef();return i.useEffect(function(){return P.current=He(function(){w(t)}),function(){He.cancel(P.current)}},[t]),i.createElement(Un,{prefixCls:n,popupClassName:_("".concat(n,"-popup"),Z({},"".concat(n,"-rtl"),b),a,y),stretch:u==="horizontal"?"minWidth":null,getPopupContainer:m,builtinPlacements:O,popupPlacement:z,popupVisible:S,popup:r,popupStyle:l,popupAlign:s&&{offset:s},action:c?[]:[I],mouseEnterDelay:p,mouseLeaveDelay:f,onPopupVisibleChange:g,forceRender:M,popupMotion:N,fresh:!0},o)}function ji(e){var n=e.id,t=e.open,o=e.keyPath,r=e.children,l="inline",a=i.useContext(fe),s=a.prefixCls,c=a.forceSubMenuRender,u=a.motion,g=a.defaultMotions,d=a.mode,m=i.useRef(!1);m.current=d===l;var b=i.useState(!m.current),p=ie(b,2),f=p[0],v=p[1],I=m.current?t:!1;i.useEffect(function(){m.current&&v(!1)},[d]);var M=ne({},lo(l,u,g));o.length>1&&(M.motionAppear=!1);var y=M.onVisibleChanged;return M.onVisibleChanged=function(x){return!m.current&&!x&&v(!0),y==null?void 0:y(x)},f?null:i.createElement(Qe,{mode:l,locked:!m.current},i.createElement(or,ee({visible:I},M,{forceRender:c,removeOnLeave:!1,leavedClassName:"".concat(s,"-hidden")}),function(x){var h=x.className,C=x.style;return i.createElement(Ut,{id:n,className:h,style:C},r)}))}var Ti=["style","className","title","eventKey","warnKey","disabled","internalPopupClose","children","itemIcon","expandIcon","popupClassName","popupOffset","popupStyle","onClick","onMouseEnter","onMouseLeave","onTitleClick","onTitleMouseEnter","onTitleMouseLeave"],zi=["active"],ki=i.forwardRef(function(e,n){var t=e.style,o=e.className,r=e.title,l=e.eventKey;e.warnKey;var a=e.disabled,s=e.internalPopupClose,c=e.children,u=e.itemIcon,g=e.expandIcon,d=e.popupClassName,m=e.popupOffset,b=e.popupStyle,p=e.onClick,f=e.onMouseEnter,v=e.onMouseLeave,I=e.onTitleClick,M=e.onTitleMouseEnter,y=e.onTitleMouseLeave,x=ge(e,Ti),h=Jn(l),C=i.useContext(fe),R=C.prefixCls,S=C.mode,w=C.openKeys,O=C.disabled,z=C.overflowDisabled,j=C.activeKey,T=C.selectedKeys,N=C.itemIcon,P=C.expandIcon,F=C.onItemClick,E=C.onOpenChange,A=C.onActive,W=i.useContext(Xt),X=W._internalRenderSubMenuItem,q=i.useContext(no),G=q.isSubPathKey,K=Le(),H="".concat(R,"-submenu"),D=O||a,te=i.useRef(),Y=i.useRef(),B=u??N,L=g??P,oe=w.includes(l),re=!z&&oe,Be=G(T,l),je=ro(l,D,M,y),be=je.active,we=ge(je,zi),Oe=i.useState(!1),Q=ie(Oe,2),Me=Q[0],_e=Q[1],et=function(se){D||_e(se)},It=function(se){et(!0),f==null||f({key:l,domEvent:se})},tt=function(se){et(!1),v==null||v({key:l,domEvent:se})},he=i.useMemo(function(){return be||(S!=="inline"?Me||G([j],l):!1)},[S,be,j,Me,l,G]),wt=io(K.length),Ot=function(se){D||(I==null||I({key:l,domEvent:se}),S==="inline"&&E(l,!oe))},nt=qe(function(pe){p==null||p(mt(pe)),F(pe)}),ot=function(se){S!=="inline"&&E(l,se)},Mt=function(){A(l)},ye=h&&"".concat(h,"-popup"),Ke=i.useMemo(function(){return i.createElement(ao,{icon:S!=="horizontal"?L:void 0,props:ne(ne({},e),{},{isOpen:re,isSubMenu:!0})},i.createElement("i",{className:"".concat(H,"-arrow")}))},[S,L,e,re,H]),Ne=i.createElement("div",ee({role:"menuitem",style:wt,className:"".concat(H,"-title"),tabIndex:D?null:-1,ref:te,title:typeof r=="string"?r:null,"data-menu-id":z&&h?null:h,"aria-expanded":re,"aria-haspopup":!0,"aria-controls":ye,"aria-disabled":D,onClick:Ot,onFocus:Mt},we),r,Ke),Ve=i.useRef(S);if(S!=="inline"&&K.length>1?Ve.current="vertical":Ve.current=S,!z){var Te=Ve.current;Ne=i.createElement(Bi,{mode:Te,prefixCls:H,visible:!s&&re&&S!=="inline",popupClassName:d,popupOffset:m,popupStyle:b,popup:i.createElement(Qe,{mode:Te==="horizontal"?"vertical":Te},i.createElement(Ut,{id:ye,ref:Y},c)),disabled:D,onVisibleChange:ot},Ne)}var de=i.createElement(Ye.Item,ee({ref:n,role:"none"},x,{component:"li",style:t,className:_(H,"".concat(H,"-").concat(S),o,Z(Z(Z(Z({},"".concat(H,"-open"),re),"".concat(H,"-active"),he),"".concat(H,"-selected"),Be),"".concat(H,"-disabled"),D)),onMouseEnter:It,onMouseLeave:tt}),Ne,!z&&i.createElement(ji,{id:ye,open:re,keyPath:K},c));return X&&(de=X(de,e,{selected:Be,active:he,open:re,disabled:D})),i.createElement(Qe,{onItemClick:nt,mode:S==="horizontal"?"vertical":S,itemIcon:B,expandIcon:L},de)}),$t=i.forwardRef(function(e,n){var t=e.eventKey,o=e.children,r=Le(t),l=Yt(o,r),a=ht();i.useEffect(function(){if(a)return a.registerPath(t,r),function(){a.unregisterPath(t,r)}},[r]);var s;return a?s=l:s=i.createElement(ki,ee({ref:n},e),l),i.createElement(to.Provider,{value:r},s)});function Qt(e){var n=e.className,t=e.style,o=i.useContext(fe),r=o.prefixCls,l=ht();return l?null:i.createElement("li",{role:"separator",className:_("".concat(r,"-item-divider"),n),style:t})}var Ai=["className","title","eventKey","children"],Hi=i.forwardRef(function(e,n){var t=e.className,o=e.title;e.eventKey;var r=e.children,l=ge(e,Ai),a=i.useContext(fe),s=a.prefixCls,c="".concat(s,"-item-group");return i.createElement("li",ee({ref:n,role:"presentation"},l,{onClick:function(g){return g.stopPropagation()},className:_(c,t)}),i.createElement("div",{role:"presentation",className:"".concat(c,"-title"),title:typeof o=="string"?o:void 0},o),i.createElement("ul",{role:"group",className:"".concat(c,"-list")},r))}),Zt=i.forwardRef(function(e,n){var t=e.eventKey,o=e.children,r=Le(t),l=Yt(o,r),a=ht();return a?l:i.createElement(Hi,ee({ref:n},$e(e,["warnKey"])),l)}),Di=["label","children","key","type","extra"];function Vt(e,n,t){var o=n.item,r=n.group,l=n.submenu,a=n.divider;return(e||[]).map(function(s,c){if(s&&rr(s)==="object"){var u=s,g=u.label,d=u.children,m=u.key,b=u.type,p=u.extra,f=ge(u,Di),v=m??"tmp-".concat(c);return d||b==="group"?b==="group"?i.createElement(r,ee({key:v},f,{title:g}),Vt(d,n,t)):i.createElement(l,ee({key:v},f,{title:g}),Vt(d,n,t)):b==="divider"?i.createElement(a,ee({key:v},f)):i.createElement(o,ee({key:v},f,{extra:p}),g,(!!p||p===0)&&i.createElement("span",{className:"".concat(t,"-item-extra")},p))}return null}).filter(function(s){return s})}function Nn(e,n,t,o,r){var l=e,a=ne({divider:Qt,item:Ct,group:Zt,submenu:$t},o);return n&&(l=Vt(n,a,r)),Yt(l,t)}var Li=["prefixCls","rootClassName","style","className","tabIndex","items","children","direction","id","mode","inlineCollapsed","disabled","disabledOverflow","subMenuOpenDelay","subMenuCloseDelay","forceSubMenuRender","defaultOpenKeys","openKeys","activeKey","defaultActiveFirst","selectable","multiple","defaultSelectedKeys","selectedKeys","onSelect","onDeselect","inlineIndent","motion","defaultMotions","triggerSubMenuAction","builtinPlacements","itemIcon","expandIcon","overflowedIndicator","overflowedIndicatorPopupClassName","getPopupContainer","onClick","onOpenChange","onKeyDown","openAnimation","openTransitionName","_internalRenderMenuItem","_internalRenderSubMenuItem","_internalComponents"],Pe=[],_i=i.forwardRef(function(e,n){var t,o=e,r=o.prefixCls,l=r===void 0?"rc-menu":r,a=o.rootClassName,s=o.style,c=o.className,u=o.tabIndex,g=u===void 0?0:u,d=o.items,m=o.children,b=o.direction,p=o.id,f=o.mode,v=f===void 0?"vertical":f,I=o.inlineCollapsed,M=o.disabled,y=o.disabledOverflow,x=o.subMenuOpenDelay,h=x===void 0?.1:x,C=o.subMenuCloseDelay,R=C===void 0?.1:C,S=o.forceSubMenuRender,w=o.defaultOpenKeys,O=o.openKeys,z=o.activeKey,j=o.defaultActiveFirst,T=o.selectable,N=T===void 0?!0:T,P=o.multiple,F=P===void 0?!1:P,E=o.defaultSelectedKeys,A=o.selectedKeys,W=o.onSelect,X=o.onDeselect,q=o.inlineIndent,G=q===void 0?24:q,K=o.motion,H=o.defaultMotions,D=o.triggerSubMenuAction,te=D===void 0?"hover":D,Y=o.builtinPlacements,B=o.itemIcon,L=o.expandIcon,oe=o.overflowedIndicator,re=oe===void 0?"...":oe,Be=o.overflowedIndicatorPopupClassName,je=o.getPopupContainer,be=o.onClick,we=o.onOpenChange,Oe=o.onKeyDown;o.openAnimation,o.openTransitionName;var Q=o._internalRenderMenuItem,Me=o._internalRenderSubMenuItem,_e=o._internalComponents,et=ge(o,Li),It=i.useMemo(function(){return[Nn(m,d,Pe,_e,l),Nn(m,d,Pe,{},l)]},[m,d,_e]),tt=ie(It,2),he=tt[0],wt=tt[1],Ot=i.useState(!1),nt=ie(Ot,2),ot=nt[0],Mt=nt[1],ye=i.useRef(),Ke=Ci(p),Ne=b==="rtl",Ve=Ue(w,{value:O,postState:function(V){return V||Pe}}),Te=ie(Ve,2),de=Te[0],pe=Te[1],se=function(V){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;function ce(){pe(V),we==null||we(V)}U?ir.flushSync(ce):ce()},xo=i.useState(de),tn=ie(xo,2),So=tn[0],Io=tn[1],Nt=i.useRef(!1),wo=i.useMemo(function(){return(v==="inline"||v==="vertical")&&I?["vertical",I]:[v,!1]},[v,I]),nn=ie(wo,2),rt=nn[0],Rt=nn[1],on=rt==="inline",Oo=i.useState(rt),rn=ie(Oo,2),xe=rn[0],Mo=rn[1],No=i.useState(Rt),an=ie(No,2),Ro=an[0],Po=an[1];i.useEffect(function(){Mo(rt),Po(Rt),Nt.current&&(on?pe(So):se(Pe))},[rt,Rt]);var Eo=i.useState(0),ln=ie(Eo,2),it=ln[0],Bo=ln[1],Pt=it>=he.length-1||xe!=="horizontal"||y;i.useEffect(function(){on&&Io(de)},[de]),i.useEffect(function(){return Nt.current=!0,function(){Nt.current=!1}},[]);var Re=bi(),sn=Re.registerPath,cn=Re.unregisterPath,jo=Re.refreshOverflowKeys,un=Re.isSubPathKey,To=Re.getKeyPath,dn=Re.getKeys,zo=Re.getSubPathKeys,ko=i.useMemo(function(){return{registerPath:sn,unregisterPath:cn}},[sn,cn]),Ao=i.useMemo(function(){return{isSubPathKey:un}},[un]);i.useEffect(function(){jo(Pt?Pe:he.slice(it+1).map(function(J){return J.key}))},[it,Pt]);var Ho=Ue(z||j&&((t=he[0])===null||t===void 0?void 0:t.key),{value:z}),mn=ie(Ho,2),We=mn[0],Et=mn[1],Do=qe(function(J){Et(J)}),Lo=qe(function(){Et(void 0)});i.useImperativeHandle(n,function(){return{list:ye.current,focus:function(V){var U,ce=dn(),ue=_t(ce,Ke),lt=ue.elements,Bt=ue.key2element,Yo=ue.element2key,fn=Gt(ye.current,lt),vn=We??(fn[0]?Yo.get(fn[0]):(U=he.find(function(Qo){return!Qo.props.disabled}))===null||U===void 0?void 0:U.key),Fe=Bt.get(vn);if(vn&&Fe){var jt;Fe==null||(jt=Fe.focus)===null||jt===void 0||jt.call(Fe,V)}}}});var _o=Ue(E||[],{value:A,postState:function(V){return Array.isArray(V)?V:V==null?Pe:[V]}}),gn=ie(_o,2),at=gn[0],Ko=gn[1],Vo=function(V){if(N){var U=V.key,ce=at.includes(U),ue;F?ce?ue=at.filter(function(Bt){return Bt!==U}):ue=[].concat(Ie(at),[U]):ue=[U],Ko(ue);var lt=ne(ne({},V),{},{selectedKeys:ue});ce?X==null||X(lt):W==null||W(lt)}!F&&de.length&&xe!=="inline"&&se(Pe)},Wo=qe(function(J){be==null||be(mt(J)),Vo(J)}),pn=qe(function(J,V){var U=de.filter(function(ue){return ue!==J});if(V)U.push(J);else if(xe!=="inline"){var ce=zo(J);U=U.filter(function(ue){return!ce.has(ue)})}Dn(de,U,!0)||se(U,!0)}),Fo=function(V,U){var ce=U??!de.includes(V);pn(V,ce)},Xo=pi(xe,We,Ne,Ke,ye,dn,To,Et,Fo,Oe);i.useEffect(function(){Mt(!0)},[]);var Go=i.useMemo(function(){return{_internalRenderMenuItem:Q,_internalRenderSubMenuItem:Me}},[Q,Me]),qo=xe!=="horizontal"||y?he:he.map(function(J,V){return i.createElement(Qe,{key:J.key,overflowDisabled:V>it},J)}),Uo=i.createElement(Ye,ee({id:p,ref:ye,prefixCls:"".concat(l,"-overflow"),component:"ul",itemComponent:Ct,className:_(l,"".concat(l,"-root"),"".concat(l,"-").concat(xe),c,Z(Z({},"".concat(l,"-inline-collapsed"),Ro),"".concat(l,"-rtl"),Ne),a),dir:b,style:s,role:"menu",tabIndex:g,data:qo,renderRawItem:function(V){return V},renderRawRest:function(V){var U=V.length,ce=U?he.slice(-U):null;return i.createElement($t,{eventKey:Kt,title:re,disabled:Pt,internalPopupClose:U===0,popupClassName:Be},ce)},maxCount:xe!=="horizontal"||y?Ye.INVALIDATE:Ye.RESPONSIVE,ssr:"full","data-menu-list":!0,onVisibleChange:function(V){Bo(V)},onKeyDown:Xo},et));return i.createElement(Xt.Provider,{value:Go},i.createElement(Qn.Provider,{value:Ke},i.createElement(Qe,{prefixCls:l,rootClassName:a,mode:xe,openKeys:de,rtl:Ne,disabled:M,motion:ot?K:null,defaultMotions:ot?H:null,activeKey:We,onActive:Do,onInactive:Lo,selectedKeys:at,inlineIndent:G,subMenuOpenDelay:h,subMenuCloseDelay:R,forceSubMenuRender:S,builtinPlacements:Y,triggerSubMenuAction:te,getPopupContainer:je,itemIcon:B,expandIcon:L,onItemClick:Wo,onOpenChange:pn},i.createElement(no.Provider,{value:Ao},Uo),i.createElement("div",{style:{display:"none"},"aria-hidden":!0},i.createElement(eo.Provider,{value:ko},wt)))))}),Ze=_i;Ze.Item=Ct;Ze.SubMenu=$t;Ze.ItemGroup=Zt;Ze.Divider=Qt;var Ki={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"bars",theme:"outlined"},Vi=function(n,t){return i.createElement(De,ee({},n,{ref:t,icon:Ki}))},Wi=i.forwardRef(Vi);const so=i.createContext({siderHook:{addSider:()=>null,removeSider:()=>null}}),Fi=e=>{const{antCls:n,componentCls:t,colorText:o,footerBg:r,headerHeight:l,headerPadding:a,headerColor:s,footerPadding:c,fontSize:u,bodyBg:g,headerBg:d}=e;return{[t]:{display:"flex",flex:"auto",flexDirection:"column",minHeight:0,background:g,"&, *":{boxSizing:"border-box"},[`&${t}-has-sider`]:{flexDirection:"row",[`> ${t}, > ${t}-content`]:{width:0}},[`${t}-header, &${t}-footer`]:{flex:"0 0 auto"},"&-rtl":{direction:"rtl"}},[`${t}-header`]:{height:l,padding:a,color:s,lineHeight:k(l),background:d,[`${n}-menu`]:{lineHeight:"inherit"}},[`${t}-footer`]:{padding:c,color:o,fontSize:u,background:r},[`${t}-content`]:{flex:"auto",color:o,minHeight:0}}},co=e=>{const{colorBgLayout:n,controlHeight:t,controlHeightLG:o,colorText:r,controlHeightSM:l,marginXXS:a,colorTextLightSolid:s,colorBgContainer:c}=e,u=o*1.25;return{colorBgHeader:"#001529",colorBgBody:n,colorBgTrigger:"#002140",bodyBg:n,headerBg:"#001529",headerHeight:t*2,headerPadding:`0 ${u}px`,headerColor:r,footerPadding:`${l}px ${u}px`,footerBg:n,siderBg:"#001529",triggerHeight:o+a*2,triggerBg:"#002140",triggerColor:s,zeroTriggerWidth:o,zeroTriggerHeight:o,lightSiderBg:c,lightTriggerBg:c,lightTriggerColor:r}},uo=[["colorBgBody","bodyBg"],["colorBgHeader","headerBg"],["colorBgTrigger","triggerBg"]],mo=ft("Layout",e=>[Fi(e)],co,{deprecatedTokens:uo}),Xi=e=>{const{componentCls:n,siderBg:t,motionDurationMid:o,motionDurationSlow:r,antCls:l,triggerHeight:a,triggerColor:s,triggerBg:c,headerHeight:u,zeroTriggerWidth:g,zeroTriggerHeight:d,borderRadiusLG:m,lightSiderBg:b,lightTriggerColor:p,lightTriggerBg:f,bodyBg:v}=e;return{[n]:{position:"relative",minWidth:0,background:t,transition:`all ${o}, background 0s`,"&-has-trigger":{paddingBottom:a},"&-right":{order:1},[`${n}-children`]:{height:"100%",marginTop:-.1,paddingTop:.1,[`${l}-menu${l}-menu-inline-collapsed`]:{width:"auto"}},[`&-zero-width ${n}-children`]:{overflow:"hidden"},[`${n}-trigger`]:{position:"fixed",bottom:0,zIndex:1,height:a,color:s,lineHeight:k(a),textAlign:"center",background:c,cursor:"pointer",transition:`all ${o}`},[`${n}-zero-width-trigger`]:{position:"absolute",top:u,insetInlineEnd:e.calc(g).mul(-1).equal(),zIndex:1,width:g,height:d,color:s,fontSize:e.fontSizeXL,display:"flex",alignItems:"center",justifyContent:"center",background:t,borderRadius:`0 ${k(m)} ${k(m)} 0`,cursor:"pointer",transition:`background ${r} ease`,"&::after":{position:"absolute",inset:0,background:"transparent",transition:`all ${r}`,content:'""'},"&:hover::after":{background:"rgba(255, 255, 255, 0.2)"},"&-right":{insetInlineStart:e.calc(g).mul(-1).equal(),borderRadius:`${k(m)} 0 0 ${k(m)}`}},"&-light":{background:b,[`${n}-trigger`]:{color:p,background:f},[`${n}-zero-width-trigger`]:{color:p,background:f,border:`1px solid ${v}`,borderInlineStart:0}}}}},Gi=ft(["Layout","Sider"],e=>[Xi(e)],co,{deprecatedTokens:uo});var qi=function(e,n){var t={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&n.indexOf(o)<0&&(t[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)n.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(t[o[r]]=e[o[r]]);return t};const Rn={xs:"479.98px",sm:"575.98px",md:"767.98px",lg:"991.98px",xl:"1199.98px",xxl:"1599.98px"},Ui=e=>!Number.isNaN(Number.parseFloat(e))&&isFinite(e),yt=i.createContext({}),Yi=(()=>{let e=0;return function(){let n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";return e+=1,`${n}${e}`}})(),go=i.forwardRef((e,n)=>{const{prefixCls:t,className:o,trigger:r,children:l,defaultCollapsed:a=!1,theme:s="dark",style:c={},collapsible:u=!1,reverseArrow:g=!1,width:d=200,collapsedWidth:m=80,zeroWidthTriggerStyle:b,breakpoint:p,onCollapse:f,onBreakpoint:v}=e,I=qi(e,["prefixCls","className","trigger","children","defaultCollapsed","theme","style","collapsible","reverseArrow","width","collapsedWidth","zeroWidthTriggerStyle","breakpoint","onCollapse","onBreakpoint"]),{siderHook:M}=i.useContext(so),[y,x]=i.useState("collapsed"in e?e.collapsed:a),[h,C]=i.useState(!1);i.useEffect(()=>{"collapsed"in e&&x(e.collapsed)},[e.collapsed]);const R=(B,L)=>{"collapsed"in e||x(B),f==null||f(B,L)},{getPrefixCls:S,direction:w}=i.useContext(Ee),O=S("layout-sider",t),[z,j,T]=Gi(O),N=i.useRef(null);N.current=B=>{C(B.matches),v==null||v(B.matches),y!==B.matches&&R(B.matches,"responsive")},i.useEffect(()=>{function B(oe){var re;return(re=N.current)===null||re===void 0?void 0:re.call(N,oe)}let L;return typeof(window==null?void 0:window.matchMedia)<"u"&&p&&p in Rn&&(L=window.matchMedia(`screen and (max-width: ${Rn[p]})`),Mr(L,B),B(L)),()=>{Nr(L,B)}},[p]),i.useEffect(()=>{const B=Yi("ant-sider-");return M.addSider(B),()=>M.removeSider(B)},[]);const P=()=>{R(!y,"clickTrigger")},F=$e(I,["collapsed"]),E=y?m:d,A=Ui(E)?`${E}px`:String(E),W=parseFloat(String(m||0))===0?i.createElement("span",{onClick:P,className:_(`${O}-zero-width-trigger`,`${O}-zero-width-trigger-${g?"right":"left"}`),style:b},r||i.createElement(Wi,null)):null,X=w==="rtl"==!g,K={expanded:X?i.createElement(kt,null):i.createElement(At,null),collapsed:X?i.createElement(At,null):i.createElement(kt,null)}[y?"collapsed":"expanded"],H=r!==null?W||i.createElement("div",{className:`${O}-trigger`,onClick:P,style:{width:A}},r||K):null,D=Object.assign(Object.assign({},c),{flex:`0 0 ${A}`,maxWidth:A,minWidth:A,width:A}),te=_(O,`${O}-${s}`,{[`${O}-collapsed`]:!!y,[`${O}-has-trigger`]:u&&r!==null&&!W,[`${O}-below`]:!!h,[`${O}-zero-width`]:parseFloat(A)===0},o,j,T),Y=i.useMemo(()=>({siderCollapsed:y}),[y]);return z(i.createElement(yt.Provider,{value:Y},i.createElement("aside",Object.assign({className:te},F,{style:D,ref:n}),i.createElement("div",{className:`${O}-children`},l),u||h&&W?H:null)))});var Qi={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"ellipsis",theme:"outlined"},Zi=function(n,t){return i.createElement(De,ee({},n,{ref:t,icon:Qi}))},po=i.forwardRef(Zi);const gt=i.createContext({prefixCls:"",firstLevel:!0,inlineCollapsed:!1});var Ji=function(e,n){var t={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&n.indexOf(o)<0&&(t[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)n.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(t[o[r]]=e[o[r]]);return t};const fo=e=>{const{prefixCls:n,className:t,dashed:o}=e,r=Ji(e,["prefixCls","className","dashed"]),{getPrefixCls:l}=i.useContext(Ee),a=l("menu",n),s=_({[`${a}-item-divider-dashed`]:!!o},t);return i.createElement(Qt,Object.assign({className:s},r))},vo=e=>{var n;const{className:t,children:o,icon:r,title:l,danger:a,extra:s}=e,{prefixCls:c,firstLevel:u,direction:g,disableMenuItemTitleTooltip:d,inlineCollapsed:m}=i.useContext(gt),b=y=>{const x=o==null?void 0:o[0],h=i.createElement("span",{className:_(`${c}-title-content`,{[`${c}-title-content-with-extra`]:!!s||s===0})},o);return(!r||i.isValidElement(o)&&o.type==="span")&&o&&y&&u&&typeof x=="string"?i.createElement("div",{className:`${c}-inline-collapsed-noicon`},x.charAt(0)):h},{siderCollapsed:p}=i.useContext(yt);let f=l;typeof l>"u"?f=u?o:"":l===!1&&(f="");const v={title:f};!p&&!m&&(v.title=null,v.open=!1);const I=bt(o).length;let M=i.createElement(Ct,Object.assign({},$e(e,["title","icon","danger"]),{className:_({[`${c}-item-danger`]:a,[`${c}-item-only-child`]:(r?I+1:I)===1},t),title:typeof l=="string"?l:void 0}),vt(r,{className:_(i.isValidElement(r)?(n=r.props)===null||n===void 0?void 0:n.className:"",`${c}-item-icon`)}),b(m));return d||(M=i.createElement(Rr,Object.assign({},v,{placement:g==="rtl"?"left":"right",classNames:{root:`${c}-inline-collapsed-tooltip`}}),M)),M};var ea=function(e,n){var t={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&n.indexOf(o)<0&&(t[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)n.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(t[o[r]]=e[o[r]]);return t};const pt=i.createContext(null),ta=i.forwardRef((e,n)=>{const{children:t}=e,o=ea(e,["children"]),r=i.useContext(pt),l=i.useMemo(()=>Object.assign(Object.assign({},r),o),[r,o.prefixCls,o.mode,o.selectable,o.rootClassName]),a=ar(t),s=_n(n,a?Wt(t):null);return i.createElement(pt.Provider,{value:l},i.createElement(Pr,{space:!0},a?i.cloneElement(t,{ref:s}):t))}),na=e=>{const{componentCls:n,motionDurationSlow:t,horizontalLineHeight:o,colorSplit:r,lineWidth:l,lineType:a,itemPaddingInline:s}=e;return{[`${n}-horizontal`]:{lineHeight:o,border:0,borderBottom:`${k(l)} ${a} ${r}`,boxShadow:"none","&::after":{display:"block",clear:"both",height:0,content:'"\\20"'},[`${n}-item, ${n}-submenu`]:{position:"relative",display:"inline-block",verticalAlign:"bottom",paddingInline:s},[`> ${n}-item:hover,
        > ${n}-item-active,
        > ${n}-submenu ${n}-submenu-title:hover`]:{backgroundColor:"transparent"},[`${n}-item, ${n}-submenu-title`]:{transition:[`border-color ${t}`,`background ${t}`].join(",")},[`${n}-submenu-arrow`]:{display:"none"}}}},oa=e=>{let{componentCls:n,menuArrowOffset:t,calc:o}=e;return{[`${n}-rtl`]:{direction:"rtl"},[`${n}-submenu-rtl`]:{transformOrigin:"100% 0"},[`${n}-rtl${n}-vertical,
    ${n}-submenu-rtl ${n}-vertical`]:{[`${n}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateY(${k(o(t).mul(-1).equal())})`},"&::after":{transform:`rotate(45deg) translateY(${k(t)})`}}}}},Pn=e=>Object.assign({},lr(e)),En=(e,n)=>{const{componentCls:t,itemColor:o,itemSelectedColor:r,subMenuItemSelectedColor:l,groupTitleColor:a,itemBg:s,subMenuItemBg:c,itemSelectedBg:u,activeBarHeight:g,activeBarWidth:d,activeBarBorderWidth:m,motionDurationSlow:b,motionEaseInOut:p,motionEaseOut:f,itemPaddingInline:v,motionDurationMid:I,itemHoverColor:M,lineType:y,colorSplit:x,itemDisabledColor:h,dangerItemColor:C,dangerItemHoverColor:R,dangerItemSelectedColor:S,dangerItemActiveBg:w,dangerItemSelectedBg:O,popupBg:z,itemHoverBg:j,itemActiveBg:T,menuSubMenuBg:N,horizontalItemSelectedColor:P,horizontalItemSelectedBg:F,horizontalItemBorderRadius:E,horizontalItemHoverBg:A}=e;return{[`${t}-${n}, ${t}-${n} > ${t}`]:{color:o,background:s,[`&${t}-root:focus-visible`]:Object.assign({},Pn(e)),[`${t}-item`]:{"&-group-title, &-extra":{color:a}},[`${t}-submenu-selected > ${t}-submenu-title`]:{color:l},[`${t}-item, ${t}-submenu-title`]:{color:o,[`&:not(${t}-item-disabled):focus-visible`]:Object.assign({},Pn(e))},[`${t}-item-disabled, ${t}-submenu-disabled`]:{color:`${h} !important`},[`${t}-item:not(${t}-item-selected):not(${t}-submenu-selected)`]:{[`&:hover, > ${t}-submenu-title:hover`]:{color:M}},[`&:not(${t}-horizontal)`]:{[`${t}-item:not(${t}-item-selected)`]:{"&:hover":{backgroundColor:j},"&:active":{backgroundColor:T}},[`${t}-submenu-title`]:{"&:hover":{backgroundColor:j},"&:active":{backgroundColor:T}}},[`${t}-item-danger`]:{color:C,[`&${t}-item:hover`]:{[`&:not(${t}-item-selected):not(${t}-submenu-selected)`]:{color:R}},[`&${t}-item:active`]:{background:w}},[`${t}-item a`]:{"&, &:hover":{color:"inherit"}},[`${t}-item-selected`]:{color:r,[`&${t}-item-danger`]:{color:S},"a, a:hover":{color:"inherit"}},[`& ${t}-item-selected`]:{backgroundColor:u,[`&${t}-item-danger`]:{backgroundColor:O}},[`&${t}-submenu > ${t}`]:{backgroundColor:N},[`&${t}-popup > ${t}`]:{backgroundColor:z},[`&${t}-submenu-popup > ${t}`]:{backgroundColor:z},[`&${t}-horizontal`]:Object.assign(Object.assign({},n==="dark"?{borderBottom:0}:{}),{[`> ${t}-item, > ${t}-submenu`]:{top:m,marginTop:e.calc(m).mul(-1).equal(),marginBottom:0,borderRadius:E,"&::after":{position:"absolute",insetInline:v,bottom:0,borderBottom:`${k(g)} solid transparent`,transition:`border-color ${b} ${p}`,content:'""'},"&:hover, &-active, &-open":{background:A,"&::after":{borderBottomWidth:g,borderBottomColor:P}},"&-selected":{color:P,backgroundColor:F,"&:hover":{backgroundColor:F},"&::after":{borderBottomWidth:g,borderBottomColor:P}}}}),[`&${t}-root`]:{[`&${t}-inline, &${t}-vertical`]:{borderInlineEnd:`${k(m)} ${y} ${x}`}},[`&${t}-inline`]:{[`${t}-sub${t}-inline`]:{background:c},[`${t}-item`]:{position:"relative","&::after":{position:"absolute",insetBlock:0,insetInlineEnd:0,borderInlineEnd:`${k(d)} solid ${r}`,transform:"scaleY(0.0001)",opacity:0,transition:[`transform ${I} ${f}`,`opacity ${I} ${f}`].join(","),content:'""'},[`&${t}-item-danger`]:{"&::after":{borderInlineEndColor:S}}},[`${t}-selected, ${t}-item-selected`]:{"&::after":{transform:"scaleY(1)",opacity:1,transition:[`transform ${I} ${p}`,`opacity ${I} ${p}`].join(",")}}}}}},Bn=e=>{const{componentCls:n,itemHeight:t,itemMarginInline:o,padding:r,menuArrowSize:l,marginXS:a,itemMarginBlock:s,itemWidth:c,itemPaddingInline:u}=e,g=e.calc(l).add(r).add(a).equal();return{[`${n}-item`]:{position:"relative",overflow:"hidden"},[`${n}-item, ${n}-submenu-title`]:{height:t,lineHeight:k(t),paddingInline:u,overflow:"hidden",textOverflow:"ellipsis",marginInline:o,marginBlock:s,width:c},[`> ${n}-item,
            > ${n}-submenu > ${n}-submenu-title`]:{height:t,lineHeight:k(t)},[`${n}-item-group-list ${n}-submenu-title,
            ${n}-submenu-title`]:{paddingInlineEnd:g}}},ra=e=>{const{componentCls:n,iconCls:t,itemHeight:o,colorTextLightSolid:r,dropdownWidth:l,controlHeightLG:a,motionEaseOut:s,paddingXL:c,itemMarginInline:u,fontSizeLG:g,motionDurationFast:d,motionDurationSlow:m,paddingXS:b,boxShadowSecondary:p,collapsedWidth:f,collapsedIconSize:v}=e,I={height:o,lineHeight:k(o),listStylePosition:"inside",listStyleType:"disc"};return[{[n]:{"&-inline, &-vertical":Object.assign({[`&${n}-root`]:{boxShadow:"none"}},Bn(e))},[`${n}-submenu-popup`]:{[`${n}-vertical`]:Object.assign(Object.assign({},Bn(e)),{boxShadow:p})}},{[`${n}-submenu-popup ${n}-vertical${n}-sub`]:{minWidth:l,maxHeight:`calc(100vh - ${k(e.calc(a).mul(2.5).equal())})`,padding:"0",overflow:"hidden",borderInlineEnd:0,"&:not([class*='-active'])":{overflowX:"hidden",overflowY:"auto"}}},{[`${n}-inline`]:{width:"100%",[`&${n}-root`]:{[`${n}-item, ${n}-submenu-title`]:{display:"flex",alignItems:"center",transition:[`border-color ${m}`,`background ${m}`,`padding ${d} ${s}`].join(","),[`> ${n}-title-content`]:{flex:"auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis"},"> *":{flex:"none"}}},[`${n}-sub${n}-inline`]:{padding:0,border:0,borderRadius:0,boxShadow:"none",[`& > ${n}-submenu > ${n}-submenu-title`]:I,[`& ${n}-item-group-title`]:{paddingInlineStart:c}},[`${n}-item`]:I}},{[`${n}-inline-collapsed`]:{width:f,[`&${n}-root`]:{[`${n}-item, ${n}-submenu ${n}-submenu-title`]:{[`> ${n}-inline-collapsed-noicon`]:{fontSize:g,textAlign:"center"}}},[`> ${n}-item,
          > ${n}-item-group > ${n}-item-group-list > ${n}-item,
          > ${n}-item-group > ${n}-item-group-list > ${n}-submenu > ${n}-submenu-title,
          > ${n}-submenu > ${n}-submenu-title`]:{insetInlineStart:0,paddingInline:`calc(50% - ${k(e.calc(v).div(2).equal())} - ${k(u)})`,textOverflow:"clip",[`
            ${n}-submenu-arrow,
            ${n}-submenu-expand-icon
          `]:{opacity:0},[`${n}-item-icon, ${t}`]:{margin:0,fontSize:v,lineHeight:k(o),"+ span":{display:"inline-block",opacity:0}}},[`${n}-item-icon, ${t}`]:{display:"inline-block"},"&-tooltip":{pointerEvents:"none",[`${n}-item-icon, ${t}`]:{display:"none"},"a, a:hover":{color:r}},[`${n}-item-group-title`]:Object.assign(Object.assign({},sr),{paddingInline:b})}}]},jn=e=>{const{componentCls:n,motionDurationSlow:t,motionDurationMid:o,motionEaseInOut:r,motionEaseOut:l,iconCls:a,iconSize:s,iconMarginInlineEnd:c}=e;return{[`${n}-item, ${n}-submenu-title`]:{position:"relative",display:"block",margin:0,whiteSpace:"nowrap",cursor:"pointer",transition:[`border-color ${t}`,`background ${t}`,`padding calc(${t} + 0.1s) ${r}`].join(","),[`${n}-item-icon, ${a}`]:{minWidth:s,fontSize:s,transition:[`font-size ${o} ${l}`,`margin ${t} ${r}`,`color ${t}`].join(","),"+ span":{marginInlineStart:c,opacity:1,transition:[`opacity ${t} ${r}`,`margin ${t}`,`color ${t}`].join(",")}},[`${n}-item-icon`]:Object.assign({},cr()),[`&${n}-item-only-child`]:{[`> ${a}, > ${n}-item-icon`]:{marginInlineEnd:0}}},[`${n}-item-disabled, ${n}-submenu-disabled`]:{background:"none !important",cursor:"not-allowed","&::after":{borderColor:"transparent !important"},a:{color:"inherit !important",cursor:"not-allowed",pointerEvents:"none"},[`> ${n}-submenu-title`]:{color:"inherit !important",cursor:"not-allowed"}}}},Tn=e=>{const{componentCls:n,motionDurationSlow:t,motionEaseInOut:o,borderRadius:r,menuArrowSize:l,menuArrowOffset:a}=e;return{[`${n}-submenu`]:{"&-expand-icon, &-arrow":{position:"absolute",top:"50%",insetInlineEnd:e.margin,width:l,color:"currentcolor",transform:"translateY(-50%)",transition:`transform ${t} ${o}, opacity ${t}`},"&-arrow":{"&::before, &::after":{position:"absolute",width:e.calc(l).mul(.6).equal(),height:e.calc(l).mul(.15).equal(),backgroundColor:"currentcolor",borderRadius:r,transition:[`background ${t} ${o}`,`transform ${t} ${o}`,`top ${t} ${o}`,`color ${t} ${o}`].join(","),content:'""'},"&::before":{transform:`rotate(45deg) translateY(${k(e.calc(a).mul(-1).equal())})`},"&::after":{transform:`rotate(-45deg) translateY(${k(a)})`}}}}},ia=e=>{const{antCls:n,componentCls:t,fontSize:o,motionDurationSlow:r,motionDurationMid:l,motionEaseInOut:a,paddingXS:s,padding:c,colorSplit:u,lineWidth:g,zIndexPopup:d,borderRadiusLG:m,subMenuItemBorderRadius:b,menuArrowSize:p,menuArrowOffset:f,lineType:v,groupTitleLineHeight:I,groupTitleFontSize:M}=e;return[{"":{[t]:Object.assign(Object.assign({},bn()),{"&-hidden":{display:"none"}})},[`${t}-submenu-hidden`]:{display:"none"}},{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Kn(e)),bn()),{marginBottom:0,paddingInlineStart:0,fontSize:o,lineHeight:0,listStyle:"none",outline:"none",transition:`width ${r} cubic-bezier(0.2, 0, 0, 1) 0s`,"ul, ol":{margin:0,padding:0,listStyle:"none"},"&-overflow":{display:"flex",[`${t}-item`]:{flex:"none"}},[`${t}-item, ${t}-submenu, ${t}-submenu-title`]:{borderRadius:e.itemBorderRadius},[`${t}-item-group-title`]:{padding:`${k(s)} ${k(c)}`,fontSize:M,lineHeight:I,transition:`all ${r}`},[`&-horizontal ${t}-submenu`]:{transition:[`border-color ${r} ${a}`,`background ${r} ${a}`].join(",")},[`${t}-submenu, ${t}-submenu-inline`]:{transition:[`border-color ${r} ${a}`,`background ${r} ${a}`,`padding ${l} ${a}`].join(",")},[`${t}-submenu ${t}-sub`]:{cursor:"initial",transition:[`background ${r} ${a}`,`padding ${r} ${a}`].join(",")},[`${t}-title-content`]:{transition:`color ${r}`,"&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},[`> ${n}-typography-ellipsis-single-line`]:{display:"inline",verticalAlign:"unset"},[`${t}-item-extra`]:{marginInlineStart:"auto",paddingInlineStart:e.padding}},[`${t}-item a`]:{"&::before":{position:"absolute",inset:0,backgroundColor:"transparent",content:'""'}},[`${t}-item-divider`]:{overflow:"hidden",lineHeight:0,borderColor:u,borderStyle:v,borderWidth:0,borderTopWidth:g,marginBlock:g,padding:0,"&-dashed":{borderStyle:"dashed"}}}),jn(e)),{[`${t}-item-group`]:{[`${t}-item-group-list`]:{margin:0,padding:0,[`${t}-item, ${t}-submenu-title`]:{paddingInline:`${k(e.calc(o).mul(2).equal())} ${k(c)}`}}},"&-submenu":{"&-popup":{position:"absolute",zIndex:d,borderRadius:m,boxShadow:"none",transformOrigin:"0 0",[`&${t}-submenu`]:{background:"transparent"},"&::before":{position:"absolute",inset:0,zIndex:-1,width:"100%",height:"100%",opacity:0,content:'""'},[`> ${t}`]:Object.assign(Object.assign(Object.assign({borderRadius:m},jn(e)),Tn(e)),{[`${t}-item, ${t}-submenu > ${t}-submenu-title`]:{borderRadius:b},[`${t}-submenu-title::after`]:{transition:`transform ${r} ${a}`}})},"\n          &-placement-leftTop,\n          &-placement-bottomRight,\n          ":{transformOrigin:"100% 0"},"\n          &-placement-leftBottom,\n          &-placement-topRight,\n          ":{transformOrigin:"100% 100%"},"\n          &-placement-rightBottom,\n          &-placement-topLeft,\n          ":{transformOrigin:"0 100%"},"\n          &-placement-bottomLeft,\n          &-placement-rightTop,\n          ":{transformOrigin:"0 0"},"\n          &-placement-leftTop,\n          &-placement-leftBottom\n          ":{paddingInlineEnd:e.paddingXS},"\n          &-placement-rightTop,\n          &-placement-rightBottom\n          ":{paddingInlineStart:e.paddingXS},"\n          &-placement-topRight,\n          &-placement-topLeft\n          ":{paddingBottom:e.paddingXS},"\n          &-placement-bottomRight,\n          &-placement-bottomLeft\n          ":{paddingTop:e.paddingXS}}}),Tn(e)),{[`&-inline-collapsed ${t}-submenu-arrow,
        &-inline ${t}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateX(${k(f)})`},"&::after":{transform:`rotate(45deg) translateX(${k(e.calc(f).mul(-1).equal())})`}},[`${t}-submenu-open${t}-submenu-inline > ${t}-submenu-title > ${t}-submenu-arrow`]:{transform:`translateY(${k(e.calc(p).mul(.2).mul(-1).equal())})`,"&::after":{transform:`rotate(-45deg) translateX(${k(e.calc(f).mul(-1).equal())})`},"&::before":{transform:`rotate(45deg) translateX(${k(f)})`}}})},{[`${n}-layout-header`]:{[t]:{lineHeight:"inherit"}}}]},aa=e=>{var n,t,o;const{colorPrimary:r,colorError:l,colorTextDisabled:a,colorErrorBg:s,colorText:c,colorTextDescription:u,colorBgContainer:g,colorFillAlter:d,colorFillContent:m,lineWidth:b,lineWidthBold:p,controlItemBgActive:f,colorBgTextHover:v,controlHeightLG:I,lineHeight:M,colorBgElevated:y,marginXXS:x,padding:h,fontSize:C,controlHeightSM:R,fontSizeLG:S,colorTextLightSolid:w,colorErrorHover:O}=e,z=(n=e.activeBarWidth)!==null&&n!==void 0?n:0,j=(t=e.activeBarBorderWidth)!==null&&t!==void 0?t:b,T=(o=e.itemMarginInline)!==null&&o!==void 0?o:e.marginXXS,N=new ut(w).setA(.65).toRgbString();return{dropdownWidth:160,zIndexPopup:e.zIndexPopupBase+50,radiusItem:e.borderRadiusLG,itemBorderRadius:e.borderRadiusLG,radiusSubMenuItem:e.borderRadiusSM,subMenuItemBorderRadius:e.borderRadiusSM,colorItemText:c,itemColor:c,colorItemTextHover:c,itemHoverColor:c,colorItemTextHoverHorizontal:r,horizontalItemHoverColor:r,colorGroupTitle:u,groupTitleColor:u,colorItemTextSelected:r,itemSelectedColor:r,subMenuItemSelectedColor:r,colorItemTextSelectedHorizontal:r,horizontalItemSelectedColor:r,colorItemBg:g,itemBg:g,colorItemBgHover:v,itemHoverBg:v,colorItemBgActive:m,itemActiveBg:f,colorSubItemBg:d,subMenuItemBg:d,colorItemBgSelected:f,itemSelectedBg:f,colorItemBgSelectedHorizontal:"transparent",horizontalItemSelectedBg:"transparent",colorActiveBarWidth:0,activeBarWidth:z,colorActiveBarHeight:p,activeBarHeight:p,colorActiveBarBorderSize:b,activeBarBorderWidth:j,colorItemTextDisabled:a,itemDisabledColor:a,colorDangerItemText:l,dangerItemColor:l,colorDangerItemTextHover:l,dangerItemHoverColor:l,colorDangerItemTextSelected:l,dangerItemSelectedColor:l,colorDangerItemBgActive:s,dangerItemActiveBg:s,colorDangerItemBgSelected:s,dangerItemSelectedBg:s,itemMarginInline:T,horizontalItemBorderRadius:0,horizontalItemHoverBg:"transparent",itemHeight:I,groupTitleLineHeight:M,collapsedWidth:I*2,popupBg:y,itemMarginBlock:x,itemPaddingInline:h,horizontalLineHeight:`${I*1.15}px`,iconSize:C,iconMarginInlineEnd:R-C,collapsedIconSize:S,groupTitleFontSize:C,darkItemDisabledColor:new ut(w).setA(.25).toRgbString(),darkItemColor:N,darkDangerItemColor:l,darkItemBg:"#001529",darkPopupBg:"#001529",darkSubMenuItemBg:"#000c17",darkItemSelectedColor:w,darkItemSelectedBg:r,darkDangerItemSelectedBg:l,darkItemHoverBg:"transparent",darkGroupTitleColor:N,darkItemHoverColor:w,darkDangerItemHoverColor:O,darkDangerItemSelectedColor:w,darkDangerItemActiveBg:l,itemWidth:z?`calc(100% + ${j}px)`:`calc(100% - ${T*2}px)`}},la=function(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e,t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;return ft("Menu",r=>{const{colorBgElevated:l,controlHeightLG:a,fontSize:s,darkItemColor:c,darkDangerItemColor:u,darkItemBg:g,darkSubMenuItemBg:d,darkItemSelectedColor:m,darkItemSelectedBg:b,darkDangerItemSelectedBg:p,darkItemHoverBg:f,darkGroupTitleColor:v,darkItemHoverColor:I,darkItemDisabledColor:M,darkDangerItemHoverColor:y,darkDangerItemSelectedColor:x,darkDangerItemActiveBg:h,popupBg:C,darkPopupBg:R}=r,S=r.calc(s).div(7).mul(5).equal(),w=zt(r,{menuArrowSize:S,menuHorizontalHeight:r.calc(a).mul(1.15).equal(),menuArrowOffset:r.calc(S).mul(.25).equal(),menuSubMenuBg:l,calc:r.calc,popupBg:C}),O=zt(w,{itemColor:c,itemHoverColor:I,groupTitleColor:v,itemSelectedColor:m,subMenuItemSelectedColor:m,itemBg:g,popupBg:R,subMenuItemBg:d,itemActiveBg:"transparent",itemSelectedBg:b,activeBarHeight:0,activeBarBorderWidth:0,itemHoverBg:f,itemDisabledColor:M,dangerItemColor:u,dangerItemHoverColor:y,dangerItemSelectedColor:x,dangerItemActiveBg:h,dangerItemSelectedBg:p,menuSubMenuBg:d,horizontalItemSelectedColor:m,horizontalItemSelectedBg:b});return[ia(w),na(w),ra(w),En(w,"light"),En(O,"dark"),oa(w),Er(w),dt(w,"slide-up"),dt(w,"slide-down"),Yn(w,"zoom-big")]},aa,{deprecatedTokens:[["colorGroupTitle","groupTitleColor"],["radiusItem","itemBorderRadius"],["radiusSubMenuItem","subMenuItemBorderRadius"],["colorItemText","itemColor"],["colorItemTextHover","itemHoverColor"],["colorItemTextHoverHorizontal","horizontalItemHoverColor"],["colorItemTextSelected","itemSelectedColor"],["colorItemTextSelectedHorizontal","horizontalItemSelectedColor"],["colorItemTextDisabled","itemDisabledColor"],["colorDangerItemText","dangerItemColor"],["colorDangerItemTextHover","dangerItemHoverColor"],["colorDangerItemTextSelected","dangerItemSelectedColor"],["colorDangerItemBgActive","dangerItemActiveBg"],["colorDangerItemBgSelected","dangerItemSelectedBg"],["colorItemBg","itemBg"],["colorItemBgHover","itemHoverBg"],["colorSubItemBg","subMenuItemBg"],["colorItemBgActive","itemActiveBg"],["colorItemBgSelectedHorizontal","horizontalItemSelectedBg"],["colorActiveBarWidth","activeBarWidth"],["colorActiveBarHeight","activeBarHeight"],["colorActiveBarBorderSize","activeBarBorderWidth"],["colorItemBgSelected","itemSelectedBg"]],injectStyle:t,unitless:{groupTitleLineHeight:!0}})(e,n)},bo=e=>{var n;const{popupClassName:t,icon:o,title:r,theme:l}=e,a=i.useContext(gt),{prefixCls:s,inlineCollapsed:c,theme:u}=a,g=Le();let d;if(!o)d=c&&!g.length&&r&&typeof r=="string"?i.createElement("div",{className:`${s}-inline-collapsed-noicon`},r.charAt(0)):i.createElement("span",{className:`${s}-title-content`},r);else{const p=i.isValidElement(r)&&r.type==="span";d=i.createElement(i.Fragment,null,vt(o,{className:_(i.isValidElement(o)?(n=o.props)===null||n===void 0?void 0:n.className:"",`${s}-item-icon`)}),p?r:i.createElement("span",{className:`${s}-title-content`},r))}const m=i.useMemo(()=>Object.assign(Object.assign({},a),{firstLevel:!1}),[a]),[b]=Vn("Menu");return i.createElement(gt.Provider,{value:m},i.createElement($t,Object.assign({},$e(e,["icon"]),{title:d,popupClassName:_(s,t,`${s}-${l||u}`),popupStyle:Object.assign({zIndex:b},e.popupStyle)})))};var sa=function(e,n){var t={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&n.indexOf(o)<0&&(t[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)n.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(t[o[r]]=e[o[r]]);return t};function Tt(e){return e===null||e===!1}const ca={item:vo,submenu:bo,divider:fo},ua=i.forwardRef((e,n)=>{var t;const o=i.useContext(pt),r=o||{},{getPrefixCls:l,getPopupContainer:a,direction:s,menu:c}=i.useContext(Ee),u=l(),{prefixCls:g,className:d,style:m,theme:b="light",expandIcon:p,_internalDisableMenuItemTitleTooltip:f,inlineCollapsed:v,siderCollapsed:I,rootClassName:M,mode:y,selectable:x,onClick:h,overflowedIndicatorPopupClassName:C}=e,R=sa(e,["prefixCls","className","style","theme","expandIcon","_internalDisableMenuItemTitleTooltip","inlineCollapsed","siderCollapsed","rootClassName","mode","selectable","onClick","overflowedIndicatorPopupClassName"]),S=$e(R,["collapsedWidth"]);(t=r.validator)===null||t===void 0||t.call(r,{mode:y});const w=Wn(function(){var G;h==null||h.apply(void 0,arguments),(G=r.onClick)===null||G===void 0||G.call(r)}),O=r.mode||y,z=x??r.selectable,j=v??I,T={horizontal:{motionName:`${u}-slide-up`},inline:Br(u),other:{motionName:`${u}-zoom-big`}},N=l("menu",g||r.prefixCls),P=Fn(N),[F,E,A]=la(N,P,!o),W=_(`${N}-${b}`,c==null?void 0:c.className,d),X=i.useMemo(()=>{var G,K;if(typeof p=="function"||Tt(p))return p||null;if(typeof r.expandIcon=="function"||Tt(r.expandIcon))return r.expandIcon||null;if(typeof(c==null?void 0:c.expandIcon)=="function"||Tt(c==null?void 0:c.expandIcon))return(c==null?void 0:c.expandIcon)||null;const H=(G=p??(r==null?void 0:r.expandIcon))!==null&&G!==void 0?G:c==null?void 0:c.expandIcon;return vt(H,{className:_(`${N}-submenu-expand-icon`,i.isValidElement(H)?(K=H.props)===null||K===void 0?void 0:K.className:void 0)})},[p,r==null?void 0:r.expandIcon,c==null?void 0:c.expandIcon,N]),q=i.useMemo(()=>({prefixCls:N,inlineCollapsed:j||!1,direction:s,firstLevel:!0,theme:b,mode:O,disableMenuItemTitleTooltip:f}),[N,j,s,f,b]);return F(i.createElement(pt.Provider,{value:null},i.createElement(gt.Provider,{value:q},i.createElement(Ze,Object.assign({getPopupContainer:a,overflowedIndicator:i.createElement(po,null),overflowedIndicatorPopupClassName:_(N,`${N}-${b}`,C),mode:O,selectable:z,onClick:w},S,{inlineCollapsed:j,style:Object.assign(Object.assign({},c==null?void 0:c.style),m),className:W,prefixCls:N,direction:s,defaultMotions:T,expandIcon:X,ref:n,rootClassName:_(M,E,r.rootClassName,A,P),_internalComponents:ca})))))}),Je=i.forwardRef((e,n)=>{const t=i.useRef(null),o=i.useContext(yt);return i.useImperativeHandle(n,()=>({menu:t.current,focus:r=>{var l;(l=t.current)===null||l===void 0||l.focus(r)}})),i.createElement(ua,Object.assign({ref:t},e,o))});Je.Item=vo;Je.SubMenu=bo;Je.Divider=fo;Je.ItemGroup=Zt;const da=e=>{const{componentCls:n,menuCls:t,colorError:o,colorTextLightSolid:r}=e,l=`${t}-item`;return{[`${n}, ${n}-menu-submenu`]:{[`${t} ${l}`]:{[`&${l}-danger:not(${l}-disabled)`]:{color:o,"&:hover":{color:r,backgroundColor:o}}}}}},ma=e=>{const{componentCls:n,menuCls:t,zIndexPopup:o,dropdownArrowDistance:r,sizePopupArrow:l,antCls:a,iconCls:s,motionDurationMid:c,paddingBlock:u,fontSize:g,dropdownEdgeChildPadding:d,colorTextDisabled:m,fontSizeIcon:b,controlPaddingHorizontal:p,colorBgElevated:f}=e;return[{[n]:{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:o,display:"block","&::before":{position:"absolute",insetBlock:e.calc(l).div(2).sub(r).equal(),zIndex:-9999,opacity:1e-4,content:'""'},"&-menu-vertical":{maxHeight:"100vh",overflowY:"auto"},[`&-trigger${a}-btn`]:{[`& > ${s}-down, & > ${a}-btn-icon > ${s}-down`]:{fontSize:b}},[`${n}-wrap`]:{position:"relative",[`${a}-btn > ${s}-down`]:{fontSize:b},[`${s}-down::before`]:{transition:`transform ${c}`}},[`${n}-wrap-open`]:{[`${s}-down::before`]:{transform:"rotate(180deg)"}},"\n        &-hidden,\n        &-menu-hidden,\n        &-menu-submenu-hidden\n      ":{display:"none"},[`&${a}-slide-down-enter${a}-slide-down-enter-active${n}-placement-bottomLeft,
          &${a}-slide-down-appear${a}-slide-down-appear-active${n}-placement-bottomLeft,
          &${a}-slide-down-enter${a}-slide-down-enter-active${n}-placement-bottom,
          &${a}-slide-down-appear${a}-slide-down-appear-active${n}-placement-bottom,
          &${a}-slide-down-enter${a}-slide-down-enter-active${n}-placement-bottomRight,
          &${a}-slide-down-appear${a}-slide-down-appear-active${n}-placement-bottomRight`]:{animationName:kr},[`&${a}-slide-up-enter${a}-slide-up-enter-active${n}-placement-topLeft,
          &${a}-slide-up-appear${a}-slide-up-appear-active${n}-placement-topLeft,
          &${a}-slide-up-enter${a}-slide-up-enter-active${n}-placement-top,
          &${a}-slide-up-appear${a}-slide-up-appear-active${n}-placement-top,
          &${a}-slide-up-enter${a}-slide-up-enter-active${n}-placement-topRight,
          &${a}-slide-up-appear${a}-slide-up-appear-active${n}-placement-topRight`]:{animationName:zr},[`&${a}-slide-down-leave${a}-slide-down-leave-active${n}-placement-bottomLeft,
          &${a}-slide-down-leave${a}-slide-down-leave-active${n}-placement-bottom,
          &${a}-slide-down-leave${a}-slide-down-leave-active${n}-placement-bottomRight`]:{animationName:Tr},[`&${a}-slide-up-leave${a}-slide-up-leave-active${n}-placement-topLeft,
          &${a}-slide-up-leave${a}-slide-up-leave-active${n}-placement-top,
          &${a}-slide-up-leave${a}-slide-up-leave-active${n}-placement-topRight`]:{animationName:jr}}},Ar(e,f,{arrowPlacement:{top:!0,bottom:!0}}),{[`${n} ${t}`]:{position:"relative",margin:0},[`${t}-submenu-popup`]:{position:"absolute",zIndex:o,background:"transparent",boxShadow:"none",transformOrigin:"0 0","ul, li":{listStyle:"none",margin:0}},[`${n}, ${n}-menu-submenu`]:Object.assign(Object.assign({},Kn(e)),{[t]:Object.assign(Object.assign({padding:d,listStyleType:"none",backgroundColor:f,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary},hn(e)),{"&:empty":{padding:0,boxShadow:"none"},[`${t}-item-group-title`]:{padding:`${k(u)} ${k(p)}`,color:e.colorTextDescription,transition:`all ${c}`},[`${t}-item`]:{position:"relative",display:"flex",alignItems:"center"},[`${t}-item-icon`]:{minWidth:g,marginInlineEnd:e.marginXS,fontSize:e.fontSizeSM},[`${t}-title-content`]:{flex:"auto","&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},"> a":{color:"inherit",transition:`all ${c}`,"&:hover":{color:"inherit"},"&::after":{position:"absolute",inset:0,content:'""'}},[`${t}-item-extra`]:{paddingInlineStart:e.padding,marginInlineStart:"auto",fontSize:e.fontSizeSM,color:e.colorTextDescription}},[`${t}-item, ${t}-submenu-title`]:Object.assign(Object.assign({display:"flex",margin:0,padding:`${k(u)} ${k(p)}`,color:e.colorText,fontWeight:"normal",fontSize:g,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${c}`,borderRadius:e.borderRadiusSM,"&:hover, &-active":{backgroundColor:e.controlItemBgHover}},hn(e)),{"&-selected":{color:e.colorPrimary,backgroundColor:e.controlItemBgActive,"&:hover, &-active":{backgroundColor:e.controlItemBgActiveHover}},"&-disabled":{color:m,cursor:"not-allowed","&:hover":{color:m,backgroundColor:f,cursor:"not-allowed"},a:{pointerEvents:"none"}},"&-divider":{height:1,margin:`${k(e.marginXXS)} 0`,overflow:"hidden",lineHeight:0,backgroundColor:e.colorSplit},[`${n}-menu-submenu-expand-icon`]:{position:"absolute",insetInlineEnd:e.paddingXS,[`${n}-menu-submenu-arrow-icon`]:{marginInlineEnd:"0 !important",color:e.colorIcon,fontSize:b,fontStyle:"normal"}}}),[`${t}-item-group-list`]:{margin:`0 ${k(e.marginXS)}`,padding:0,listStyle:"none"},[`${t}-submenu-title`]:{paddingInlineEnd:e.calc(p).add(e.fontSizeSM).equal()},[`${t}-submenu-vertical`]:{position:"relative"},[`${t}-submenu${t}-submenu-disabled ${n}-menu-submenu-title`]:{[`&, ${n}-menu-submenu-arrow-icon`]:{color:m,backgroundColor:f,cursor:"not-allowed"}},[`${t}-submenu-selected ${n}-menu-submenu-title`]:{color:e.colorPrimary}})})},[dt(e,"slide-up"),dt(e,"slide-down"),$n(e,"move-up"),$n(e,"move-down"),Yn(e,"zoom-big")]]},ga=e=>Object.assign(Object.assign({zIndexPopup:e.zIndexPopupBase+50,paddingBlock:(e.controlHeight-e.fontSize*e.lineHeight)/2},Hr({contentRadius:e.borderRadiusLG,limitVerticalRadius:!0})),Dr(e)),pa=ft("Dropdown",e=>{const{marginXXS:n,sizePopupArrow:t,paddingXXS:o,componentCls:r}=e,l=zt(e,{menuCls:`${r}-menu`,dropdownArrowDistance:e.calc(t).div(2).add(n).equal(),dropdownEdgeChildPadding:o});return[ma(l),da(l)]},ga,{resetStyle:!1}),xt=e=>{var n;const{menu:t,arrow:o,prefixCls:r,children:l,trigger:a,disabled:s,dropdownRender:c,popupRender:u,getPopupContainer:g,overlayClassName:d,rootClassName:m,overlayStyle:b,open:p,onOpenChange:f,visible:v,onVisibleChange:I,mouseEnterDelay:M=.15,mouseLeaveDelay:y=.1,autoAdjustOverflow:x=!0,placement:h="",overlay:C,transitionName:R,destroyOnHidden:S,destroyPopupOnHide:w}=e,{getPopupContainer:O,getPrefixCls:z,direction:j,dropdown:T}=i.useContext(Ee),N=u||c;dr();const P=i.useMemo(()=>{const Q=z();return R!==void 0?R:h.includes("top")?`${Q}-slide-down`:`${Q}-slide-up`},[z,h,R]),F=i.useMemo(()=>h?h.includes("Center")?h.slice(0,h.indexOf("Center")):h:j==="rtl"?"bottomRight":"bottomLeft",[h,j]),E=z("dropdown",r),A=Fn(E),[W,X,q]=pa(E,A),[,G]=Xn(),K=i.Children.only(ai(l)?i.createElement("span",null,l):l),H=vt(K,{className:_(`${E}-trigger`,{[`${E}-rtl`]:j==="rtl"},K.props.className),disabled:(n=K.props.disabled)!==null&&n!==void 0?n:s}),D=s?[]:a,te=!!(D!=null&&D.includes("contextMenu")),[Y,B]=Ue(!1,{value:p??v}),L=Wn(Q=>{f==null||f(Q,{source:"trigger"}),I==null||I(Q),B(Q)}),oe=_(d,m,X,q,A,T==null?void 0:T.className,{[`${E}-rtl`]:j==="rtl"}),re=Lr({arrowPointAtCenter:typeof o=="object"&&o.pointAtCenter,autoAdjustOverflow:x,offset:G.marginXXS,arrowWidth:o?G.sizePopupArrow:0,borderRadius:G.borderRadius}),Be=i.useCallback(()=>{t!=null&&t.selectable&&(t!=null&&t.multiple)||(f==null||f(!1,{source:"menu"}),B(!1))},[t==null?void 0:t.selectable,t==null?void 0:t.multiple]),je=()=>{let Q;return t!=null&&t.items?Q=i.createElement(Je,Object.assign({},t)):typeof C=="function"?Q=C():Q=C,N&&(Q=N(Q)),Q=i.Children.only(typeof Q=="string"?i.createElement("span",null,Q):Q),i.createElement(ta,{prefixCls:`${E}-menu`,rootClassName:_(q,A),expandIcon:i.createElement("span",{className:`${E}-menu-submenu-arrow`},j==="rtl"?i.createElement(At,{className:`${E}-menu-submenu-arrow-icon`}):i.createElement(kt,{className:`${E}-menu-submenu-arrow-icon`})),mode:"vertical",selectable:!1,onClick:Be,validator:Me=>{let{mode:_e}=Me}},Q)},[be,we]=Vn("Dropdown",b==null?void 0:b.zIndex);let Oe=i.createElement(ii,Object.assign({alignPoint:te},$e(e,["rootClassName"]),{mouseEnterDelay:M,mouseLeaveDelay:y,visible:Y,builtinPlacements:re,arrow:!!o,overlayClassName:oe,prefixCls:E,getPopupContainer:g||O,transitionName:P,trigger:D,overlay:je,placement:F,onVisibleChange:L,overlayStyle:Object.assign(Object.assign(Object.assign({},T==null?void 0:T.style),b),{zIndex:be}),autoDestroy:S??w}),H);return be&&(Oe=i.createElement(ur.Provider,{value:we},Oe)),W(Oe)},fa=_r(xt,"align",void 0,"dropdown",e=>e),va=e=>i.createElement(fa,Object.assign({},e),i.createElement("span",null));xt._InternalPanelDoNotUseOrYouWillBeFired=va;function zn(e){return["small","middle","large"].includes(e)}function kn(e){return e?typeof e=="number"&&!Number.isNaN(e):!1}const ho=le.createContext({latestIndex:0}),ba=ho.Provider,ha=e=>{let{className:n,index:t,children:o,split:r,style:l}=e;const{latestIndex:a}=i.useContext(ho);return o==null?null:i.createElement(i.Fragment,null,i.createElement("div",{className:n,style:l},o),t<a&&r&&i.createElement("span",{className:`${n}-split`},r))};var Ca=function(e,n){var t={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&n.indexOf(o)<0&&(t[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)n.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(t[o[r]]=e[o[r]]);return t};const $a=i.forwardRef((e,n)=>{var t;const{getPrefixCls:o,direction:r,size:l,className:a,style:s,classNames:c,styles:u}=Gn("space"),{size:g=l??"small",align:d,className:m,rootClassName:b,children:p,direction:f="horizontal",prefixCls:v,split:I,style:M,wrap:y=!1,classNames:x,styles:h}=e,C=Ca(e,["size","align","className","rootClassName","children","direction","prefixCls","split","style","wrap","classNames","styles"]),[R,S]=Array.isArray(g)?g:[g,g],w=zn(S),O=zn(R),z=kn(S),j=kn(R),T=bt(p,{keepEmpty:!0}),N=d===void 0&&f==="horizontal"?"center":d,P=o("space",v),[F,E,A]=Fr(P),W=_(P,a,E,`${P}-${f}`,{[`${P}-rtl`]:r==="rtl",[`${P}-align-${N}`]:N,[`${P}-gap-row-${S}`]:w,[`${P}-gap-col-${R}`]:O},m,b,A),X=_(`${P}-item`,(t=x==null?void 0:x.item)!==null&&t!==void 0?t:c.item);let q=0;const G=T.map((D,te)=>{var Y;D!=null&&(q=te);const B=(D==null?void 0:D.key)||`${X}-${te}`;return i.createElement(ha,{className:X,key:B,index:te,split:I,style:(Y=h==null?void 0:h.item)!==null&&Y!==void 0?Y:u.item},D)}),K=i.useMemo(()=>({latestIndex:q}),[q]);if(T.length===0)return null;const H={};return y&&(H.flexWrap="wrap"),!O&&j&&(H.columnGap=R),!w&&z&&(H.rowGap=S),F(i.createElement("div",Object.assign({ref:n,className:W,style:Object.assign(Object.assign(Object.assign({},H),s),M)},C),i.createElement(ba,{value:K},G)))}),Jt=$a;Jt.Compact=Xr;var ya=function(e,n){var t={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&n.indexOf(o)<0&&(t[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)n.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(t[o[r]]=e[o[r]]);return t};const Co=e=>{const{getPopupContainer:n,getPrefixCls:t,direction:o}=i.useContext(Ee),{prefixCls:r,type:l="default",danger:a,disabled:s,loading:c,onClick:u,htmlType:g,children:d,className:m,menu:b,arrow:p,autoFocus:f,overlay:v,trigger:I,align:M,open:y,onOpenChange:x,placement:h,getPopupContainer:C,href:R,icon:S=i.createElement(po,null),title:w,buttonsRender:O=re=>re,mouseEnterDelay:z,mouseLeaveDelay:j,overlayClassName:T,overlayStyle:N,destroyOnHidden:P,destroyPopupOnHide:F,dropdownRender:E,popupRender:A}=e,W=ya(e,["prefixCls","type","danger","disabled","loading","onClick","htmlType","children","className","menu","arrow","autoFocus","overlay","trigger","align","open","onOpenChange","placement","getPopupContainer","href","icon","title","buttonsRender","mouseEnterDelay","mouseLeaveDelay","overlayClassName","overlayStyle","destroyOnHidden","destroyPopupOnHide","dropdownRender","popupRender"]),X=t("dropdown",r),q=`${X}-button`,K={menu:b,arrow:p,autoFocus:f,align:M,disabled:s,trigger:s?[]:I,onOpenChange:x,getPopupContainer:C||n,mouseEnterDelay:z,mouseLeaveDelay:j,overlayClassName:T,overlayStyle:N,destroyOnHidden:P,popupRender:A||E},{compactSize:H,compactItemClassnames:D}=Gr(X,o),te=_(q,D,m);"destroyPopupOnHide"in e&&(K.destroyPopupOnHide=F),"overlay"in e&&(K.overlay=v),"open"in e&&(K.open=y),"placement"in e?K.placement=h:K.placement=o==="rtl"?"bottomLeft":"bottomRight";const Y=i.createElement(yn,{type:l,danger:a,disabled:s,loading:c,onClick:u,htmlType:g,href:R,title:w},d),B=i.createElement(yn,{type:l,danger:a,icon:S}),[L,oe]=O([Y,B]);return i.createElement(Jt.Compact,Object.assign({className:te,size:H,block:!0},W),L,i.createElement(xt,Object.assign({},K),oe))};Co.__ANT_BUTTON=!0;const $o=xt;$o.Button=Co;function xa(e,n,t){return typeof t=="boolean"?t:e.length?!0:bt(n).some(r=>r.type===go)}var yo=function(e,n){var t={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&n.indexOf(o)<0&&(t[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)n.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(t[o[r]]=e[o[r]]);return t};function St(e){let{suffixCls:n,tagName:t,displayName:o}=e;return r=>i.forwardRef((a,s)=>i.createElement(r,Object.assign({ref:s,suffixCls:n,tagName:t},a)))}const en=i.forwardRef((e,n)=>{const{prefixCls:t,suffixCls:o,className:r,tagName:l}=e,a=yo(e,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:s}=i.useContext(Ee),c=s("layout",t),[u,g,d]=mo(c),m=o?`${c}-${o}`:c;return u(i.createElement(l,Object.assign({className:_(t||m,r,g,d),ref:n},a)))}),Sa=i.forwardRef((e,n)=>{const{direction:t}=i.useContext(Ee),[o,r]=i.useState([]),{prefixCls:l,className:a,rootClassName:s,children:c,hasSider:u,tagName:g,style:d}=e,m=yo(e,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),b=$e(m,["suffixCls"]),{getPrefixCls:p,className:f,style:v}=Gn("layout"),I=p("layout",l),M=xa(o,c,u),[y,x,h]=mo(I),C=_(I,{[`${I}-has-sider`]:M,[`${I}-rtl`]:t==="rtl"},f,a,s,x,h),R=i.useMemo(()=>({siderHook:{addSider:S=>{r(w=>[].concat(Ie(w),[S]))},removeSider:S=>{r(w=>w.filter(O=>O!==S))}}}),[]);return y(i.createElement(so.Provider,{value:R},i.createElement(g,Object.assign({ref:n,className:C,style:Object.assign(Object.assign({},v),d)},b),c)))}),Ia=St({tagName:"div",displayName:"Layout"})(Sa),wa=St({suffixCls:"header",tagName:"header",displayName:"Header"})(en),Oa=St({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(en),Ma=St({suffixCls:"content",tagName:"main",displayName:"Content"})(en),Ce=Ia;Ce.Header=wa;Ce.Footer=Oa;Ce.Content=Ma;Ce.Sider=go;Ce._InternalSiderContext=yt;var Na={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M816 768h-24V428c0-141.1-104.3-257.7-240-277.1V112c0-22.1-17.9-40-40-40s-40 17.9-40 40v38.9c-135.7 19.4-240 136-240 277.1v340h-24c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h216c0 61.8 50.2 112 112 112s112-50.2 112-112h216c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM512 888c-26.5 0-48-21.5-48-48h96c0 26.5-21.5 48-48 48zM304 768V428c0-55.6 21.6-107.8 60.9-147.1S456.4 220 512 220c55.6 0 107.8 21.6 147.1 60.9S720 372.4 720 428v340H304z"}}]},name:"bell",theme:"outlined"},Ra=function(n,t){return i.createElement(De,ee({},n,{ref:t,icon:Na}))},Pa=i.forwardRef(Ra),Ea={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M904 160H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0 624H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0-312H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8z"}}]},name:"menu",theme:"outlined"},Ba=function(n,t){return i.createElement(De,ee({},n,{ref:t,icon:Ea}))},ja=i.forwardRef(Ba);const Ta=e=>{const n=e!=null&&e.algorithm?mr(e.algorithm):gr,t=Object.assign(Object.assign({},pr),e==null?void 0:e.token);return fr(t,{override:e==null?void 0:e.token},n,vr)};function za(e){const{sizeUnit:n,sizeStep:t}=e,o=t-2;return{sizeXXL:n*(o+10),sizeXL:n*(o+6),sizeLG:n*(o+2),sizeMD:n*(o+2),sizeMS:n*(o+1),size:n*o,sizeSM:n*o,sizeXS:n*(o-1),sizeXXS:n*(o-1)}}const ka=(e,n)=>{const t=n??Ft(e),o=t.fontSizeSM,r=t.controlHeight-4;return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},t),za(n??e)),br(o)),{controlHeight:r}),hr(Object.assign(Object.assign({},t),{controlHeight:r})))},me=(e,n)=>new ut(e).setA(n).toRgbString(),Ae=(e,n)=>new ut(e).lighten(n).toHexString(),Aa=e=>{const n=qn(e,{theme:"dark"});return{1:n[0],2:n[1],3:n[2],4:n[3],5:n[6],6:n[5],7:n[4],8:n[6],9:n[5],10:n[4]}},Ha=(e,n)=>{const t=e||"#000",o=n||"#fff";return{colorBgBase:t,colorTextBase:o,colorText:me(o,.85),colorTextSecondary:me(o,.65),colorTextTertiary:me(o,.45),colorTextQuaternary:me(o,.25),colorFill:me(o,.18),colorFillSecondary:me(o,.12),colorFillTertiary:me(o,.08),colorFillQuaternary:me(o,.04),colorBgSolid:me(o,.95),colorBgSolidHover:me(o,1),colorBgSolidActive:me(o,.9),colorBgElevated:Ae(t,12),colorBgContainer:Ae(t,8),colorBgLayout:Ae(t,0),colorBgSpotlight:Ae(t,26),colorBgBlur:me(o,.04),colorBorder:Ae(t,26),colorBorderSecondary:Ae(t,19)}},Da=(e,n)=>{const t=Object.keys(Cr).map(r=>{const l=qn(e[r],{theme:"dark"});return Array.from({length:10},()=>1).reduce((a,s,c)=>(a[`${r}-${c+1}`]=l[c],a[`${r}${c+1}`]=l[c],a),{})}).reduce((r,l)=>(r=Object.assign(Object.assign({},r),l),r),{}),o=n??Ft(e);return Object.assign(Object.assign(Object.assign({},o),t),$r(e,{generateColorPalettes:Aa,generateNeutralColorPalettes:Ha}))};function La(){const[e,n,t]=Xn();return{theme:e,token:n,hashId:t}}const _a={defaultSeed:Cn.token,useToken:La,defaultAlgorithm:Ft,darkAlgorithm:Da,compactAlgorithm:ka,getDesignToken:Ta,defaultConfig:Cn,_internalContext:yr},Ka=({title:e,items:n,className:t,icon:o,overlayClassName:r})=>$.jsx($o,{menu:{items:n},trigger:["click"],className:t,overlayClassName:r,children:$.jsx("span",{onClick:l=>l.preventDefault(),style:{cursor:"pointer"},children:$.jsxs(Jt,{children:[$.jsx("span",{children:e}),o&&$.jsx(Kr,{})]})})}),Va=()=>({showAlert:async({title:n,text:t,icon:o,background:r,showCancelButton:l,confirmButtonText:a,cancelButtonText:s})=>await xr.mixin({customClass:{confirmButton:"custom-swal-confirm-btn",cancelButton:"custom-swal-cancel-btn",popup:"custom-swal-popup",title:"custom-swal-title",content:"custom-swal-content"}}).fire({title:n,text:t,icon:o,background:r,showCancelButton:l,confirmButtonText:a,cancelButtonText:s})}),{Header:Wa}=Ce,Fa=[{label:"Home",path:"/home"},{label:"Sphere it",path:"/sphare-it"},{label:"Agents",path:"/agent"},{label:"Listings",path:"/listing"},{label:"States",path:"/state"},{label:"Contract Q",path:"/contract"}],Xa=()=>{let e=window.user;console.log("user",e);const[n,t]=i.useState(!1),o=Sr(),{showAlert:r}=Va(),{mutate:l}=Vr("logout",{useFormData:!1,showSuccessNotification:!0,onSuccess:()=>{localStorage.removeItem("session"),window.user={},o("/login")}}),s=Ir().pathname,c=async()=>{if((await r({title:"Sign Out",text:"Are you sure you want to sign out?",icon:"question",showCancelButton:!0,confirmButtonText:"Yes, Sign Out",cancelButtonText:"Cancel"})).isConfirmed){const d={device_token:e.api_token};l(d)}},u=[{key:"1",label:$.jsxs(Se,{to:"/profile/posts",className:`d-flex align-items-center ${s==="/profile/posts"?"active-dropdown-link":""}`,children:[$.jsx("div",{children:$.jsx("img",{src:"/assets/img/heart-icon.png",alt:""})}),$.jsx("div",{className:"ms-2",children:$.jsx("p",{children:"My Profile"})})]})},{key:"2",label:$.jsxs(Se,{to:"/favourite",className:`d-flex align-items-center ${s==="/favourite"?"active-dropdown-link":""}`,children:[$.jsx("div",{children:$.jsx("img",{src:"/assets/img/heart-icon.png",alt:""})}),$.jsx("div",{className:"ms-2",children:$.jsx("p",{children:"Favorite Properties"})})]})},{key:"3",label:$.jsxs(Se,{to:"/subscription",className:`d-flex align-items-center ${s==="/subscription"?"active-dropdown-link":""}`,children:[$.jsx("div",{children:$.jsx("img",{src:"/assets/img/card-icon.png",alt:""})}),$.jsx("div",{className:"ms-2",children:$.jsx("p",{children:"Subscription"})})]})},{key:"4",label:$.jsxs(Se,{to:"/about",className:`d-flex align-items-center ${s==="/about"?"active-dropdown-link":""}`,children:[$.jsx("div",{children:$.jsx("img",{src:"/assets/img/about-icon.png",alt:""})}),$.jsx("div",{className:"ms-2",children:$.jsx("p",{children:"About"})})]})},{key:"5",label:$.jsxs(Se,{to:"/setting",className:`d-flex align-items-center ${s==="/setting"?"active-dropdown-link":""}`,children:[$.jsx("div",{children:$.jsx("img",{src:"/assets/img/heart-icon.png",alt:""})}),$.jsx("div",{className:"ms-2",children:$.jsx("p",{children:"Settings"})})]})},{key:"6",label:$.jsxs("div",{className:"d-flex align-items-center",onClick:c,style:{cursor:"pointer"},children:[$.jsx("div",{children:$.jsx("img",{src:"/assets/img/logout-icon.png",alt:""})}),$.jsx("div",{className:"ms-2",children:$.jsx("p",{children:"Sign out"})})]})}];return $.jsxs(Wa,{className:"app-header",children:[$.jsxs("div",{className:"header-left",children:[$.jsx(Se,{to:"/home",children:$.jsx("img",{src:"/assets/img/logo.png",alt:"Logo",className:"logo"})}),$.jsx("div",{className:"mobile-toggle",onClick:()=>t(!n),children:n?$.jsx(wr,{}):$.jsx(ja,{})}),$.jsx("nav",{className:`nav-links d-flex align-items-center ${n?"open":""}`,children:Fa.map(g=>$.jsx(Or,{to:g.path,onClick:()=>t(!1),className:({isActive:d})=>d?"nav-link active":"nav-link",children:g.label},g.path))})]}),$.jsxs("div",{className:"header-right",children:[$.jsxs("div",{className:"icons d-flex align-items-center",children:[$.jsx("div",{className:"ms-3 ",children:$.jsx(Se,{to:"/notifications",children:$.jsx(xn,{shape:"square",size:"large",icon:$.jsx(Pa,{className:"icon"})})})}),$.jsx("div",{className:"ms-3 me-3",children:$.jsx(Se,{to:"/inbox",children:$.jsx(xn,{shape:"square",size:"large",icon:$.jsx("img",{src:"/assets/img/message-icon.png",alt:""})})})})]}),$.jsx(Ka,{overlayClassName:"profle-dropdown",title:$.jsxs("div",{className:"user-info",children:[$.jsx("img",{src:e==null?void 0:e.image_url,alt:e==null?void 0:e.name,className:"user-img"}),$.jsx("span",{className:"user-name",children:e==null?void 0:e.name})]}),items:u})]})]})},Ga=i.memo(Xa),{Content:qa}=Ce,Ja=({children:e})=>{const{token:{colorBgContainer:n,borderRadiusLG:t}}=_a.useToken();return $.jsx(Ce,{children:$.jsxs(Ce,{children:[$.jsx(Ga,{}),$.jsx(qa,{children:e})]})})};export{$o as D,Ja as I,At as R,Jt as S,kt as a,Va as u};
