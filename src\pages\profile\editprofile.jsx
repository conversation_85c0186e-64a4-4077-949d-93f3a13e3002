import React, { memo, useEffect } from "react";
import InnerLayout from "@/components/shared/layout/innerlayout";
import BaseInput from "@/components/shared/inputs/index";
import FlatButton from "@/components/shared/button/flatbutton";
import CustomUpload from "@/components/shared/upload/index";
import { Spin, Form } from "antd";
import { Link, useNavigate } from "react-router-dom";
import { combineRules, validations } from "@/config/rules";
import PhoneInputField from "@/components/shared/inputs/phonenumber";
import { useMutation, useQuery } from "@/hooks/reactQuery";
import useStartupData from "@/hooks/reactQuery/useStartupData";
import useLocationData from "@/hooks/useLocationData";
import { dateHelper } from "@/helpers/dateHelper";
import Helper from "@/helpers";
import {
  parseLanguagesForForm,
  transformLanguagesForAPI,
  getLanguageOptions,
} from "@/utils/languageUtils";

const EditProfile = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [selectedProfessionTypes, setSelectedProfessionTypes] = React.useState(
    []
  );

  // Get startup data with proper structure handling
  const { data: startupResponse } = useStartupData();

  // Extract startup data properly - data is nested under startupResponse.data
  const startupData = startupResponse?.data;

  // Use the location data hook
  const {
    selectedState,
    stateOptions,
    cityOptions,
    statesLoading,
    citiesLoading,
    handleStateChange,
    updateSelectedState,
  } = useLocationData();

  // Fetch user data using the Get User API with user ID as slug
  const { data: queryResponse, isLoading: userLoading } = useQuery("getUser", {
    slug: window.user?.id, // Pass user ID as URL slug
    staleTime: 0, // Data is always considered stale
    gcTime: 0, // No caching - data is garbage collected immediately
    enabled: !!window.user?.id, // Only fetch if user ID is available
    refetchOnMount: true, // Always refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window gains focus
    refetchOnReconnect: true, // Refetch when network reconnects
    cacheTime: 0, // Legacy option for older React Query versions
    retry: false, // Don't retry failed requests to avoid cache interference
  });

  // Extract data and pagination separately to avoid nesting issues
  const userData = queryResponse?.data;

  // Format phone number function
  const formatPhoneNumber = (phoneNumber) => {
    if (!phoneNumber) return "";
    // Remove all non-digits
    const digits = phoneNumber.toString().replace(/\D/g, "");
    // Format as (XXX) XXX-XXXX
    if (digits.length === 10) {
      return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
    }
    return phoneNumber; // Return original if not 10 digits
  };

  // Update window.user when userData changes, but preserve api_token
  useEffect(() => {
    if (userData) {
      const updatedUser = {
        ...userData,
        api_token: window.user?.api_token,
      };
      window.user = updatedUser;
      Helper.setStorageData("session", updatedUser);
    }
  }, [userData]);

  const { mutate: updateUser, isPending } = useMutation("updateUser", {
    invalidateQueries: ["getUser"],
    onSuccess: async (data) => {
      if (data) {
        await Helper.setStorageData("session", data.data);
        window.user = data.data;
        navigate("/profile/posts");
      }
    },
  });

  // Get language options using the utility (now uses IDs as values)
  const languageOptions = React.useMemo(() => {
    return getLanguageOptions(startupData?.languages);
  }, [startupData?.languages]);

  // Pre-fill form with user data when it's loaded
  useEffect(() => {
    if (userData && !userLoading) {
      // Set the selected state to trigger city loading
      if (userData?.state) {
        updateSelectedState(userData.state);
      }

      // Parse languages using the utility function with available languages for ID mapping
      let userLanguages = [];
      if (startupData?.languages && userData?.languages) {
        userLanguages = parseLanguagesForForm(
          userData.languages,
          startupData.languages
        );
      }

      const formValues = {
        name: userData?.name || "",
        email: userData?.email || "",
        mobile_no: formatPhoneNumber(userData?.mobile_no) || "",
        state: userData?.state || "",
        city: userData?.city || "",
        languages: userLanguages,
        multi_state_license: userData?.multi_state_license,
        // Parse professional_types data
        professional_type: (() => {
          // Handle new format (array of objects)
          if (
            userData?.professional_types &&
            Array.isArray(userData.professional_types)
          ) {
            return userData.professional_types.map((type) => type.name);
          }
          // Handle old format (single value)
          return userData?.professional_type
            ? [userData.professional_type]
            : [];
        })(),

        // Initialize license fields for each profession type
        broker_license_number: (() => {
          if (
            userData?.professional_types &&
            Array.isArray(userData.professional_types)
          ) {
            const brokerType = userData.professional_types.find(
              (type) => type.name === "broker"
            );
            return brokerType?.license_number || "";
          }
          return (
            userData?.broker_license_number || userData?.license_number || ""
          );
        })(),

        broker_license_expiry_date: (() => {
          if (
            userData?.professional_types &&
            Array.isArray(userData.professional_types)
          ) {
            const brokerType = userData.professional_types.find(
              (type) => type.name === "broker"
            );
            return brokerType?.license_expiry_date
              ? dateHelper.formatForForm(brokerType.license_expiry_date)
              : null;
          }
          return userData?.broker_license_expiry_date
            ? dateHelper.formatForForm(userData.broker_license_expiry_date)
            : userData?.license_expiry_date
            ? dateHelper.formatForForm(userData.license_expiry_date)
            : null;
        })(),

        lender_license_number: (() => {
          if (
            userData?.professional_types &&
            Array.isArray(userData.professional_types)
          ) {
            const lenderType = userData.professional_types.find(
              (type) => type.name === "lender"
            );
            return lenderType?.license_number || "";
          }
          return userData?.lender_license_number || "";
        })(),

        lender_license_expiry_date: (() => {
          if (
            userData?.professional_types &&
            Array.isArray(userData.professional_types)
          ) {
            const lenderType = userData.professional_types.find(
              (type) => type.name === "lender"
            );
            return lenderType?.license_expiry_date
              ? dateHelper.formatForForm(lenderType.license_expiry_date)
              : null;
          }
          return userData?.lender_license_expiry_date
            ? dateHelper.formatForForm(userData.lender_license_expiry_date)
            : null;
        })(),

        commercial_license_number: (() => {
          if (
            userData?.professional_types &&
            Array.isArray(userData.professional_types)
          ) {
            const commercialType = userData.professional_types.find(
              (type) => type.name === "commercial"
            );
            return commercialType?.license_number || "";
          }
          return userData?.license_number || "";
        })(),

        commercial_license_expiry_date: (() => {
          if (
            userData?.professional_types &&
            Array.isArray(userData.professional_types)
          ) {
            const commercialType = userData.professional_types.find(
              (type) => type.name === "commercial"
            );
            return commercialType?.license_expiry_date
              ? dateHelper.formatForForm(commercialType.license_expiry_date)
              : null;
          }
          return userData?.commercial_license_expiry_date
            ? dateHelper.formatForForm(userData.commercial_license_expiry_date)
            : null;
        })(),
        image_url: userData?.image_url || null,
        username: userData?.username || "",
        dob: userData?.dob ? dateHelper.formatForForm(userData.dob) : null,
        gender: userData?.gender || "",
      };
      // Set form values
      form.setFieldsValue(formValues);

      // Update selected profession types for conditional rendering
      if (
        userData?.professional_types &&
        Array.isArray(userData.professional_types)
      ) {
        setSelectedProfessionTypes(
          userData.professional_types.map((type) => type.name)
        );
      } else if (userData?.professional_type) {
        setSelectedProfessionTypes([userData.professional_type]);
      }
    }
  }, [userData, userLoading, startupData, form, updateSelectedState]);

  const onFinish = (values) => {
    // Build professional_types array of objects
    const professionalTypes = [];

    if (values.professional_type && values.professional_type.length > 0) {
      values.professional_type.forEach((type) => {
        const professionData = {
          name: type,
        };

        // Add license data based on profession type
        if (type === "broker" && values.broker_license_number) {
          professionData.license_number = values.broker_license_number;
          professionData.license_expiry_date = values.broker_license_expiry_date
            ? dateHelper.formatForAPI(values.broker_license_expiry_date)
            : "";
        } else if (type === "lender" && values.lender_license_number) {
          professionData.license_number = values.lender_license_number;
          professionData.license_expiry_date = values.lender_license_expiry_date
            ? dateHelper.formatForAPI(values.lender_license_expiry_date)
            : "";
        } else if (type === "commercial" && values.commercial_license_number) {
          professionData.license_number = values.commercial_license_number;
          professionData.license_expiry_date =
            values.commercial_license_expiry_date
              ? dateHelper.formatForAPI(values.commercial_license_expiry_date)
              : "";
        }

        professionalTypes.push(professionData);
      });
    }

    const transformedData = {
      ...values,
      // Stringify the professional_types array
      professional_types: JSON.stringify(professionalTypes),
      dob: values.dob ? dateHelper.formatForAPI(values.dob) : "",
      device: "web",
      device_token: "web-token-" + Date.now(), // Generate a web token
    };

    // Remove individual license fields and professional_type from the payload
    delete transformedData.professional_type;
    delete transformedData.broker_license_number;
    delete transformedData.broker_license_expiry_date;
    delete transformedData.lender_license_number;
    delete transformedData.lender_license_expiry_date;
    delete transformedData.commercial_license_number;
    delete transformedData.commercial_license_expiry_date;

    // Remove languages from main payload
    delete transformedData.languages;

    // Transform languages using the utility and merge with main payload
    const languagePayload = transformLanguagesForAPI(values.languages);
    const finalPayload = {
      ...transformedData,
      ...languagePayload,
    };

    updateUser({ slug: window.user.id, data: finalPayload });
  };

  // Handle state change with form reference
  const onStateChange = (value) => {
    handleStateChange(value, form);
  };

  // Handle profession type change
  const onProfessionTypeChange = (checkedValues) => {
    setSelectedProfessionTypes(checkedValues);
  };

  if (userLoading) {
    return (
      <InnerLayout>
        <div className="container-fluid">
          <div className="row mt-5">
            <div className="col-12 text-center">
              <Spin size="large" tip="Loading user data..." />
            </div>
          </div>
        </div>
      </InnerLayout>
    );
  }

  return (
    <InnerLayout>
      <div className="container-fluid">
        <div className="row mt-5">
          <h2 className="color-black mb-3">Profile</h2>

          {/* Left Profile Card - Original UI */}
          <div className="col-12 col-md-4 col-lg-4 col-xl-3">
            <div className="profile-edit text-center">
              <div className="text-end">
                <img src="/assets/img/more-icon.png" alt="" />
              </div>
              <div className="agent-profile mt-4">
                <img
                  src={userData?.image_url || "/assets/img/default-avatar.png"}
                  alt="Profile"
                  style={{
                    width: "100px",
                    height: "100px",
                    borderRadius: "50%",
                    objectFit: "cover",
                  }}
                />
              </div>
              <p className="mb-4">{userData?.name}</p>
              <div className="row">
                <div className="col-12 col-md-6">
                  <p>{userData?.post_count}</p>
                  <p>Post</p>
                </div>
                <div className="col-12 col-md-6">
                  <p>{userData?.property_count}</p>
                  <p>Listing</p>
                </div>
              </div>
            </div>
          </div>

          {/* Right Form Section - Using Signup Form Fields */}
          <div className="col-12 col-md-8 col-lg-8 col-xl-9">
            <div className="personal-border">
              <div className="border-bottom p-4">
                <p className="font-18">Edit Profile</p>
              </div>

              <Form
                name="editProfile"
                layout="vertical"
                form={form}
                onFinish={onFinish}
                scrollToFirstError={true}
                autoComplete="off"
                className="p-4"
              >
                {/* Profile Image Upload */}
                <div className="row">
                  <div className="col-12 text-center mb-4">
                    <CustomUpload
                      static_img={userData?.image_url}
                      fileType="image"
                      multiple={false}
                      maxSize={5}
                      uploadAction=""
                      useFormItem={true}
                      formItemProps={{
                        name: "image_url",
                        rules: combineRules("profile-image"),
                      }}
                      callback={(file) => {
                        form.setFieldsValue({
                          image_url: file.fileObj || file.originFileObj,
                        });
                      }}
                    />
                  </div>
                </div>

                {/* Personal Information */}
                <div className="profile-information">
                  <h4 className="mb-3">Personal Information</h4>
                  <div className="row">
                    <div className="col-12 col-md-6">
                      <BaseInput
                        name="name"
                        placeholder="e.g John Doe"
                        label="Full Name"
                        rules={combineRules(
                          "full-name",
                          validations.required,
                          validations.minLength(2),
                          validations.maxLength(25)
                        )}
                      />
                    </div>
                    <div className="col-12 col-md-6">
                      <BaseInput
                        name="email"
                        placeholder="<EMAIL>"
                        label="Email Address"
                        rules={combineRules(
                          "email",
                          validations.required,
                          validations.email
                        )}
                        disabled={true}
                      />
                    </div>
                    <div className="col-12 col-md-6">
                      <PhoneInputField
                        name="mobile_no"
                        placeholder="(XXX) XXX-XXXX"
                        label="Phone Number"
                        rules={combineRules(
                          "mobile-number",
                          validations.required,
                          validations.phone
                        )}
                      />
                    </div>
                  </div>
                </div>

                {/* Professional Information */}
                <div className="professional-information">
                  <h4 className="mt-4 mb-3">Professional Information</h4>
                  <div className="row">
                    {/* Location */}
                    <div className="col-12 col-md-6">
                      <div className="row">
                        <div className="col-6">
                          <BaseInput
                            name="state"
                            placeholder="State"
                            label="Location"
                            type="select"
                            rules={combineRules("state", validations.required)}
                            options={stateOptions}
                            loading={statesLoading}
                            handlechange={onStateChange}
                            showSearch={true}
                          />
                        </div>
                        <div className="col-6">
                          <BaseInput
                            name="city"
                            placeholder="City"
                            label=" "
                            type="select"
                            options={cityOptions}
                            rules={cityOptions.length > 0 ? combineRules("city", validations.required) : []}
                            disabled={!selectedState}
                            loading={citiesLoading}
                            showSearch={true}
                          />
                        </div>
                      </div>
                    </div>

                    {/* Languages */}
                    <div className="col-12 col-md-6">
                      <BaseInput
                        name="languages"
                        placeholder="Select Language"
                        label="Languages Spoken"
                        type="select"
                        rules={combineRules("language", validations.required)}
                        mode="multiple"
                        options={languageOptions}
                      />
                    </div>

                    {/* Multi State License */}
                    <div className="col-12 col-md-6">
                      <div className="ant-form-item d-block">
                        <div className="mt-2">
                          <BaseInput
                            type="radio"
                            name="multi_state_license"
                            label="Multi State License"
                            options={[
                              { value: true, label: "Yes" },
                              { value: false, label: "No" },
                            ]}
                            rules={combineRules(
                              "multi-state-license",
                              validations.required
                            )}
                          />
                        </div>
                      </div>
                    </div>

                    {/* Professional Type */}
                    <div className="col-12">
                      <BaseInput
                        type="checkboxgroup"
                        name="professional_type"
                        label="Profession Type"
                        options={[
                          {
                            value: "broker",
                            label: "Real Estate Broker",
                          },
                          {
                            value: "lender",
                            label: "Lender Mortgage Broker",
                          },
                          {
                            value: "commercial",
                            label: "Commercial Agent",
                          },
                        ]}
                        onChange={onProfessionTypeChange}
                        rules={combineRules(
                          "profession-type",
                          validations.required
                        )}
                      />
                    </div>

                    {/* Conditional License Sections */}
                    {selectedProfessionTypes.includes("broker") && (
                      <div className="col-12">
                        <h5 className="mt-3 font-18">
                          Real Estate Broker License
                        </h5>
                        <div className="row">
                          <div className="col-12 col-md-6">
                            <BaseInput
                              name="broker_license_number"
                              placeholder="1ABC234"
                              label="License No."
                              rules={combineRules(
                                "license",
                                validations.required
                              )}
                            />
                          </div>
                          <div className="col-12 col-md-6">
                            <BaseInput
                              name="broker_license_expiry_date"
                              placeholder="Expiry Date"
                              label="Expiry Date"
                              type="datepicker"
                              rules={combineRules(
                                "expiry",
                                validations.required
                              )}
                            />
                          </div>
                        </div>
                      </div>
                    )}

                    {selectedProfessionTypes.includes("lender") && (
                      <div className="col-12">
                        <h5 className="mt-3 font-18">
                          Lender / mtg Broker License
                        </h5>
                        <div className="row">
                          <div className="col-12 col-md-6">
                            <BaseInput
                              name="lender_license_number"
                              placeholder="1ABC234"
                              label="License No."
                              rules={combineRules(
                                "license",
                                validations.required
                              )}
                            />
                          </div>
                          <div className="col-12 col-md-6">
                            <BaseInput
                              name="lender_license_expiry_date"
                              placeholder="Expiry Date"
                              label="Expiry Date"
                              type="datepicker"
                              rules={combineRules(
                                "expiry",
                                validations.required
                              )}
                            />
                          </div>
                        </div>
                      </div>
                    )}

                    {selectedProfessionTypes.includes("commercial") && (
                      <div className="col-12">
                        <h5 className="mt-3 font-18">
                          Commercial Agent License
                        </h5>
                        <div className="row">
                          <div className="col-12 col-md-6">
                            <BaseInput
                              name="commercial_license_number"
                              placeholder="1ABC234"
                              label="License No."
                              rules={combineRules(
                                "license",
                                validations.required
                              )}
                            />
                          </div>
                          <div className="col-12 col-md-6">
                            <BaseInput
                              name="commercial_license_expiry_date"
                              placeholder="Expiry Date"
                              label="Expiry Date"
                              type="datepicker"
                              rules={combineRules(
                                "expiry",
                                validations.required
                              )}
                            />
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="col-12 mt-4 text-end">
                      <FlatButton
                        title="Cancel"
                        className="gray-btn me-3"
                        onClick={() => navigate(-1)}
                      />
                      <FlatButton
                        title={
                          isPending ? "Updating Profile..." : "Update Profile"
                        }
                        className="blue-btn"
                        htmlType="submit"
                        loading={isPending}
                        disabled={isPending}
                      />
                    </div>
                  </div>
                </div>
              </Form>
            </div>
          </div>
        </div>
      </div>
    </InnerLayout>
  );
};

export default EditProfile;
