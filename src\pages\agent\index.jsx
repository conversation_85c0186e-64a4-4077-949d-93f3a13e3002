import React from "react";
import { Skeleton } from "antd";
import InnerLayout from "@/components/shared/layout/innerlayout";
import AgentCard from "@/components/shared/card/agentcard";
import SearchBar from "@/components/shared/inputs/searchbar";
import Filter from "@/components/shared/Filter";
import ReusablePagination from "@/components/shared/ReusablePagination";
import EmptyState from "@/components/shared/EmptyState";
import useSearchFilterPagination from "@/hooks/useSearchFilterPagination";
import _ from "lodash";

const Agent = () => {
  const { data, isLoading, pagination, handlePageChange, handleFilterClick } =
    useSearchFilterPagination("getUser", { pageSize: 10 });

  // Filter fields configuration for agents
  const filterFields = [
    {
      name: "state",
      label: "State",
      type: "select",
      placeholder: "Select State",
    },
    {
      name: "city",
      label: "City",
      type: "select",
      placeholder: "Select City",
    },
    {
      name: "professional_type",
      label: "Professional Type",
      type: "radio",
    },
  ];

  return (
    <InnerLayout>
      <div className="container-fluid">
        {/* Search and Filter Row */}
        <div className="row">
          <div className="col-12">
            <SearchBar onFilterClick={handleFilterClick} />
          </div>
        </div>

        {/* Hidden Filter Component */}
        <Filter fields={filterFields} />

        <div className="row mt-5">
          {/* Show skeletons when loading */}
          {isLoading ? (
            Array.from({ length: 10 }).map((_, index) => (
              <div className="col-12 col-md-6 col-lg-6" key={index}>
                <Skeleton active avatar paragraph={{ rows: 3 }} />
              </div>
            ))
          ) : !_.isEmpty(data?.data) ? (
            data.data.map((user) => (
              <div className="col-12 col-md-6 col-lg-6" key={user.id}>
                <AgentCard agent={user} />
              </div>
            ))
          ) : (
            <EmptyState
              title="No agents found"
              description="No agents available at the moment"
            />
          )}
        </div>

        {/* Reusable Pagination */}
        <ReusablePagination
          pagination={pagination}
          handlePageChange={handlePageChange}
          isLoading={isLoading}
          itemName="agents"
          pageSizeOptions={["10", "20", "50"]}
          align="end"
        />
      </div>
    </InnerLayout>
  );
};

export default Agent;
