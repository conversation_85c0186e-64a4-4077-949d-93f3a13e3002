// Firebase service worker for background notifications
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js');

// Firebase config
const firebaseConfig = {
  apiKey: "AIzaSyCliv4clsA2gcuT5fg3sQs6Ds9KGV0UySc",
  authDomain: "sphereiconx-d68a6.firebaseapp.com",
  projectId: "sphereiconx-d68a6",
  storageBucket: "sphereiconx-d68a6.firebasestorage.app",
  messagingSenderId: "30096115243",
  appId: "1:30096115243:web:a589ee94a612498ccecc6e",
  measurementId: "G-FJWSZ0F0JG"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);
const messaging = firebase.messaging();

messaging.onBackgroundMessage(function (payload) {
 
  const notificationTitle = payload.data?.title;
  const notificationOptions = {
    body: payload.data?.body,
    icon: '/assets/img/logo.png',
    badge: '/assets/img/logo.png',
    tag: `boundaries-${Date.now()}`,
    renotify: false,
    data: payload.data || {},
    silent: false,
  };
  return self.clients.matchAll({
    includeUncontrolled: true,
    type: 'window'
  }).then(clients => {
    const isVisible = clients.some(client => client.visibilityState === 'visible');
    if (!isVisible) {
      return self.registration.showNotification(notificationTitle, notificationOptions);
    }
  });
});


self.addEventListener('notificationclick', function (event) {
  event.notification.close();

  let redirectUrl = '/';
  try {
    
    const customDataStr = event.notification?.data;
    const customData = event?.notification?.data?.customData;
    const parsed =  JSON.parse(customData);
    if (customDataStr) {
    
      if (customDataStr.recordType == "post") {
        redirectUrl = `/sphare-it/${customDataStr.recordId}`;
      }
       if (customDataStr.recordType == "post" && parsed.is_anonymous == true  ) {
        redirectUrl = `/contract/${customDataStr.recordId}`;
      }
      if (customDataStr.redirectTo == "chat_room"  ) {
        redirectUrl = `/inbox`;
      }
    }
  } catch (err) {
    console.error("Error parsing notification custom data:", err);
  }

  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true }).then(clientList => {
      for (const client of clientList) {
        if (client.url.includes(redirectUrl) && 'focus' in client) {
          return client.focus();
        }
      }
      if (clients.openWindow) {
        return clients.openWindow(redirectUrl);
      }
    })
  );
});
