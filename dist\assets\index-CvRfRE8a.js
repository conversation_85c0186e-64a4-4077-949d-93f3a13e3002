import{e as I,r as w,l as E,R as v,j as e,u as F,_ as O}from"./index-Dklazue-.js";import{I as k,u as A}from"./index-vmMMvWhJ.js";import{P as T}from"./postinput-CVyP1L1L.js";import{S as z}from"./searchbar-BCS1MYCc.js";import{u as R,S as M}from"./spherepost-C2uEIc3S.js";import{a as D,u as _}from"./useQuery-C3n1GVcJ.js";import{u as B,P as K,a as L}from"./PostsFilter-C_OIWz4k.js";import{R as Q}from"./ReusablePagination-B7_yMXG_.js";import{u as q}from"./useMutation-BrUrPIzr.js";import{S as n}from"./Skeleton--lTrbnrp.js";import{E as W}from"./index-CHbHgJvR.js";import"./button-DNhBCuue.js";import"./index-Cj6uPc4c.js";import"./flatbutton-B_tUS4QM.js";import"./trialUtils-PQN1eD5v.js";import"./index-BUt89ETK.js";import"./react-stripe.esm-ypQSOYN5.js";import"./useLocale-BNhrTARD.js";import"./index-CjGjc6T5.js";import"./index-BNeAK5sW.js";import"./index-ChnPz6Q1.js";import"./fade-B36sOBLs.js";import"./useLikePost-BsM_zOKm.js";import"./EditOutlined-DxjsqgjX.js";import"./DeleteOutlined-BjE_e6LE.js";import"./useLocationData-CQHRFCOb.js";import"./useStartupData-QD8kuEYy.js";import"./index-CatU6E6a.js";const H=(l={})=>{var u,y,j,P;const{searchKeyword:d}=I(),{filters:i,setIsFilterModalOpen:p}=B(),[s,x]=w.useState(d),h=w.useMemo(()=>E.debounce(t=>{x(t)},500),[]);w.useEffect(()=>(h(d),()=>h.cancel()),[d,h]);const c=w.useMemo(()=>Object.keys(i).some(t=>i[t]!==void 0&&i[t]!==null&&i[t]!=="")||s&&s.trim(),[i,s]),g=v.useMemo(()=>{const t={...i};return l.defaultParams&&Object.assign(t,l.defaultParams),s&&s.trim()&&(t.keyword=s.trim()),t.is_anonymous=!1,Object.keys(t).forEach(o=>{(t[o]===void 0||t[o]===null||t[o]==="")&&delete t[o]}),t},[s,i,l.defaultParams]),a=D("postItem",{params:g,initialPage:1,initialPageSize:l.pageSize||12,staleTime:0,gcTime:0,keepPreviousData:!0,refetchOnWindowFocus:!1,refetchOnMount:!0,enabled:!c,...l}),S=_("postItem",{params:g,staleTime:0,gcTime:0,refetchOnWindowFocus:!0,refetchOnMount:!0,enabled:c,...l}),m=c?S:a,b=(t,o)=>{!c&&a.setPageSize&&a.setPage&&(o!==a.pageSize?(a.setPageSize(o),a.setPage(1)):a.setPage(t))},N=()=>{p(!0)};return{...m,pagination:c?{current:1,pageSize:((y=(u=m.data)==null?void 0:u.data)==null?void 0:y.length)||0,total:((P=(j=m.data)==null?void 0:j.data)==null?void 0:P.length)||0,totalPages:1}:a.pagination,handlePageChange:b,handleFilterClick:N,searchKeyword:d,debouncedSearchKeyword:s,filters:i,apiParams:g,hasActiveFilters:c}},Y=()=>{const l=F(),{showAlert:d}=A(),[i,p]=v.useState(null),{data:s,isLoading:x,pagination:h,handlePageChange:c,handleFilterClick:g,hasActiveFilters:a}=H({pageSize:12}),S=v.useCallback(()=>{p(null)},[]),m=R(i,S),{mutate:b,isPending:N}=q("deletePost",{useFormData:!1,showSuccessNotification:!0,invalidateQueries:[{queryKey:["postItem"],type:"paginated"}]}),u=(s==null?void 0:s.data)||[],y=[{name:"state",label:"State",type:"select",placeholder:"Select State"},{name:"city",label:"City",type:"select",placeholder:"Select City"},{name:"professional_type",label:"Profession Type",type:"radio"},{name:"last_10_days",label:"Last 10 days posts only",type:"checkbox"}],j=r=>{const f=u.find(C=>C.id===r);f&&(p(f),window.scrollTo({top:0,behavior:"smooth"}))},P=()=>{p(null),m.resetForm()},t=async r=>{(await d({title:"Are you sure?",text:"Are you sure you want to delete this post?",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, delete it!",cancelButtonText:"Cancel"})).isConfirmed&&b({slug:r,data:""})},o=r=>!0;return e.jsx(k,{children:e.jsxs("div",{className:"container-fluid",children:[e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-12",children:[e.jsx(z,{onFilterClick:g}),e.jsx(T,{postCreationHook:m,onCancelEdit:P})]})}),e.jsx(L,{fields:y}),e.jsx("div",{className:"row mt-5",children:x?Array.from({length:12}).map((r,f)=>e.jsx("div",{className:"col-12 col-md-6 col-lg-4 col-xl-3 mb-4",children:e.jsx("div",{className:"card",children:e.jsxs("div",{className:"card-body",children:[e.jsxs("div",{className:"d-flex align-items-center mb-3",children:[e.jsx(n.Avatar,{size:40}),e.jsxs("div",{className:"ms-3 flex-grow-1",children:[e.jsx(n.Input,{style:{width:120,height:16}}),e.jsx("div",{className:"mt-1",children:e.jsx(n.Input,{style:{width:80,height:12}})})]})]}),e.jsx(n.Image,{active:!0,className:"w-100 text-center align-items-center d-flex mb-2",style:{width:"100%",height:200,display:"block"}}),e.jsx(n,{paragraph:{rows:2,width:["100%","80%"]}}),e.jsxs("div",{className:"d-flex justify-content-between align-items-center mt-3",children:[e.jsxs("div",{className:"d-flex gap-3",children:[e.jsx(n.Input,{style:{width:60,height:20}}),e.jsx(n.Input,{style:{width:60,height:20}}),e.jsx(n.Input,{style:{width:60,height:20}})]}),e.jsx(n.Input,{style:{width:40,height:20}})]})]})})},f)):O.isEmpty(u)?e.jsx("div",{className:"col-12 d-flex justify-content-center",children:e.jsx(W,{description:a?"No posts match your search criteria":"No posts available"})}):u.map(r=>e.jsx("div",{className:"col-12 col-md-6 col-lg-4 col-xl-3 mb-4",children:e.jsx(M,{...r,onClick:()=>l(`/sphare-it/${r.id}`),showActions:o(),onEdit:j,onDelete:t})},r.id))}),!a&&e.jsx(Q,{pagination:h,handlePageChange:c,isLoading:x,itemName:"posts",pageSizeOptions:["12","24","48","96"],align:"end"})]})})},we=()=>e.jsx(K,{children:e.jsx(Y,{})});export{we as default};
