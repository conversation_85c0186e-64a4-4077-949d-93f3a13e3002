import { notification } from "antd";

const api = {
  // Authentication endpoints
  signup: { method: "POST", url: "user" },
  login: { method: "POST", url: "user/login" },
  forgotPassword: { method: "POST", url: "user/forgot-password" },
  changePassword: { method: "POST", url: "user/change-password" },
  updateUser: { method: "PATCH", url: "user" },
  getUser: { method: "GET", url: "user" },
  disableAccount: { method: "POST", url: "user/disable-account" },
  deleteAccount: { method: "DELETE", url: "user" },
  getUser: { method: "GET", url: "user" },
  getProfile: { method: "GET", url: "user/profile" },
  logout: { method: "POST", url: "user/logout", private: true },

  // General data endpoints
  startupData: { method: "GET", url: "general/startup-data" },
  cities: { method: "GET", url: "cities" },

  // File Upload
  fileUpload: { method: "POST", url: "files/upload" },

  // Subcription
  createSubscription: { method: "POST", url: "subscriptions" },
  getCurrentSubscription: { method: "GET", url: "subscriptions/current" },
  cancelSubscription: { method: "POST", url: "subscriptions/cancel" },
  getSubscriptionProduct: { method: "GET", url: "subscriptions/products" },

  // Property Listing
  properties: { method: "GET", url: "properties" },
  getProperty: { method: "GET", url: "properties" },
  addProperty: { method: "POST", url: "properties" },
  updateProperty: { method: "PATCH", url: "properties" },
  deleteProperty: { method: "DELETE", url: "properties" },

  // Post
  postItem: { method: "GET", url: "posts" },
  addPost: { method: "POST", url: "posts" },
  deletePost: { method: "DELETE", url: "posts" },
  updatePost: { method: "PATCH", url: "posts" },
  likePost: { method: "POST", url: "likes" },
  comments: { method: "POST", url: "comments" },
  getComments: { method: "GET", url: "comments" },

  // Notifications
  getNotification: { method: "GET", url: "notifications" },

};

export default api;