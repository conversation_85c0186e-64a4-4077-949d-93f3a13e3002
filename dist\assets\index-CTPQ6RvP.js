import{r as C,u as P,R as I,j as e,_ as A}from"./index-Dklazue-.js";import{I as B,u as _}from"./index-vmMMvWhJ.js";import{t as k,P as q}from"./propertyUtils-DF3QzgTW.js";import{S as F}from"./searchbar-BCS1MYCc.js";import{I as T}from"./iconbutton-DMkwwwLX.js";import"./index-BUt89ETK.js";import"./react-stripe.esm-ypQSOYN5.js";import{F as z}from"./Filter-D5O_6hp-.js";import{R as E}from"./ReusablePagination-B7_yMXG_.js";import{E as L}from"./EmptyState-YbiQZMha.js";import{u as O}from"./useSearchFilterPagination-BuDUyyEy.js";import{u as M}from"./useMutation-BrUrPIzr.js";import{u as R}from"./useStartupData-QD8kuEYy.js";import{h as D}from"./trialUtils-PQN1eD5v.js";import{S as s}from"./Skeleton--lTrbnrp.js";import"./button-DNhBCuue.js";import"./index-Cj6uPc4c.js";import"./EditOutlined-DxjsqgjX.js";import"./DeleteOutlined-BjE_e6LE.js";import"./index-CjGjc6T5.js";import"./index-CHbHgJvR.js";import"./useLocale-BNhrTARD.js";import"./useLocationData-CQHRFCOb.js";import"./useQuery-C3n1GVcJ.js";import"./languageUtils-BKYM3hOY.js";import"./index-CatU6E6a.js";import"./index-ChnPz6Q1.js";import"./fade-B36sOBLs.js";const je=()=>{var h,u;const[K,Y]=C.useState(!1),o=P(),{showAlert:n}=_(),y=async()=>{await D(n,o)||o("/listing/add")},{data:i,isLoading:c,pagination:g,handlePageChange:x,handleFilterClick:f}=O("properties",{pageSize:12}),{data:r,loading:d}=R(),l=r==null?void 0:r.data,{mutate:j,isPending:$}=M("deleteProperty",{showSuccessNotification:!0,invalidateQueries:[{queryKey:["properties"]},{queryKey:["getProperty"]},{queryKey:["getUser"]}]}),b=t=>{o(`/listing/add/${t.id}`)},v=async t=>{(await n({title:"Are you sure?",text:"Do you want to delete this property",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, delete it!",cancelButtonText:"Cancel"})).isConfirmed&&j({slug:t.id,data:""})},p=I.useMemo(()=>i!=null&&i.data?k(i.data,{showActions:!0,onEdit:b,onDelete:v}):[],[i]),m=Array.from({length:10},(t,a)=>({value:a+1,label:a+1})),S=Array.from({length:10},(t,a)=>({value:a+1,label:`${a+1} Car${a+1>1?"s":""}`})),w=[{value:"sqft",label:"Sq.Ft"},{value:"sq.m",label:"Sq.M"},{value:"sq.yd",label:"Sq.Yd"},{value:"acres",label:"Acres"}],N=[{name:"type_id",label:"Property Type",type:"select",placeholder:"Select Property Type",options:(h=l==null?void 0:l.propertyTypes)==null?void 0:h.map(t=>({value:t.id,label:t.name})),loading:d},{name:"home_style_id",label:"Home Style",type:"select",placeholder:"Select Home Style",options:(u=l==null?void 0:l.homeStyles)==null?void 0:u.map(t=>({value:t.id,label:t.name})),loading:d},{name:"state",label:"State",type:"select",placeholder:"Select State"},{name:"city",label:"City",type:"select",placeholder:"Select City"},{name:"zip",label:"Zip Code",type:"input",placeholder:"Enter Zip Code"},{name:"basement",label:"Basement",type:"radio",options:[{value:!0,label:"Yes"},{value:!1,label:"No"}]},{name:"max_bed",label:"Bed",type:"select",placeholder:"Select Bedrooms",options:m},{name:"max_bath",label:"Bath",type:"select",placeholder:"Select Bathrooms",options:m},{name:"max_garage",label:"Garage",type:"select",placeholder:"Select Garage",options:S},{name:"size_type",label:"Size Type",type:"select",placeholder:"Select Size Type",options:w}];return e.jsx(B,{children:e.jsxs("div",{className:"container-fluid",children:[e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12",children:e.jsx(F,{onFilterClick:f})}),e.jsx("div",{className:"col-12 mt-3",children:e.jsx(T,{icon:e.jsx("img",{src:"/assets/img/addlist-icon.png"}),title:"Add Listing",className:"blue-btn",onClick:y})})]}),e.jsx(z,{fields:N}),e.jsx("div",{className:"row mt-3",children:c?Array.from({length:12}).map((t,a)=>e.jsx("div",{className:"col-12 col-sm-6 col-md-4 col-lg-3",children:e.jsx("div",{className:"card",children:e.jsxs("div",{className:"card-body",children:[e.jsxs("div",{className:"d-flex align-items-center mb-3",children:[e.jsx(s.Avatar,{size:40}),e.jsxs("div",{className:"ms-3 flex-grow-1",children:[e.jsx(s.Input,{style:{width:120,height:16}}),e.jsx("div",{className:"mt-1",children:e.jsx(s.Input,{style:{width:80,height:12}})})]})]}),e.jsx(s.Image,{active:!0,className:"w-100 text-center align-items-center d-flex mb-2",style:{width:"100%",height:200,display:"block"}}),e.jsx(s,{paragraph:{rows:2,width:["100%","80%"]}}),e.jsxs("div",{className:"d-flex justify-content-between align-items-center mt-3",children:[e.jsxs("div",{className:"d-flex gap-3",children:[e.jsx(s.Input,{style:{width:60,height:20}}),e.jsx(s.Input,{style:{width:60,height:20}}),e.jsx(s.Input,{style:{width:60,height:20}})]}),e.jsx(s.Input,{style:{width:40,height:20}})]})]})})},a)):A.isEmpty(p)?e.jsx(L,{title:"No properties found",description:"No properties available at the moment"}):p.map((t,a)=>e.jsx("div",{className:"col-12 col-sm-6 col-md-4 col-lg-3",children:e.jsx(q,{...t})},t.id||a))}),e.jsx(E,{pagination:g,handlePageChange:x,isLoading:c,itemName:"properties",pageSizeOptions:["12","24","48"]})]})})};export{je as default};
