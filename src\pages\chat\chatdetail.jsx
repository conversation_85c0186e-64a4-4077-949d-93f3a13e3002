import React, { useState, useEffect, useRef } from "react";
import MessageBubble from "./msgbubble";
import PostInput from "@/components/shared/inputs/postinput";
import ChatHeader from "./chatheader";

const ChatDetail = ({
  messages,
  selectedRoom,
  otherUser,
  onSendMessage,
  connected,
}) => {
  const [messageText, setMessageText] = useState("");
  const messagesEndRef = useRef(null);

  // Auto scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleSendMessage = () => {
    if (messageText.trim() && connected && onSendMessage) {
      onSendMessage(messageText);
      setMessageText("");
    }
  };

  // Show empty state when no room is selected
  if (!selectedRoom) {
    return (
      <div className="chat-detail d-flex align-items-center justify-content-center">
        <div className="text-center">
          <h4 className="color-light mt-3">Select a conversation</h4>
          <p className="color-light">
            Choose a conversation from the list to start messaging
          </p>
        </div>
      </div>
    );
  }

  // Prepare user data for chat header
  const headerUser = {
    name: otherUser?.name,
    avatar:
      otherUser?.image_url ||
      selectedRoom.room_image ||
      "/assets/img/avatar-1.png",
  };

  return (
    <div className="chat-detail">
      <ChatHeader user={headerUser} />

      <div className="chat-area">
        {messages.length === 0 ? (
          <div className="text-center py-5">
            <p className="color-light">No messages yet</p>
            <p className="color-light font-12">
              Start the conversation by sending a message
            </p>
          </div>
        ) : (
          messages.map((msg, idx) => (
            <MessageBubble
              key={msg.id ? `msg-${msg.id}` : `temp-${idx}-${Date.now()}`}
              msg={msg}
            />
          ))
        )}

        <div ref={messagesEndRef} />
      </div>

      <div className="chat-send-area">
        <PostInput
          placeholder="Type a message..."
          button={
            <img
              src="/assets/img/chat-send-icon.png"
              alt="Send"
              style={{
                cursor:
                  connected && messageText.trim() ? "pointer" : "not-allowed",
                opacity: connected && messageText.trim() ? 1 : 0.5,
              }}
              onClick={handleSendMessage}
            />
          }
          postCreationHook={{
            content: messageText,
            setContent: setMessageText,
            isFormValid: messageText.trim().length > 0,
            handleSubmit: handleSendMessage,
          }}
        />
      </div>
    </div>
  );
};

export default ChatDetail;
