import{r as n,W as l,v as o,o as L,p as y}from"./index-Dklazue-.js";import{a as w}from"./useMutation-BrUrPIzr.js";var z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M885.9 533.7c16.8-22.2 26.1-49.4 26.1-77.7 0-44.9-25.1-87.4-65.5-111.1a67.67 67.67 0 00-34.3-9.3H572.4l6-122.9c1.4-29.7-9.1-57.9-29.5-79.4A106.62 106.62 0 00471 99.9c-52 0-98 35-111.8 85.1l-85.9 311h-.3v428h472.3c9.2 0 18.2-1.8 26.5-5.4 47.6-20.3 78.3-66.8 78.3-118.4 0-12.6-1.8-25-5.4-37 16.8-22.2 26.1-49.4 26.1-77.7 0-12.6-1.8-25-5.4-37 16.8-22.2 26.1-49.4 26.1-77.7-.2-12.6-2-25.1-5.6-37.1zM112 528v364c0 17.7 14.3 32 32 32h65V496h-65c-17.7 0-32 14.3-32 32z"}}]},name:"like",theme:"filled"},_=function(s,c){return n.createElement(l,o({},s,{ref:c,icon:z}))},S=n.forwardRef(_),C={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M885.9 533.7c16.8-22.2 26.1-49.4 26.1-77.7 0-44.9-25.1-87.4-65.5-111.1a67.67 67.67 0 00-34.3-9.3H572.4l6-122.9c1.4-29.7-9.1-57.9-29.5-79.4A106.62 106.62 0 00471 99.9c-52 0-98 35-111.8 85.1l-85.9 311H144c-17.7 0-32 14.3-32 32v364c0 17.7 14.3 32 32 32h601.3c9.2 0 18.2-1.8 26.5-5.4 47.6-20.3 78.3-66.8 78.3-118.4 0-12.6-1.8-25-5.4-37 16.8-22.2 26.1-49.4 26.1-77.7 0-12.6-1.8-25-5.4-37 16.8-22.2 26.1-49.4 26.1-77.7-.2-12.6-2-25.1-5.6-37.1zM184 852V568h81v284h-81zm636.4-353l-21.9 19 13.9 25.4a56.2 56.2 0 016.9 27.3c0 16.5-7.2 32.2-19.6 43l-21.9 19 13.9 25.4a56.2 56.2 0 016.9 27.3c0 16.5-7.2 32.2-19.6 43l-21.9 19 13.9 25.4a56.2 56.2 0 016.9 27.3c0 22.4-13.2 42.6-33.6 51.8H329V564.8l99.5-360.5a44.1 44.1 0 0142.2-32.3c7.6 0 15.1 2.2 21.1 6.7 9.9 7.4 15.2 18.6 14.6 30.5l-9.6 198.4h314.4C829 418.5 840 436.9 840 456c0 16.5-7.2 32.1-19.6 43z"}}]},name:"like",theme:"outlined"},p=function(s,c){return n.createElement(l,o({},s,{ref:c,icon:C}))},q=n.forwardRef(p),O={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 512a48 48 0 1096 0 48 48 0 10-96 0zm200 0a48 48 0 1096 0 48 48 0 10-96 0zm-400 0a48 48 0 1096 0 48 48 0 10-96 0zm661.2-173.6c-22.6-53.7-55-101.9-96.3-143.3a444.35 444.35 0 00-143.3-96.3C630.6 75.7 572.2 64 512 64h-2c-60.6.3-119.3 12.3-174.5 35.9a445.35 445.35 0 00-142 96.5c-40.9 41.3-73 89.3-95.2 142.8-23 55.4-34.6 114.3-34.3 174.9A449.4 449.4 0 00112 714v152a46 46 0 0046 46h152.1A449.4 449.4 0 00510 960h2.1c59.9 0 118-11.6 172.7-34.3a444.48 444.48 0 00142.8-95.2c41.3-40.9 73.8-88.7 96.5-142 23.6-55.2 35.6-113.9 35.9-174.5.3-60.9-11.5-120-34.8-175.6zm-151.1 438C704 845.8 611 884 512 884h-1.7c-60.3-.3-120.2-15.3-173.1-43.5l-8.4-4.5H188V695.2l-4.5-8.4C155.3 633.9 140.3 574 140 513.7c-.4-99.7 37.7-193.3 107.6-263.8 69.8-70.5 163.1-109.5 262.8-109.9h1.7c50 0 98.5 9.7 144.2 28.9 44.6 18.7 84.6 45.6 119 80 34.3 34.3 61.3 74.4 80 119 19.4 46.2 29.1 95.2 28.9 145.8-.6 99.6-39.7 192.9-110.1 262.7z"}}]},name:"message",theme:"outlined"},R=function(s,c){return n.createElement(l,o({},s,{ref:c,icon:O}))},x=n.forwardRef(R),A={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M456 231a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"more",theme:"outlined"},b=function(s,c){return n.createElement(l,o({},s,{ref:c,icon:A}))},F=n.forwardRef(b);const H=(a,s=!1,c=0)=>{const[m,f]=n.useState(s),[k,h]=n.useState(c),u=L(),d=n.useCallback(t=>{const r=t?1:-1;u.setQueriesData({queryKey:["postItem"]},e=>e&&(e.data&&Array.isArray(e.data)?{...e,data:e.data.map(i=>i.id===a||i.id===parseInt(a)?{...i,is_liked:t,likes_count:Math.max(0,(i.likes_count||0)+r)}:i)}:e.data&&(e.data.id===a||e.data.id===parseInt(a))?{...e,data:{...e.data,is_liked:t,likes_count:Math.max(0,(e.data.likes_count||0)+r)}}:Array.isArray(e)?e.map(i=>i.id===a||i.id===parseInt(a)?{...i,is_liked:t,likes_count:Math.max(0,(i.likes_count||0)+r)}:i):e.id===a||e.id===parseInt(a)?{...e,is_liked:t,likes_count:Math.max(0,(e.likes_count||0)+r)}:e))},[a,u]),{mutate:v,isPending:g}=w({mutationFn:async t=>{const r={type:"post",value:t};return await y.request("likePost",{slug:a,data:r,useFormData:!1,showSuccessNotification:!1})},onMutate:async t=>(await u.cancelQueries({queryKey:["postItem"]}),f(t),h(r=>t?r+1:Math.max(0,r-1)),d(t),{previousLiked:!t}),onError:(t,r,e)=>{f((e==null?void 0:e.previousLiked)??!r),h(i=>r?Math.max(0,i-1):i+1),d((e==null?void 0:e.previousLiked)??!r)},onSettled:()=>{u.invalidateQueries({queryKey:["postItem"],exact:!1})}}),M=n.useCallback(()=>{v(!m)},[m,v]);return{isLiked:m,likesCount:k,isToggling:g,handleLikeToggle:M}};export{S as R,q as a,F as b,x as c,H as u};
