import React from "react";
import PropertyCard from "@/components/shared/card/propertycard";
import { useQuery } from "@/hooks/reactQuery";
import { Skeleton } from "antd";
import { transformPropertiesData } from "@/utils/propertyUtils";
import EmptyState from "@/components/shared/EmptyState";
import { useNavigate } from "react-router-dom";
import { useMutation } from "@/hooks/reactQuery";
import useSweetAlert from "@/hooks/useSweetAlert";
import _ from "lodash";

const AgentProperties = ({ userId, isMyProfile = false }) => {
  const navigate = useNavigate();
  const { showAlert } = useSweetAlert();

  // Fetch properties for specific user
  const {
    data: propertiesResponse,
    isLoading,
    error,
  } = useQuery("properties", {
    params: { user_id: userId },
    enabled: !!userId,
    staleTime:0,
    gcTime: 0,
  });

  // Delete property mutation
  const { mutate: deleteProperty, isPending: isDeleting } = useMutation(
    "deleteProperty",
    {
      showSuccessNotification: true,
      invalidateQueries: [
        { queryKey: ["properties"] },
        { queryKey: ["getProperty"] },
        { queryKey: ["getUser"] },
      ],
    }
  );

  // Handle edit property
  const handleEditProperty = (property) => {
    navigate(`/listing/add/${property.id}`);
  };

  // Handle delete property with confirmation
  const handleDeleteProperty = async (property) => {
    const result = await showAlert({
      title: "Are you sure?",
      text: `Do you want to delete this property`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "Cancel",
    });

    if (result.isConfirmed) {
      deleteProperty({ slug: property.id, data: "" });
    }
  };

  // Transform server data to match PropertyCard props with actions (same as main listing page)
  const properties = React.useMemo(() => {
    if (!propertiesResponse?.data) return [];
    return transformPropertiesData(propertiesResponse.data, {
      showActions: true,
      onEdit: handleEditProperty,
      onDelete: handleDeleteProperty,
    });
  }, [propertiesResponse]);

  if (isLoading) {
    return (
      <div className="row mt-4">
        {Array.from({ length: 12 }).map((_, index) => (
          <div key={index} className="col-12 col-sm-6 col-md-4 col-lg-3">
            <Skeleton active paragraph={{ rows: 4 }} />
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <EmptyState
        title="Failed to load properties"
        description="Failed to load properties. Please try again."
      />
    );
  }

  if (_.isEmpty(properties)) {
    return (
      <EmptyState
        title="No properties found"
        description={
          isMyProfile
            ? "You haven't listed any properties yet"
            : "This agent hasn't listed any properties yet"
        }
      />
    );
  }

  return (
    <div className="row mt-3">
      {properties.map((item, index) => (
        <div
          key={item.id || index}
          className="col-12 col-sm-6 col-md-4 col-lg-3"
        >
          <PropertyCard {...item} />
        </div>
      ))}
    </div>
  );
};

export default AgentProperties;
