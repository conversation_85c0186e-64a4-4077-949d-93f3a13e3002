import{e as m,V as h,r as i,l as P,R as S}from"./index-Dklazue-.js";import{a as p}from"./useQuery-C3n1GVcJ.js";const O=(n,t={})=>{const{searchKeyword:l}=m(),{filters:r,setIsFilterModalOpen:g}=h(),[s,d]=i.useState(l),u=i.useMemo(()=>P.debounce(e=>{d(e)},500),[]);i.useEffect(()=>(u(l),()=>u.cancel()),[l,u]);const o=S.useMemo(()=>{const e={...r};return t.defaultParams&&Object.assign(e,t.defaultParams),s&&s.trim()&&(n==="getUser"?e.name=s.trim():e.keyword=s.trim()),r.languages&&Array.isArray(r.languages)&&(delete e.languages,r.languages.forEach((a,f)=>{e[`languages[${f}]`]=a})),Object.keys(e).forEach(a=>{(e[a]===void 0||e[a]===null||e[a]==="")&&delete e[a]}),e},[s,r,n,t.defaultParams]),c=p(n,{params:o,initialPage:1,initialPageSize:t.pageSize||10,staleTime:0,gcTime:0,keepPreviousData:!0,refetchOnWindowFocus:!1,refetchOnMount:!0,...t});return{...c,handlePageChange:(e,a)=>{a!==c.pageSize?(c.setPageSize(a),c.setPage(1)):c.setPage(e)},handleFilterClick:()=>{g(!0)},searchKeyword:l,debouncedSearchKeyword:s,filters:r,apiParams:o}};export{O as u};
