import React from "react";
import { useQuery } from "@/hooks/reactQuery";
import useStartupData from "@/hooks/reactQuery/useStartupData";

/**
 * Custom hook for managing state and city data
 * Provides state options, city options based on selected state, and handlers
 */
export const useLocationData = (initialState = "") => {
  const [selectedState, setSelectedState] = React.useState(initialState);

  // Get startup data with proper structure handling
  const { data: startupResponse, isLoading: statesLoading } = useStartupData();

  // Extract startup data properly - data is nested under startupResponse.data
  const startupData = startupResponse?.data;

  // Get the isoCode for the selected state name (for API calls)
  const selectedStateIsoCode = React.useMemo(() => {
    if (!selectedState || !startupData?.states) return "";

    const stateObj = startupData.states.find(
      (state) => state.name === selectedState
    );
    return stateObj?.isoCode || "";
  }, [selectedState, startupData?.states]);

  // Fetch cities based on selected state
  const { data: citiesResponse, isLoading: citiesLoading } = useQuery(
    "cities",
    {
      params: { stateCode: selectedStateIsoCode }, // Use isoCode for API call
      enabled: !!selectedStateIsoCode, // Only fetch when a state is selected
      staleTime: 0,
      gcTime: 0,
      select: (data) => {
        if (Array.isArray(data?.data)) {
          return data;
        }
        return [];
      },
    }
  );

  // Extract cities data properly
  const cities = citiesResponse?.data;

  // Transform states data for select options (US states only)
  const stateOptions = React.useMemo(() => {
    if (!startupData?.states) return [];

    const options = startupData.states
      .filter(
        (state) => state.countryCode === "US" && !state.isoCode.includes("UM")
      )
      .map((state) => ({
        value: state.name, // Use full state name instead of isoCode
        label: state.name,
        isoCode: state.isoCode, // Keep isoCode for API calls if needed
      }));

    return options;
  }, [startupData?.states]);

  // Transform cities data for select options
  const cityOptions = React.useMemo(() => {
    if (!cities || !Array.isArray(cities)) {
      return [];
    }

    const options = cities.map((city) => {
      // Handle different possible city object structures
      const cityName = city.name || city.city_name || city.label || city;

      return {
        value: cityName, // Use full city name instead of ID
        label: cityName,
        id: city.id, // Keep ID for reference if needed
      };
    });

    return options;
  }, [cities]);

  const handleStateChange = React.useCallback((value, form = null) => {
    setSelectedState(value);

    if (form) {
      form.setFieldsValue({ city: undefined });
    }
  }, []);

  const updateSelectedState = React.useCallback((state) => {
    setSelectedState(state);
  }, []);

  return {
    selectedState,
    stateOptions,
    cityOptions,
    statesLoading,
    citiesLoading,
    handleStateChange,
    updateSelectedState,
  };
};

export default useLocationData;