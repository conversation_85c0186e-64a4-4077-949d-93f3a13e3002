import React, { memo } from "react";
import InnerLayout from "@/components/shared/layout/innerlayout";
import PropertyCard from "@/components/shared/card/propertycard";
import SpherePost from "@/components/shared/card/spherepost";
import ContractPost from "@/pages/contract/components/ContractPost";
import { Link, useNavigate } from "react-router-dom";
import { useQuery } from "@/hooks/reactQuery";
import { Skeleton } from "antd";
import { transformPropertiesData } from "@/utils/propertyUtils";

const Home = () => {
  const navigate = useNavigate();

  // Fetch recent Sphere It posts (4 posts)
  const { data: sphereItData, isLoading: sphereItLoading } = useQuery(
    "postItem",
    {
      params: { limit: 4 },
      staleTime: 5 * 60 * 1000,
    }
  );

  // Fetch recent properties (4 properties)
  const { data: propertiesData, isLoading: propertiesLoading } = useQuery(
    "properties",
    {
      params: { limit: 4 },
      staleTime: 5 * 60 * 1000,
    }
  );

  // Fetch recent Contract Q posts (4 posts)
  const { data: contractData, isLoading: contractLoading } = useQuery(
    "postItem",
    {
      params: { limit: 4, type: "contract" },
      staleTime: 5 * 60 * 1000,
    }
  );

  // Transform properties data to match PropertyCard props
  const properties = React.useMemo(() => {
    if (!propertiesData?.data) return [];
    return transformPropertiesData(propertiesData.data.slice(0, 4));
  }, [propertiesData]);

  const sphereItPosts = sphereItData?.data?.slice(0, 4) || [];
  const contractPosts = contractData?.data?.slice(0, 4) || [];

  return (
    <InnerLayout>
      <div className="container-fluid mt-4">
        <div className="row mt-5">
          <div className="col-12 w-100">
            <img
              src="/assets/img/banner-img.png"
              alt=""
              className="img-fluid w-100"
            />
          </div>
        </div>

        {/* Recent Posts (Sphere It) */}
        <div className="row">
          <div className="col-12">
            <div className="d-flex align-items-center justify-content-between">
              <div>
                <h4 className="mt-4 mb-3">Recent Posts</h4>
              </div>
              <div>
                <Link to="/sphare-it" className="font-18">
                  See More
                </Link>
              </div>
            </div>
          </div>

          {sphereItLoading
            ? Array.from({ length: 4 }).map((_, index) => (
                <div
                  key={index}
                  className="col-12 col-md-6 col-lg-4 col-xl-3 mb-4"
                >
                  <div className="card">
                    <div className="card-body">
                      <div className="d-flex align-items-center mb-3">
                        <Skeleton.Avatar size={40} />
                        <div className="ms-3 flex-grow-1">
                          <Skeleton.Input style={{ width: 120, height: 16 }} />
                          <div className="mt-1">
                            <Skeleton.Input style={{ width: 80, height: 12 }} />
                          </div>
                        </div>
                      </div>

                      <Skeleton.Image
                        active
                        className="w-100 text-center align-items-center d-flex mb-2"
                        style={{ width: "100%", height: 200, display: "block" }}
                      />

                      <Skeleton
                        paragraph={{ rows: 2, width: ["100%", "80%"] }}
                      />
                      <div className="d-flex justify-content-between align-items-center mt-3">
                        <div className="d-flex gap-3">
                          <Skeleton.Input style={{ width: 60, height: 20 }} />
                          <Skeleton.Input style={{ width: 60, height: 20 }} />
                          <Skeleton.Input style={{ width: 60, height: 20 }} />
                        </div>
                        <Skeleton.Input style={{ width: 40, height: 20 }} />
                      </div>
                    </div>
                  </div>
                </div>
              ))
            : sphereItPosts.map((post) => (
                <div
                  key={post.id}
                  className="col-12 col-md-6 col-lg-4 col-xl-3 mb-4"
                >
                  <SpherePost
                    {...post}
                    onClick={() => navigate(`/sphare-it/${post.id}`)}
                  />
                </div>
              ))}
        </div>

        {/* Recent Properties */}
        <div className="row">
          <div className="col-12">
            <div className="d-flex align-items-center justify-content-between">
              <div>
                <h4 className="mt-4 mb-3">Recent Properties</h4>
              </div>
              <div>
                <Link to="/listing" className="font-18">
                  See More
                </Link>
              </div>
            </div>
          </div>

          {propertiesLoading
            ? Array.from({ length: 4 }).map((_, index) => (
                <div key={index} className="col-12 col-sm-6 col-md-4 col-lg-3">
                  <div className="card">
                    <div className="card-body">
                      <div className="d-flex align-items-center mb-3">
                        <Skeleton.Avatar size={40} />
                        <div className="ms-3 flex-grow-1">
                          <Skeleton.Input style={{ width: 120, height: 16 }} />
                          <div className="mt-1">
                            <Skeleton.Input style={{ width: 80, height: 12 }} />
                          </div>
                        </div>
                      </div>

                      <Skeleton.Image
                        active
                        className="w-100 text-center align-items-center d-flex mb-2"
                        style={{ width: "100%", height: 200, display: "block" }}
                      />

                      <Skeleton
                        paragraph={{ rows: 2, width: ["100%", "80%"] }}
                      />
                      <div className="d-flex justify-content-between align-items-center mt-3">
                        <div className="d-flex gap-3">
                          <Skeleton.Input style={{ width: 60, height: 20 }} />
                          <Skeleton.Input style={{ width: 60, height: 20 }} />
                          <Skeleton.Input style={{ width: 60, height: 20 }} />
                        </div>
                        <Skeleton.Input style={{ width: 40, height: 20 }} />
                      </div>
                    </div>
                  </div>
                </div>
              ))
            : properties.map((property, index) => (
                <div
                  key={property.id || index}
                  className="col-12 col-sm-6 col-md-4 col-lg-3"
                >
                  <PropertyCard {...property} />
                </div>
              ))}
        </div>

        {/* Recent Questions (Contract Q) */}
        <div className="row">
          <div className="col-12">
            <div className="d-flex align-items-center justify-content-between">
              <div>
                <h4 className="mt-4 mb-3">Recent Questions</h4>
              </div>
              <div>
                <Link to="/contract" className="font-18">
                  See More
                </Link>
              </div>
            </div>
          </div>

          {contractLoading
            ? Array.from({ length: 4 }).map((_, index) => (
                <div
                  key={index}
                  className="col-12 col-md-6 col-lg-4 col-xl-3 mb-4"
                >
                  <div className="card">
                    <div className="card-body">
                      <div className="d-flex align-items-center mb-3">
                        <Skeleton.Avatar size={40} />
                        <div className="ms-3 flex-grow-1">
                          <Skeleton.Input style={{ width: 120, height: 16 }} />
                          <div className="mt-1">
                            <Skeleton.Input style={{ width: 80, height: 12 }} />
                          </div>
                        </div>
                      </div>

                      <Skeleton.Image
                        active
                        className="w-100 text-center align-items-center d-flex mb-2"
                        style={{ width: "100%", height: 200, display: "block" }}
                      />

                      <Skeleton
                        paragraph={{ rows: 2, width: ["100%", "80%"] }}
                      />
                      <div className="d-flex justify-content-between align-items-center mt-3">
                        <div className="d-flex gap-3">
                          <Skeleton.Input style={{ width: 60, height: 20 }} />
                          <Skeleton.Input style={{ width: 60, height: 20 }} />
                          <Skeleton.Input style={{ width: 60, height: 20 }} />
                        </div>
                        <Skeleton.Input style={{ width: 40, height: 20 }} />
                      </div>
                    </div>
                  </div>
                </div>
              ))
            : contractPosts.map((post) => (
                <div
                  key={post.id}
                  className="col-12 col-md-6 col-lg-4 col-xl-3 mb-4"
                >
                  <ContractPost
                    {...post}
                    onClick={() => navigate(`/contract/${post.id}`)}
                  />
                </div>
              ))}
        </div>

        <div className="row mt-4 mb-5">
          <div className="col-12 w-100">
            <img
              src="/assets/img/home-img.png"
              alt=""
              className="img-fluid w-100"
            />
          </div>
        </div>
      </div>
    </InnerLayout>
  );
};

export default memo(Home);
