import { useState, useEffect, useCallback } from "react";
import { message } from "antd";
import { useMutation } from "@/hooks/reactQuery";

export const useContractPostCreation = (
  editPost = null,
  onEditComplete = null
) => {
  const [content, setContent] = useState("");
  const [selectedFile, setSelectedFile] = useState(null);
  const [filePreview, setFilePreview] = useState(null);
  const [fileType, setFileType] = useState(null);
  const [questionId, setQuestionId] = useState(null);

  const isEditMode = !!editPost;

  const { mutate: createPost, isPending: isCreatingPost } = useMutation(
    "addPost",
    {
      useFormData: true,
      showSuccessNotification: false,
      invalidateQueries: [
        { queryKey: ["postItem"], type: "paginated" },
        { queryKey: ["postItem"], type: "all" },
        { queryKey: ["getComments"], type: "all" },
      ],
      onSuccess: () => {
        setContent("");
        setSelectedFile(null);
        if (filePreview) {
          URL.revokeObjectURL(filePreview);
        }
        setFilePreview(null);
        setFileType(null);
        setQuestionId(null);
      },
    }
  );

  const { mutate: updatePost, isPending: isUpdatingPost } = useMutation(
    "updatePost",
    {
      useFormData: true,
      showSuccessNotification: true,
      invalidateQueries: [
        { queryKey: ["postItem"], type: "paginated" },
        { queryKey: ["postItem"], type: "all" },
        { queryKey: ["getComments"], type: "all" },
      ],
      onSuccess: () => {
        setContent("");
        setSelectedFile(null);
        if (filePreview) {
          URL.revokeObjectURL(filePreview);
        }
        setFilePreview(null);
        setFileType(null);
        setQuestionId(null);
        if (onEditComplete) {
          onEditComplete();
        }
      },
    }
  );

  const isProcessing = isCreatingPost || isUpdatingPost;

  const resetForm = () => {
    setContent("");
    setSelectedFile(null);
    if (filePreview) {
      URL.revokeObjectURL(filePreview);
    }
    setFilePreview(null);
    setFileType(null);
    setQuestionId(null);
  };

  const handleFileSelect = (file, type) => {
    const isValidImage = type === "image" && file.type.startsWith("image/");
    const isValidVideo = type === "video" && file.type.startsWith("video/");

    if (!isValidImage && !isValidVideo) {
      message.error(`Please select a valid ${type} file`);
      return false;
    }

    const maxSize = 5;
    const isValidSize = file.size / 1024 / 1024 < maxSize;

    if (!isValidSize) {
      message.error(
        `${
          type === "image" ? "Image" : "Video"
        } must be smaller than ${maxSize}MB`
      );
      return false;
    }

    if (filePreview) {
      URL.revokeObjectURL(filePreview);
    }

    setSelectedFile(file);
    setFileType(type);
    const preview = URL.createObjectURL(file);
    setFilePreview(preview);

    return false;
  };

  const handleImageUpload = ({ file }) => {
    return handleFileSelect(file, "image");
  };

  const handleVideoUpload = ({ file }) => {
    return handleFileSelect(file, "video");
  };

  const removeFile = () => {
    if (filePreview) {
      URL.revokeObjectURL(filePreview);
    }
    setSelectedFile(null);
    setFilePreview(null);
    setFileType(null);
  };

  const handleSubmit = () => {
    if (!content.trim()) {
      message.error("Please enter some content for your post");
      return;
    }

    const payload = {
      content: content.trim(),
      is_anonymous: true,
    };

    // Include question_id if it's selected
    if (questionId) {
      payload.question_id = questionId;
    }

    if (selectedFile) {
      if (fileType === "image") {
        payload.image = selectedFile;
      } else if (fileType === "video") {
        payload.video = selectedFile;
      }
    }

    if (isEditMode) {
      updatePost({ slug: editPost.id, data: payload });
    } else {
      createPost(payload);
    }
  };

  useEffect(() => {
    if (editPost) {
      setContent(editPost.content || "");
      setQuestionId(editPost.question_id || null);

      if (editPost.image) {
        setFileType("image");
        setFilePreview(editPost.image);
        setSelectedFile(null);
      } else if (editPost.video) {
        setFileType("video");
        setFilePreview(editPost.video);
        setSelectedFile(null);
      }
    }
  }, [editPost]);

  useEffect(() => {
    return () => {
      if (filePreview) {
        URL.revokeObjectURL(filePreview);
      }
    };
  }, [filePreview]);

  const isFormValid = content.trim().length > 0;

  return {
    content,
    selectedFile,
    filePreview,
    fileType,
    questionId,
    isCreatingPost: isProcessing,
    isFormValid,
    isEditMode,
    setContent,
    setQuestionId,
    handleImageUpload,
    handleVideoUpload,
    removeFile,
    handleSubmit,
    resetForm,
  };
};

export default useContractPostCreation;
