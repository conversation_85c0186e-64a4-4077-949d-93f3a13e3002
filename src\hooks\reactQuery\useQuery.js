import { useState, useMemo } from "react";
import { useQuery as useReactQuery } from "@tanstack/react-query";
import apiClient from "@/services/apiClient";
import api from "@/services/api";

/**
 * Simple hook for data fetching with React Query
 *
 * @param {string} endpoint - API endpoint key from api.js
 * @param {Object} options - Query options
 * @returns {Object} - Query result object
 */
export function useQuery(endpoint, options = {}) {
  // Extract options with defaults
  const {
    params,
    slug,
    showSuccessNotification = false,
    token,
    enabled,
    ...queryOptions
  } = options;

  // Get API endpoint details
  const apiEndpoint = api[endpoint];
  if (!apiEndpoint) {
    throw new Error(`API endpoint "${endpoint}" not found`);
  }

  // Stable query key generation to prevent unnecessary re-renders
  const queryKey = useMemo(() => {
    const key = [endpoint];
    if (slug) key.push(slug);
    if (params && Object.keys(params).length > 0) {
      // Create stable key from params - sort keys to ensure consistency
      const sortedParams = Object.keys(params)
        .sort()
        .reduce((acc, key) => {
          acc[key] = params[key];
          return acc;
        }, {});
      key.push(JSON.stringify(sortedParams));
    }
    return key;
  }, [endpoint, slug, params]);

  // Stable query function to prevent unnecessary re-executions
  const queryFn = useMemo(() => {
    return async () => {
      try {
        // Check if this is a POST request and showSuccessNotification is enabled
        const isPostRequest = apiEndpoint.method === "POST";
        const shouldShowNotification = isPostRequest && showSuccessNotification;

        const { data, pagination } = await apiClient.request(endpoint, {
          params,
          slug,
          showSuccessNotification: shouldShowNotification,
          token,
        });
        return { data, pagination };
      } catch (error) {
        throw error;
      }
    };
  }, [endpoint, slug, params, showSuccessNotification, apiEndpoint.method]);

  // Ensure enabled is always a boolean
  const isEnabled = useMemo(() => {
    if (enabled === undefined) return true;
    if (typeof enabled === 'function') return enabled;
    return Boolean(enabled);
  }, [enabled]);

  // Use React Query with React 19 optimizations
  return useReactQuery({
    queryKey,
    queryFn,
    // React 19 performance optimizations
    select: queryOptions.select, // Enable data transformation
    enabled: isEnabled, // Ensure boolean or function
    placeholderData: queryOptions.placeholderData, // Support for placeholder data
    // Make sure all options are properly passed through
    ...queryOptions,
  });
}

/**
 * Hook for paginated queries
 *
 * @param {string} endpoint - API endpoint key from api.js
 * @param {Object} options - Query options
 * @returns {Object} - Query result object with pagination helpers
 */
export function usePaginatedQuery(endpoint, options = {}) {
  const [page, setPage] = useState(options.initialPage || 1);
  const [pageSize, setPageSize] = useState(options.initialPageSize || 10);

  // Add pagination params (exclude page and limit for agent search and property listing search)
  const params = {
    ...(options.params || {}),
  };
  
  // Only add page and limit if not agent search or property listing search
  if (endpoint !== "getUser" && endpoint !== "properties") {
    params.page = page;
    params.limit = pageSize;
  }

  // Use the base query hook with pagination params and React 19 optimizations
  const query = useQuery(endpoint, {
    ...options,
    params,
    // React 19 and performance optimizations for pagination
    placeholderData: options.keepPreviousData
      ? (previousData) => previousData
      : undefined, // Only keep previous data if explicitly requested
    refetchOnWindowFocus: false,
    staleTime: 0,
    gcTime: 0,
    // Optimize for pagination performance
    select: options.select, // Allow data transformation
    notifyOnChangeProps: ["data", "error", "isLoading"], // Only re-render on specific prop changes
    // Pass through showSuccessNotification option
    showSuccessNotification: options.showSuccessNotification,
  });

  // Add pagination helpers
  return {
    ...query,
    page,
    pageSize,
    setPage,
    setPageSize,
    nextPage: () => setPage((p) => p + 1),
    prevPage: () => setPage((p) => Math.max(1, p - 1)),
    goToPage: (newPage) => setPage(newPage),
    pagination: query.data?.pagination || window.globalPagination || {}, // Provide pagination data
  };
}

export default useQuery;