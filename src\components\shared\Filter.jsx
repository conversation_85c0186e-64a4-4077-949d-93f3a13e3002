import React, { useState } from "react";
import { Modal, Form, Button, Space, Slider } from "antd";
import { FilterOutlined } from "@ant-design/icons";
import BaseInput from "./inputs";
import { useFilter } from "@/store/FilterContext";
import useLocationData from "@/hooks/useLocationData";
import useStartupData from "@/hooks/reactQuery/useStartupData";
import { getLanguageOptions } from "@/utils/languageUtils";

const Filter = ({ fields = [] }) => {
  const {
    filters,
    updateFilters,
    clearFilters,
    isFilterModalOpen,
    setIsFilterModalOpen,
  } = useFilter();
  const [form] = Form.useForm();

  const { data: startupResponse } = useStartupData();
  const startupData = startupResponse?.data;

  const {
    selectedState,
    stateOptions,
    cityOptions,
    statesLoading,
    citiesLoading,
    handleStateChange,
    updateSelectedState,
  } = useLocationData();

  const languageOptions = React.useMemo(() => {
    return getLanguageOptions(startupData?.languages);
  }, [startupData?.languages]);

  const currentYear = new Date().getFullYear();

  const sliderConfigs = {
    property_size: {
      min: 0,
      max: 25000,
      step: 1,
      defaultValue: [0, 5000],
      formatter: (value) => `${value}`,
    },
    lot_size: {
      min: 0,
      max: 20000,
      step: 1,
      defaultValue: [0, 10000],
      formatter: (value) => `${value} Sq.ft`,
    },
    price: {
      min: 0,
      max: 25000,
      step: 1,
      defaultValue: [0, 15000],
      formatter: (value) => `$${value.toLocaleString()}`,
    },
    year_built: {
      min: 1900,
      max: currentYear,
      step: 1,
      defaultValue: [1900, currentYear],
      formatter: (value) => value.toString(),
    },
  };

  const [sliderValues, setSliderValues] = useState({
    property_size: sliderConfigs.property_size.defaultValue,
    lot_size: sliderConfigs.lot_size.defaultValue,
    price: sliderConfigs.price.defaultValue,
    year_built: sliderConfigs.year_built.defaultValue,
  });

  React.useEffect(() => {
    const formValues = {
      ...filters,
      min_size: filters.min_size ?? sliderConfigs.property_size.defaultValue[0],
      max_size: filters.max_size ?? sliderConfigs.property_size.defaultValue[1],
      min_lot_size:
        filters.min_lot_size ?? sliderConfigs.lot_size.defaultValue[0],
      max_lot_size:
        filters.max_lot_size ?? sliderConfigs.lot_size.defaultValue[1],
      min_price: filters.min_price ?? sliderConfigs.price.defaultValue[0],
      max_price: filters.max_price ?? sliderConfigs.price.defaultValue[1],
      min_year_built:
        filters.min_year_built ?? sliderConfigs.year_built.defaultValue[0],
      max_year_built:
        filters.max_year_built ?? sliderConfigs.year_built.defaultValue[1],
    };

    form.setFieldsValue(formValues);

    setSliderValues({
      property_size: [formValues.min_size, formValues.max_size],
      lot_size: [formValues.min_lot_size, formValues.max_lot_size],
      price: [formValues.min_price, formValues.max_price],
      year_built: [formValues.min_year_built, formValues.max_year_built],
    });

    if (filters.state) {
      updateSelectedState(filters.state);
    } else {
      updateSelectedState("");
    }
  }, [filters, form, updateSelectedState]);

  const handleApplyFilters = () => {
    const values = form.getFieldsValue();

    const cleanedValues = Object.entries(values).reduce((acc, [key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        acc[key] = value;
      }
      return acc;
    }, {});

    // Transform max_bed and max_bath to min_bed and min_bath
    if (cleanedValues.max_bed) {
      cleanedValues.min_bed = cleanedValues.max_bed;
      delete cleanedValues.max_bed;
    }

    if (cleanedValues.max_bath) {
      cleanedValues.min_bath = cleanedValues.max_bath;
      delete cleanedValues.max_bath;
    }

    cleanedValues.min_garage = 0;

    if (cleanedValues.size_type) {
      cleanedValues.lot_size_type = cleanedValues.size_type;
    }

    updateFilters(cleanedValues);
    setIsFilterModalOpen(false);
  };

  const handleResetFilters = () => {
    form.resetFields();
    clearFilters();

    setSliderValues({
      property_size: sliderConfigs.property_size.defaultValue,
      lot_size: sliderConfigs.lot_size.defaultValue,
      price: sliderConfigs.price.defaultValue,
      year_built: sliderConfigs.year_built.defaultValue,
    });

    updateSelectedState("");
    setIsFilterModalOpen(false);
  };

  const onStateChange = (value) => {
    handleStateChange(value, form);
  };

  const currentSizeType = Form.useWatch("size_type", form) || "sqft";
  const sizeTypeLabel =
    currentSizeType === "sqft"
      ? "Sq.ft"
      : currentSizeType === "sq.m"
      ? "Sq.M"
      : currentSizeType === "sq.yd"
      ? "Sq.Yd"
      : currentSizeType === "acres"
      ? "Acres"
      : "Sq.ft";

  const renderSliderField = (type, minField, maxField, label) => {
    const config = sliderConfigs[type];
    const currentValues = sliderValues[type];

    const formatter =
      type === "property_size"
        ? (value) => `${value} ${sizeTypeLabel}`
        : config.formatter;

    return (
      <div key={`${minField}_${maxField}`} className="col-12 col-md-6 mb-3">
        <div className="form-item-label">
          <label>{label}</label>
        </div>
        <Slider
          range
          min={config.min}
          max={config.max}
          step={config.step}
          value={currentValues}
          onChange={(values) => {
            setSliderValues((prev) => ({
              ...prev,
              [type]: values,
            }));

            form.setFieldsValue({
              [minField]: values[0],
              [maxField]: values[1],
            });
          }}
          tooltip={{
            formatter: formatter,
          }}
        />
        <div className="d-flex justify-content-between mt-2">
          <span>{formatter(currentValues[0])}</span>
          <span>{formatter(currentValues[1])}</span>
        </div>

        <Form.Item name={minField} hidden>
          <input />
        </Form.Item>
        <Form.Item name={maxField} hidden>
          <input />
        </Form.Item>
      </div>
    );
  };

  const renderField = (field) => {
    const { name, label, type, placeholder, options, ...fieldProps } = field;

    if (name === "state") {
      return (
        <BaseInput
          key={name}
          name={name}
          label={label}
          type="select"
          placeholder={placeholder}
          options={stateOptions}
          loading={statesLoading}
          handlechange={onStateChange}
          showSearch={true}
          {...fieldProps}
        />
      );
    }

    if (name === "city") {
      return (
        <BaseInput
          key={name}
          name={name}
          label={label}
          type="select"
          placeholder={placeholder}
          options={cityOptions}
          disabled={!selectedState}
          loading={citiesLoading}
          showSearch={true}
          {...fieldProps}
        />
      );
    }

    if (name === "professional_type") {
      return (
        <BaseInput
          key={name}
          name={name}
          label={label}
          type="radio"
          options={[
            {
              value: "broker",
              label: "Real Estate Broker",
            },
            {
              value: "lender",
              label: "Lender/ mtg Broker",
            },
            {
              value: "commercial",
              label: "Commercial Agent",
            },
          ]}
          {...fieldProps}
        />
      );
    }

    if (name === "languages") {
      return (
        <BaseInput
          key={name}
          name={name}
          label={label}
          type="select"
          placeholder={placeholder}
          mode="multiple"
          options={languageOptions}
          {...fieldProps}
        />
      );
    }

    if (name === "multi_state_license") {
      return (
        <BaseInput
          key={name}
          name={name}
          label={label}
          type="radio"
          options={[
            { value: true, label: "Yes" },
            { value: false, label: "No" },
          ]}
          {...fieldProps}
        />
      );
    }

    switch (type) {
      case "select":
        return (
          <BaseInput
            key={name}
            name={name}
            label={label}
            type="select"
            placeholder={placeholder}
            options={options}
            loading={fieldProps.loading}
            {...fieldProps}
          />
        );
      case "radio":
        return (
          <BaseInput
            key={name}
            name={name}
            label={label}
            type="radio"
            options={options}
            {...fieldProps}
          />
        );
      case "datepiker":
        return (
          <BaseInput
            key={name}
            name={name}
            label={label}
            type="datepicker"
            placeholder={placeholder}
            {...fieldProps}
          />
        );
      case "input":
      default:
        return (
          <BaseInput
            key={name}
            name={name}
            label={label}
            placeholder={placeholder}
            type={type === "input" ? "number" : undefined}
            {...fieldProps}
          />
        );
    }
  };

  const orderedFieldNames = [
    "type_id",
    "home_style_id",
    "state",
    "city",
    "zip",
    "basement",
    "max_bed",
    "max_bath",
    "max_garage",
    "size_type",
  ];

  const orderedFields = orderedFieldNames
    .map((fieldName) => fields.find((field) => field.name === fieldName))
    .filter(Boolean);

  return (
    <>
      <Button
        icon={<FilterOutlined />}
        onClick={() => setIsFilterModalOpen(true)}
        type="default"
        className="d-none"
      >
        Filter
      </Button>

      <Modal
        title="Filter Posts"
        open={isFilterModalOpen}
        onCancel={() => setIsFilterModalOpen(false)}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleApplyFilters}
          preserve={false}
        >
          <div className="row">
            {fields.map((field) => (
              <div key={field.name} className="col-12 col-md-6 mb-3">
                {renderField(field)}
              </div>
            ))}
          </div>

          <div className="d-flex justify-content-end gap-2 mt-4">
            <Space>
              <Button onClick={handleResetFilters}>Reset</Button>
              <Button type="primary" htmlType="submit">
                Apply Filters
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </>
  );
};

export default Filter;
