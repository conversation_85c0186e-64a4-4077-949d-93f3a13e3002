import React, { useState, useEffect, useMemo } from "react";
import { usePaginatedQuery } from "@/hooks/reactQuery";
import { useScopedSearch } from "@/store/ScopedSearchContext";
import { useFilter } from "@/store/FilterContext";
import { debounce } from "lodash";

export const useSearchFilterPagination = (endpoint, options = {}) => {
  const { searchKeyword } = useScopedSearch();
  const { filters, setIsFilterModalOpen } = useFilter();
  const [debouncedSearchKeyword, setDebouncedSearchKeyword] =
    useState(searchKeyword);

  const debouncedSetSearch = useMemo(
    () =>
      debounce((keyword) => {
        setDebouncedSearchKeyword(keyword);
      }, 500),
    []
  );

  useEffect(() => {
    debouncedSetSearch(searchKeyword);

    return () => debouncedSetSearch.cancel();
  }, [searchKeyword, debouncedSetSearch]);

  const apiParams = React.useMemo(() => {
    const params = { ...filters };

    if (options.defaultParams) {
      Object.assign(params, options.defaultParams);
    }

    if (debouncedSearchKeyword && debouncedSearchKeyword.trim()) {
      if (endpoint === "getUser") {
        params.name = debouncedSearchKeyword.trim();
      } else {
        params.keyword = debouncedSearchKeyword.trim();
      }
    }

    if (filters.languages && Array.isArray(filters.languages)) {
      delete params.languages;

      filters.languages.forEach((languageId, index) => {
        params[`languages[${index}]`] = languageId;
      });
    }

    Object.keys(params).forEach((key) => {
      if (
        params[key] === undefined ||
        params[key] === null ||
        params[key] === ""
      ) {
        delete params[key];
      }
    });

    return params;
  }, [debouncedSearchKeyword, filters, endpoint, options.defaultParams]);

  const queryResult = usePaginatedQuery(endpoint, {
    params: apiParams,
    initialPage: 1,
    initialPageSize: options.pageSize || 10,
    staleTime: 0,
    gcTime: 0,
    keepPreviousData: true,
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    ...options,
  });

  const handlePageChange = (newPage, newPageSize) => {
    if (newPageSize !== queryResult.pageSize) {
      queryResult.setPageSize(newPageSize);
      queryResult.setPage(1);
    } else {
      queryResult.setPage(newPage);
    }
  };

  const handleFilterClick = () => {
    setIsFilterModalOpen(true);
  };

  return {
    ...queryResult,
    handlePageChange,
    handleFilterClick,
    searchKeyword,
    debouncedSearchKeyword,
    filters,
    apiParams,
  };
};

export default useSearchFilterPagination;