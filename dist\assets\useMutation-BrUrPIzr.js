var Kn=t=>{throw TypeError(t)};var hn=(t,r,e)=>r.has(t)||Kn("Cannot "+e);var se=(t,r,e)=>(hn(t,r,"read from private field"),e?e.call(t):r.get(t)),jt=(t,r,e)=>r.has(t)?Kn("Cannot add the same private member more than once"):r instanceof WeakSet?r.add(t):r.set(t,e),Wt=(t,r,e,n)=>(hn(t,r,"write to private field"),n?n.call(t,e):r.set(t,e),e),qt=(t,r,e)=>(hn(t,r,"access private method"),e);import{r as f,av as zn,aw as jn,ax as Yt,az as er,b8 as Wn,b4 as Sa,aT as qn,w as S,bq as gn,E as $e,v as Ie,a9 as Gn,z as q,C as at,aX as Zn,br as ui,as as X,ab as Wr,D as j,B as Me,bs as xa,bt as En,A as Ma,bu as ci,I as Ft,bv as fi,bw as $a,bx as di,y as C,ay as K,at as pr,au as ot,by as fr,b5 as Vt,b6 as yt,G as Qn,t as it,R as qr,ai as vi,a4 as Va,x as Re,M as mi,a3 as _a,J as hi,bz as gi,bA as Pn,bB as On,W as pi,a1 as Na,Q as hr,N as yi,O as ka,P as bi,T as wi,b7 as Ci,bC as Fi,af as Ei,a6 as Pi,ag as Oi,aD as Ri,aG as Si,bD as Jn,bE as xi,aP as Ia,o as Ta,aI as Mi,aQ as $i,aR as Vi,p as _i}from"./index-Dklazue-.js";import{t as Aa,P as Hr,o as Ni,N as ki,i as Ii}from"./button-DNhBCuue.js";var Rn=f.createContext(null);function Ti(t){var r=t.children,e=t.onBatchResize,n=f.useRef(0),a=f.useRef([]),i=f.useContext(Rn),o=f.useCallback(function(s,l,c){n.current+=1;var u=n.current;a.current.push({size:s,element:l,data:c}),Promise.resolve().then(function(){u===n.current&&(e==null||e(a.current),a.current=[])}),i==null||i(s,l,c)},[e,i]);return f.createElement(Rn.Provider,{value:o},r)}var Da=function(){if(typeof Map<"u")return Map;function t(r,e){var n=-1;return r.some(function(a,i){return a[0]===e?(n=i,!0):!1}),n}return function(){function r(){this.__entries__=[]}return Object.defineProperty(r.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),r.prototype.get=function(e){var n=t(this.__entries__,e),a=this.__entries__[n];return a&&a[1]},r.prototype.set=function(e,n){var a=t(this.__entries__,e);~a?this.__entries__[a][1]=n:this.__entries__.push([e,n])},r.prototype.delete=function(e){var n=this.__entries__,a=t(n,e);~a&&n.splice(a,1)},r.prototype.has=function(e){return!!~t(this.__entries__,e)},r.prototype.clear=function(){this.__entries__.splice(0)},r.prototype.forEach=function(e,n){n===void 0&&(n=null);for(var a=0,i=this.__entries__;a<i.length;a++){var o=i[a];e.call(n,o[1],o[0])}},r}()}(),Sn=typeof window<"u"&&typeof document<"u"&&window.document===document,Ur=function(){return typeof global<"u"&&global.Math===Math?global:typeof self<"u"&&self.Math===Math?self:typeof window<"u"&&window.Math===Math?window:Function("return this")()}(),Ai=function(){return typeof requestAnimationFrame=="function"?requestAnimationFrame.bind(Ur):function(t){return setTimeout(function(){return t(Date.now())},1e3/60)}}(),Di=2;function Li(t,r){var e=!1,n=!1,a=0;function i(){e&&(e=!1,t()),n&&s()}function o(){Ai(i)}function s(){var l=Date.now();if(e){if(l-a<Di)return;n=!0}else e=!0,n=!1,setTimeout(o,r);a=l}return s}var zi=20,ji=["top","right","bottom","left","width","height","size","weight"],Wi=typeof MutationObserver<"u",qi=function(){function t(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=Li(this.refresh.bind(this),zi)}return t.prototype.addObserver=function(r){~this.observers_.indexOf(r)||this.observers_.push(r),this.connected_||this.connect_()},t.prototype.removeObserver=function(r){var e=this.observers_,n=e.indexOf(r);~n&&e.splice(n,1),!e.length&&this.connected_&&this.disconnect_()},t.prototype.refresh=function(){var r=this.updateObservers_();r&&this.refresh()},t.prototype.updateObservers_=function(){var r=this.observers_.filter(function(e){return e.gatherActive(),e.hasActive()});return r.forEach(function(e){return e.broadcastActive()}),r.length>0},t.prototype.connect_=function(){!Sn||this.connected_||(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),Wi?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},t.prototype.disconnect_=function(){!Sn||!this.connected_||(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},t.prototype.onTransitionEnd_=function(r){var e=r.propertyName,n=e===void 0?"":e,a=ji.some(function(i){return!!~n.indexOf(i)});a&&this.refresh()},t.getInstance=function(){return this.instance_||(this.instance_=new t),this.instance_},t.instance_=null,t}(),La=function(t,r){for(var e=0,n=Object.keys(r);e<n.length;e++){var a=n[e];Object.defineProperty(t,a,{value:r[a],enumerable:!1,writable:!1,configurable:!0})}return t},Jt=function(t){var r=t&&t.ownerDocument&&t.ownerDocument.defaultView;return r||Ur},za=Zr(0,0,0,0);function Br(t){return parseFloat(t)||0}function Xn(t){for(var r=[],e=1;e<arguments.length;e++)r[e-1]=arguments[e];return r.reduce(function(n,a){var i=t["border-"+a+"-width"];return n+Br(i)},0)}function Hi(t){for(var r=["top","right","bottom","left"],e={},n=0,a=r;n<a.length;n++){var i=a[n],o=t["padding-"+i];e[i]=Br(o)}return e}function Ui(t){var r=t.getBBox();return Zr(0,0,r.width,r.height)}function Bi(t){var r=t.clientWidth,e=t.clientHeight;if(!r&&!e)return za;var n=Jt(t).getComputedStyle(t),a=Hi(n),i=a.left+a.right,o=a.top+a.bottom,s=Br(n.width),l=Br(n.height);if(n.boxSizing==="border-box"&&(Math.round(s+i)!==r&&(s-=Xn(n,"left","right")+i),Math.round(l+o)!==e&&(l-=Xn(n,"top","bottom")+o)),!Gi(t)){var c=Math.round(s+i)-r,u=Math.round(l+o)-e;Math.abs(c)!==1&&(s-=c),Math.abs(u)!==1&&(l-=u)}return Zr(a.left,a.top,s,l)}var Ki=function(){return typeof SVGGraphicsElement<"u"?function(t){return t instanceof Jt(t).SVGGraphicsElement}:function(t){return t instanceof Jt(t).SVGElement&&typeof t.getBBox=="function"}}();function Gi(t){return t===Jt(t).document.documentElement}function Zi(t){return Sn?Ki(t)?Ui(t):Bi(t):za}function Qi(t){var r=t.x,e=t.y,n=t.width,a=t.height,i=typeof DOMRectReadOnly<"u"?DOMRectReadOnly:Object,o=Object.create(i.prototype);return La(o,{x:r,y:e,width:n,height:a,top:e,right:r+n,bottom:a+e,left:r}),o}function Zr(t,r,e,n){return{x:t,y:r,width:e,height:n}}var Ji=function(){function t(r){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=Zr(0,0,0,0),this.target=r}return t.prototype.isActive=function(){var r=Zi(this.target);return this.contentRect_=r,r.width!==this.broadcastWidth||r.height!==this.broadcastHeight},t.prototype.broadcastRect=function(){var r=this.contentRect_;return this.broadcastWidth=r.width,this.broadcastHeight=r.height,r},t}(),Xi=function(){function t(r,e){var n=Qi(e);La(this,{target:r,contentRect:n})}return t}(),Yi=function(){function t(r,e,n){if(this.activeObservations_=[],this.observations_=new Da,typeof r!="function")throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=r,this.controller_=e,this.callbackCtx_=n}return t.prototype.observe=function(r){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(r instanceof Jt(r).Element))throw new TypeError('parameter 1 is not of type "Element".');var e=this.observations_;e.has(r)||(e.set(r,new Ji(r)),this.controller_.addObserver(this),this.controller_.refresh())}},t.prototype.unobserve=function(r){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(r instanceof Jt(r).Element))throw new TypeError('parameter 1 is not of type "Element".');var e=this.observations_;e.has(r)&&(e.delete(r),e.size||this.controller_.removeObserver(this))}},t.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},t.prototype.gatherActive=function(){var r=this;this.clearActive(),this.observations_.forEach(function(e){e.isActive()&&r.activeObservations_.push(e)})},t.prototype.broadcastActive=function(){if(this.hasActive()){var r=this.callbackCtx_,e=this.activeObservations_.map(function(n){return new Xi(n.target,n.broadcastRect())});this.callback_.call(r,e,r),this.clearActive()}},t.prototype.clearActive=function(){this.activeObservations_.splice(0)},t.prototype.hasActive=function(){return this.activeObservations_.length>0},t}(),ja=typeof WeakMap<"u"?new WeakMap:new Da,Wa=function(){function t(r){if(!(this instanceof t))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var e=qi.getInstance(),n=new Yi(r,e,this);ja.set(this,n)}return t}();["observe","unobserve","disconnect"].forEach(function(t){Wa.prototype[t]=function(){var r;return(r=ja.get(this))[t].apply(r,arguments)}});var eo=function(){return typeof Ur.ResizeObserver<"u"?Ur.ResizeObserver:Wa}(),Ct=new Map;function to(t){t.forEach(function(r){var e,n=r.target;(e=Ct.get(n))===null||e===void 0||e.forEach(function(a){return a(n)})})}var qa=new eo(to);function ro(t,r){Ct.has(t)||(Ct.set(t,new Set),qa.observe(t)),Ct.get(t).add(r)}function no(t,r){Ct.has(t)&&(Ct.get(t).delete(r),Ct.get(t).size||(qa.unobserve(t),Ct.delete(t)))}var ao=function(t){zn(e,t);var r=jn(e);function e(){return Yt(this,e),r.apply(this,arguments)}return er(e,[{key:"render",value:function(){return this.props.children}}]),e}(f.Component);function io(t,r){var e=t.children,n=t.disabled,a=f.useRef(null),i=f.useRef(null),o=f.useContext(Rn),s=typeof e=="function",l=s?e(a):e,c=f.useRef({width:-1,height:-1,offsetWidth:-1,offsetHeight:-1}),u=!s&&f.isValidElement(l)&&Wn(l),d=u?Sa(l):null,m=qn(d,a),b=function(){var y;return gn(a.current)||(a.current&&$e(a.current)==="object"?gn((y=a.current)===null||y===void 0?void 0:y.nativeElement):null)||gn(i.current)};f.useImperativeHandle(r,function(){return b()});var h=f.useRef(t);h.current=t;var g=f.useCallback(function(v){var y=h.current,p=y.onResize,w=y.data,P=v.getBoundingClientRect(),E=P.width,F=P.height,R=v.offsetWidth,M=v.offsetHeight,O=Math.floor(E),x=Math.floor(F);if(c.current.width!==O||c.current.height!==x||c.current.offsetWidth!==R||c.current.offsetHeight!==M){var V={width:O,height:x,offsetWidth:R,offsetHeight:M};c.current=V;var D=R===Math.round(E)?E:R,_=M===Math.round(F)?F:M,k=S(S({},V),{},{offsetWidth:D,offsetHeight:_});o==null||o(k,v,w),p&&Promise.resolve().then(function(){p(k,v)})}},[]);return f.useEffect(function(){var v=b();return v&&!n&&ro(v,g),function(){return no(v,g)}},[a.current,n]),f.createElement(ao,{ref:i},u?f.cloneElement(l,{ref:m}):l)}var oo=f.forwardRef(io),so="rc-observer-key";function lo(t,r){var e=t.children,n=typeof e=="function"?[e]:Aa(e);return n.map(function(a,i){var o=(a==null?void 0:a.key)||"".concat(so,"-").concat(i);return f.createElement(oo,Ie({},t,{key:o,ref:i===0?r:void 0}),a)})}var yr=f.forwardRef(lo);yr.Collection=Ti;function pn(t){return t!==void 0}function Ha(t,r){var e=r||{},n=e.defaultValue,a=e.value,i=e.onChange,o=e.postState,s=Gn(function(){return pn(a)?a:pn(n)?typeof n=="function"?n():n:typeof t=="function"?t():t}),l=q(s,2),c=l[0],u=l[1],d=a!==void 0?a:c,m=o?o(d):d,b=at(i),h=Gn([d]),g=q(h,2),v=g[0],y=g[1];Zn(function(){var w=v[0];c!==w&&b(c,w)},[v]),Zn(function(){pn(a)||u(a)},[a]);var p=at(function(w,P){u(w,P),y([d],P)});return[m,p]}function uo(t,r){return Hr.reduce((e,n)=>{const a=t[`${n}1`],i=t[`${n}3`],o=t[`${n}6`],s=t[`${n}7`];return Object.assign(Object.assign({},e),r(n,{lightColor:a,lightBorderColor:i,darkColor:o,textColor:s}))},{})}const yn=()=>({height:0,opacity:0}),Yn=t=>{const{scrollHeight:r}=t;return{height:r,opacity:1}},co=t=>({height:t?t.offsetHeight:0}),bn=(t,r)=>(r==null?void 0:r.deadline)===!0||r.propertyName==="height",ru=function(){return{motionName:`${arguments.length>0&&arguments[0]!==void 0?arguments[0]:ui}-motion-collapse`,onAppearStart:yn,onEnterStart:yn,onAppearActive:Yn,onEnterActive:Yn,onLeaveStart:co,onLeaveActive:yn,onAppearEnd:bn,onEnterEnd:bn,onLeaveEnd:bn,motionDeadline:500}},fo=(t,r,e)=>e!==void 0?e:`${t}-${r}`,nu=t=>({[t.componentCls]:{[`${t.antCls}-motion-collapse-legacy`]:{overflow:"hidden","&-active":{transition:`height ${t.motionDurationMid} ${t.motionEaseInOut},
        opacity ${t.motionDurationMid} ${t.motionEaseInOut} !important`}},[`${t.antCls}-motion-collapse`]:{overflow:"hidden",transition:`height ${t.motionDurationMid} ${t.motionEaseInOut},
        opacity ${t.motionDurationMid} ${t.motionEaseInOut} !important`}}}),vo=t=>({animationDuration:t,animationFillMode:"both"}),mo=t=>({animationDuration:t,animationFillMode:"both"}),Hn=function(t,r,e,n){const i=(arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1)?"&":"";return{[`
      ${i}${t}-enter,
      ${i}${t}-appear
    `]:Object.assign(Object.assign({},vo(n)),{animationPlayState:"paused"}),[`${i}${t}-leave`]:Object.assign(Object.assign({},mo(n)),{animationPlayState:"paused"}),[`
      ${i}${t}-enter${t}-enter-active,
      ${i}${t}-appear${t}-appear-active
    `]:{animationName:r,animationPlayState:"running"},[`${i}${t}-leave${t}-leave-active`]:{animationName:e,animationPlayState:"running",pointerEvents:"none"}}},ho=new X("antMoveDownIn",{"0%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),go=new X("antMoveDownOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0}}),po=new X("antMoveLeftIn",{"0%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),yo=new X("antMoveLeftOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),bo=new X("antMoveRightIn",{"0%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),wo=new X("antMoveRightOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),Co=new X("antMoveUpIn",{"0%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),Fo=new X("antMoveUpOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0}}),Eo={"move-up":{inKeyframes:Co,outKeyframes:Fo},"move-down":{inKeyframes:ho,outKeyframes:go},"move-left":{inKeyframes:po,outKeyframes:yo},"move-right":{inKeyframes:bo,outKeyframes:wo}},au=(t,r)=>{const{antCls:e}=t,n=`${e}-${r}`,{inKeyframes:a,outKeyframes:i}=Eo[r];return[Hn(n,a,i,t.motionDurationMid),{[`
        ${n}-enter,
        ${n}-appear
      `]:{opacity:0,animationTimingFunction:t.motionEaseOutCirc},[`${n}-leave`]:{animationTimingFunction:t.motionEaseInOutCirc}}]},Po=new X("antSlideUpIn",{"0%":{transform:"scaleY(0.8)",transformOrigin:"0% 0%",opacity:0},"100%":{transform:"scaleY(1)",transformOrigin:"0% 0%",opacity:1}}),Oo=new X("antSlideUpOut",{"0%":{transform:"scaleY(1)",transformOrigin:"0% 0%",opacity:1},"100%":{transform:"scaleY(0.8)",transformOrigin:"0% 0%",opacity:0}}),Ro=new X("antSlideDownIn",{"0%":{transform:"scaleY(0.8)",transformOrigin:"100% 100%",opacity:0},"100%":{transform:"scaleY(1)",transformOrigin:"100% 100%",opacity:1}}),So=new X("antSlideDownOut",{"0%":{transform:"scaleY(1)",transformOrigin:"100% 100%",opacity:1},"100%":{transform:"scaleY(0.8)",transformOrigin:"100% 100%",opacity:0}}),xo=new X("antSlideLeftIn",{"0%":{transform:"scaleX(0.8)",transformOrigin:"0% 0%",opacity:0},"100%":{transform:"scaleX(1)",transformOrigin:"0% 0%",opacity:1}}),Mo=new X("antSlideLeftOut",{"0%":{transform:"scaleX(1)",transformOrigin:"0% 0%",opacity:1},"100%":{transform:"scaleX(0.8)",transformOrigin:"0% 0%",opacity:0}}),$o=new X("antSlideRightIn",{"0%":{transform:"scaleX(0.8)",transformOrigin:"100% 0%",opacity:0},"100%":{transform:"scaleX(1)",transformOrigin:"100% 0%",opacity:1}}),Vo=new X("antSlideRightOut",{"0%":{transform:"scaleX(1)",transformOrigin:"100% 0%",opacity:1},"100%":{transform:"scaleX(0.8)",transformOrigin:"100% 0%",opacity:0}}),_o={"slide-up":{inKeyframes:Po,outKeyframes:Oo},"slide-down":{inKeyframes:Ro,outKeyframes:So},"slide-left":{inKeyframes:xo,outKeyframes:Mo},"slide-right":{inKeyframes:$o,outKeyframes:Vo}},iu=(t,r)=>{const{antCls:e}=t,n=`${e}-${r}`,{inKeyframes:a,outKeyframes:i}=_o[r];return[Hn(n,a,i,t.motionDurationMid),{[`
      ${n}-enter,
      ${n}-appear
    `]:{transform:"scale(0)",transformOrigin:"0% 0%",opacity:0,animationTimingFunction:t.motionEaseOutQuint,"&-prepare":{transform:"scale(1)"}},[`${n}-leave`]:{animationTimingFunction:t.motionEaseInQuint}}]},No=new X("antZoomIn",{"0%":{transform:"scale(0.2)",opacity:0},"100%":{transform:"scale(1)",opacity:1}}),ko=new X("antZoomOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0.2)",opacity:0}}),ea=new X("antZoomBigIn",{"0%":{transform:"scale(0.8)",opacity:0},"100%":{transform:"scale(1)",opacity:1}}),ta=new X("antZoomBigOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0.8)",opacity:0}}),Io=new X("antZoomUpIn",{"0%":{transform:"scale(0.8)",transformOrigin:"50% 0%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"50% 0%"}}),To=new X("antZoomUpOut",{"0%":{transform:"scale(1)",transformOrigin:"50% 0%"},"100%":{transform:"scale(0.8)",transformOrigin:"50% 0%",opacity:0}}),Ao=new X("antZoomLeftIn",{"0%":{transform:"scale(0.8)",transformOrigin:"0% 50%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"0% 50%"}}),Do=new X("antZoomLeftOut",{"0%":{transform:"scale(1)",transformOrigin:"0% 50%"},"100%":{transform:"scale(0.8)",transformOrigin:"0% 50%",opacity:0}}),Lo=new X("antZoomRightIn",{"0%":{transform:"scale(0.8)",transformOrigin:"100% 50%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"100% 50%"}}),zo=new X("antZoomRightOut",{"0%":{transform:"scale(1)",transformOrigin:"100% 50%"},"100%":{transform:"scale(0.8)",transformOrigin:"100% 50%",opacity:0}}),jo=new X("antZoomDownIn",{"0%":{transform:"scale(0.8)",transformOrigin:"50% 100%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"50% 100%"}}),Wo=new X("antZoomDownOut",{"0%":{transform:"scale(1)",transformOrigin:"50% 100%"},"100%":{transform:"scale(0.8)",transformOrigin:"50% 100%",opacity:0}}),qo={zoom:{inKeyframes:No,outKeyframes:ko},"zoom-big":{inKeyframes:ea,outKeyframes:ta},"zoom-big-fast":{inKeyframes:ea,outKeyframes:ta},"zoom-left":{inKeyframes:Ao,outKeyframes:Do},"zoom-right":{inKeyframes:Lo,outKeyframes:zo},"zoom-up":{inKeyframes:Io,outKeyframes:To},"zoom-down":{inKeyframes:jo,outKeyframes:Wo}},Ho=(t,r)=>{const{antCls:e}=t,n=`${e}-${r}`,{inKeyframes:a,outKeyframes:i}=qo[r];return[Hn(n,a,i,r==="zoom-big-fast"?t.motionDurationFast:t.motionDurationMid),{[`
        ${n}-enter,
        ${n}-appear
      `]:{transform:"scale(0)",opacity:0,animationTimingFunction:t.motionEaseOutCirc,"&-prepare":{transform:"none"}},[`${n}-leave`]:{animationTimingFunction:t.motionEaseInOutCirc}}]};var Ua=f.createContext(null),ra=[];function Uo(t,r){var e=f.useState(function(){if(!Wr())return null;var h=document.createElement("div");return h}),n=q(e,1),a=n[0],i=f.useRef(!1),o=f.useContext(Ua),s=f.useState(ra),l=q(s,2),c=l[0],u=l[1],d=o||(i.current?void 0:function(h){u(function(g){var v=[h].concat(j(g));return v})});function m(){a.parentElement||document.body.appendChild(a),i.current=!0}function b(){var h;(h=a.parentElement)===null||h===void 0||h.removeChild(a),i.current=!1}return Me(function(){return t?o?o(m):m():b(),b},[t]),Me(function(){c.length&&(c.forEach(function(h){return h()}),u(ra))},[c]),[a,d]}function Bo(t){var r="rc-scrollbar-measure-".concat(Math.random().toString(36).substring(7)),e=document.createElement("div");e.id=r;var n=e.style;n.position="absolute",n.left="0",n.top="0",n.width="100px",n.height="100px",n.overflow="scroll";var a,i;if(t){var o=getComputedStyle(t);n.scrollbarColor=o.scrollbarColor,n.scrollbarWidth=o.scrollbarWidth;var s=getComputedStyle(t,"::-webkit-scrollbar"),l=parseInt(s.width,10),c=parseInt(s.height,10);try{var u=l?"width: ".concat(s.width,";"):"",d=c?"height: ".concat(s.height,";"):"";xa(`
#`.concat(r,`::-webkit-scrollbar {
`).concat(u,`
`).concat(d,`
}`),r)}catch(h){console.error(h),a=l,i=c}}document.body.appendChild(e);var m=t&&a&&!isNaN(a)?a:e.offsetWidth-e.clientWidth,b=t&&i&&!isNaN(i)?i:e.offsetHeight-e.clientHeight;return document.body.removeChild(e),En(r),{width:m,height:b}}function Ko(t){return typeof document>"u"||!t||!(t instanceof Element)?{width:0,height:0}:Bo(t)}function Go(){return document.body.scrollHeight>(window.innerHeight||document.documentElement.clientHeight)&&window.innerWidth>document.body.offsetWidth}var Zo="rc-util-locker-".concat(Date.now()),na=0;function Qo(t){var r=!!t,e=f.useState(function(){return na+=1,"".concat(Zo,"_").concat(na)}),n=q(e,1),a=n[0];Me(function(){if(r){var i=Ko(document.body).width,o=Go();xa(`
html body {
  overflow-y: hidden;
  `.concat(o?"width: calc(100% - ".concat(i,"px);"):"",`
}`),a)}else En(a);return function(){En(a)}},[r,a])}var Jo=!1;function Xo(t){return Jo}var aa=function(r){return r===!1?!1:!Wr()||!r?null:typeof r=="string"?document.querySelector(r):typeof r=="function"?r():r},Ba=f.forwardRef(function(t,r){var e=t.open,n=t.autoLock,a=t.getContainer;t.debug;var i=t.autoDestroy,o=i===void 0?!0:i,s=t.children,l=f.useState(e),c=q(l,2),u=c[0],d=c[1],m=u||e;f.useEffect(function(){(o||e)&&d(e)},[e,o]);var b=f.useState(function(){return aa(a)}),h=q(b,2),g=h[0],v=h[1];f.useEffect(function(){var V=aa(a);v(V??null)});var y=Uo(m&&!g),p=q(y,2),w=p[0],P=p[1],E=g??w;Qo(n&&e&&Wr()&&(E===w||E===document.body));var F=null;if(s&&Wn(s)&&r){var R=s;F=R.ref}var M=qn(F,r);if(!m||!Wr()||g===void 0)return null;var O=E===!1||Xo(),x=s;return r&&(x=f.cloneElement(s,{ref:M})),f.createElement(Ua.Provider,{value:P},O?x:Ma.createPortal(x,E))});function Yo(){var t=S({},ci);return t.useId}var ia=0,oa=Yo();const Ka=oa?function(r){var e=oa();return r||e}:function(r){var e=f.useState("ssr-id"),n=q(e,2),a=n[0],i=n[1];return f.useEffect(function(){var o=ia;ia+=1,i("rc_unique_".concat(o))},[]),r||a};var _t="RC_FORM_INTERNAL_HOOKS",J=function(){Ft(!1,"Can not find FormContext. Please make sure you wrap Field under Form.")},Xt=f.createContext({getFieldValue:J,getFieldsValue:J,getFieldError:J,getFieldWarning:J,getFieldsError:J,isFieldsTouched:J,isFieldTouched:J,isFieldValidating:J,isFieldsValidating:J,resetFields:J,setFields:J,setFieldValue:J,setFieldsValue:J,validateFields:J,submit:J,getInternalHooks:function(){return J(),{dispatch:J,initEntityValue:J,registerField:J,useSubscribe:J,setInitialValues:J,destroyForm:J,setCallbacks:J,registerWatch:J,getFields:J,setValidateMessages:J,setPreserve:J,getInitialValue:J}}}),Kr=f.createContext(null);function xn(t){return t==null?[]:Array.isArray(t)?t:[t]}function es(t){return t&&!!t._init}function Mn(){return{default:"Validation error on field %s",required:"%s is required",enum:"%s must be one of %s",whitespace:"%s cannot be empty",date:{format:"%s date %s is invalid for format %s",parse:"%s date could not be parsed, %s is invalid ",invalid:"%s date %s is invalid"},types:{string:"%s is not a %s",method:"%s is not a %s (function)",array:"%s is not an %s",object:"%s is not an %s",number:"%s is not a %s",date:"%s is not a %s",boolean:"%s is not a %s",integer:"%s is not an %s",float:"%s is not a %s",regexp:"%s is not a valid %s",email:"%s is not a valid %s",url:"%s is not a valid %s",hex:"%s is not a valid %s"},string:{len:"%s must be exactly %s characters",min:"%s must be at least %s characters",max:"%s cannot be longer than %s characters",range:"%s must be between %s and %s characters"},number:{len:"%s must equal %s",min:"%s cannot be less than %s",max:"%s cannot be greater than %s",range:"%s must be between %s and %s"},array:{len:"%s must be exactly %s in length",min:"%s cannot be less than %s in length",max:"%s cannot be greater than %s in length",range:"%s must be between %s and %s in length"},pattern:{mismatch:"%s value %s does not match pattern %s"},clone:function(){var r=JSON.parse(JSON.stringify(this));return r.clone=this.clone,r}}}var $n=Mn();function ts(t){try{return Function.toString.call(t).indexOf("[native code]")!==-1}catch{return typeof t=="function"}}function rs(t,r,e){if(fi())return Reflect.construct.apply(null,arguments);var n=[null];n.push.apply(n,r);var a=new(t.bind.apply(t,n));return e&&$a(a,e.prototype),a}function Vn(t){var r=typeof Map=="function"?new Map:void 0;return Vn=function(n){if(n===null||!ts(n))return n;if(typeof n!="function")throw new TypeError("Super expression must either be null or a function");if(r!==void 0){if(r.has(n))return r.get(n);r.set(n,a)}function a(){return rs(n,arguments,di(this).constructor)}return a.prototype=Object.create(n.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),$a(a,n)},Vn(t)}var ns=/%[sdj%]/g,as=function(){};function _n(t){if(!t||!t.length)return null;var r={};return t.forEach(function(e){var n=e.field;r[n]=r[n]||[],r[n].push(e)}),r}function qe(t){for(var r=arguments.length,e=new Array(r>1?r-1:0),n=1;n<r;n++)e[n-1]=arguments[n];var a=0,i=e.length;if(typeof t=="function")return t.apply(null,e);if(typeof t=="string"){var o=t.replace(ns,function(s){if(s==="%%")return"%";if(a>=i)return s;switch(s){case"%s":return String(e[a++]);case"%d":return Number(e[a++]);case"%j":try{return JSON.stringify(e[a++])}catch{return"[Circular]"}break;default:return s}});return o}return t}function is(t){return t==="string"||t==="url"||t==="hex"||t==="email"||t==="date"||t==="pattern"}function ye(t,r){return!!(t==null||r==="array"&&Array.isArray(t)&&!t.length||is(r)&&typeof t=="string"&&!t)}function os(t,r,e){var n=[],a=0,i=t.length;function o(s){n.push.apply(n,j(s||[])),a++,a===i&&e(n)}t.forEach(function(s){r(s,o)})}function sa(t,r,e){var n=0,a=t.length;function i(o){if(o&&o.length){e(o);return}var s=n;n=n+1,s<a?r(t[s],i):e([])}i([])}function ss(t){var r=[];return Object.keys(t).forEach(function(e){r.push.apply(r,j(t[e]||[]))}),r}var la=function(t){zn(e,t);var r=jn(e);function e(n,a){var i;return Yt(this,e),i=r.call(this,"Async Validation Error"),C(K(i),"errors",void 0),C(K(i),"fields",void 0),i.errors=n,i.fields=a,i}return er(e)}(Vn(Error));function ls(t,r,e,n,a){if(r.first){var i=new Promise(function(m,b){var h=function(y){return n(y),y.length?b(new la(y,_n(y))):m(a)},g=ss(t);sa(g,e,h)});return i.catch(function(m){return m}),i}var o=r.firstFields===!0?Object.keys(t):r.firstFields||[],s=Object.keys(t),l=s.length,c=0,u=[],d=new Promise(function(m,b){var h=function(v){if(u.push.apply(u,v),c++,c===l)return n(u),u.length?b(new la(u,_n(u))):m(a)};s.length||(n(u),m(a)),s.forEach(function(g){var v=t[g];o.indexOf(g)!==-1?sa(v,e,h):os(v,e,h)})});return d.catch(function(m){return m}),d}function us(t){return!!(t&&t.message!==void 0)}function cs(t,r){for(var e=t,n=0;n<r.length;n++){if(e==null)return e;e=e[r[n]]}return e}function ua(t,r){return function(e){var n;return t.fullFields?n=cs(r,t.fullFields):n=r[e.field||t.fullField],us(e)?(e.field=e.field||t.fullField,e.fieldValue=n,e):{message:typeof e=="function"?e():e,fieldValue:n,field:e.field||t.fullField}}}function ca(t,r){if(r){for(var e in r)if(r.hasOwnProperty(e)){var n=r[e];$e(n)==="object"&&$e(t[e])==="object"?t[e]=S(S({},t[e]),n):t[e]=n}}return t}var Ht="enum",fs=function(r,e,n,a,i){r[Ht]=Array.isArray(r[Ht])?r[Ht]:[],r[Ht].indexOf(e)===-1&&a.push(qe(i.messages[Ht],r.fullField,r[Ht].join(", ")))},ds=function(r,e,n,a,i){if(r.pattern){if(r.pattern instanceof RegExp)r.pattern.lastIndex=0,r.pattern.test(e)||a.push(qe(i.messages.pattern.mismatch,r.fullField,e,r.pattern));else if(typeof r.pattern=="string"){var o=new RegExp(r.pattern);o.test(e)||a.push(qe(i.messages.pattern.mismatch,r.fullField,e,r.pattern))}}},vs=function(r,e,n,a,i){var o=typeof r.len=="number",s=typeof r.min=="number",l=typeof r.max=="number",c=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,u=e,d=null,m=typeof e=="number",b=typeof e=="string",h=Array.isArray(e);if(m?d="number":b?d="string":h&&(d="array"),!d)return!1;h&&(u=e.length),b&&(u=e.replace(c,"_").length),o?u!==r.len&&a.push(qe(i.messages[d].len,r.fullField,r.len)):s&&!l&&u<r.min?a.push(qe(i.messages[d].min,r.fullField,r.min)):l&&!s&&u>r.max?a.push(qe(i.messages[d].max,r.fullField,r.max)):s&&l&&(u<r.min||u>r.max)&&a.push(qe(i.messages[d].range,r.fullField,r.min,r.max))},Ga=function(r,e,n,a,i,o){r.required&&(!n.hasOwnProperty(r.field)||ye(e,o||r.type))&&a.push(qe(i.messages.required,r.fullField))},Lr;const ms=function(){if(Lr)return Lr;var t="[a-fA-F\\d:]",r=function(F){return F&&F.includeBoundaries?"(?:(?<=\\s|^)(?=".concat(t,")|(?<=").concat(t,")(?=\\s|$))"):""},e="(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}",n="[a-fA-F\\d]{1,4}",a=["(?:".concat(n,":){7}(?:").concat(n,"|:)"),"(?:".concat(n,":){6}(?:").concat(e,"|:").concat(n,"|:)"),"(?:".concat(n,":){5}(?::").concat(e,"|(?::").concat(n,"){1,2}|:)"),"(?:".concat(n,":){4}(?:(?::").concat(n,"){0,1}:").concat(e,"|(?::").concat(n,"){1,3}|:)"),"(?:".concat(n,":){3}(?:(?::").concat(n,"){0,2}:").concat(e,"|(?::").concat(n,"){1,4}|:)"),"(?:".concat(n,":){2}(?:(?::").concat(n,"){0,3}:").concat(e,"|(?::").concat(n,"){1,5}|:)"),"(?:".concat(n,":){1}(?:(?::").concat(n,"){0,4}:").concat(e,"|(?::").concat(n,"){1,6}|:)"),"(?::(?:(?::".concat(n,"){0,5}:").concat(e,"|(?::").concat(n,"){1,7}|:))")],i="(?:%[0-9a-zA-Z]{1,})?",o="(?:".concat(a.join("|"),")").concat(i),s=new RegExp("(?:^".concat(e,"$)|(?:^").concat(o,"$)")),l=new RegExp("^".concat(e,"$")),c=new RegExp("^".concat(o,"$")),u=function(F){return F&&F.exact?s:new RegExp("(?:".concat(r(F)).concat(e).concat(r(F),")|(?:").concat(r(F)).concat(o).concat(r(F),")"),"g")};u.v4=function(E){return E&&E.exact?l:new RegExp("".concat(r(E)).concat(e).concat(r(E)),"g")},u.v6=function(E){return E&&E.exact?c:new RegExp("".concat(r(E)).concat(o).concat(r(E)),"g")};var d="(?:(?:[a-z]+:)?//)",m="(?:\\S+(?::\\S*)?@)?",b=u.v4().source,h=u.v6().source,g="(?:(?:[a-z\\u00a1-\\uffff0-9][-_]*)*[a-z\\u00a1-\\uffff0-9]+)",v="(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*",y="(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))",p="(?::\\d{2,5})?",w='(?:[/?#][^\\s"]*)?',P="(?:".concat(d,"|www\\.)").concat(m,"(?:localhost|").concat(b,"|").concat(h,"|").concat(g).concat(v).concat(y,")").concat(p).concat(w);return Lr=new RegExp("(?:^".concat(P,"$)"),"i"),Lr};var fa={email:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+\.)+[a-zA-Z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}))$/,hex:/^#?([a-f0-9]{6}|[a-f0-9]{3})$/i},dr={integer:function(r){return dr.number(r)&&parseInt(r,10)===r},float:function(r){return dr.number(r)&&!dr.integer(r)},array:function(r){return Array.isArray(r)},regexp:function(r){if(r instanceof RegExp)return!0;try{return!!new RegExp(r)}catch{return!1}},date:function(r){return typeof r.getTime=="function"&&typeof r.getMonth=="function"&&typeof r.getYear=="function"&&!isNaN(r.getTime())},number:function(r){return isNaN(r)?!1:typeof r=="number"},object:function(r){return $e(r)==="object"&&!dr.array(r)},method:function(r){return typeof r=="function"},email:function(r){return typeof r=="string"&&r.length<=320&&!!r.match(fa.email)},url:function(r){return typeof r=="string"&&r.length<=2048&&!!r.match(ms())},hex:function(r){return typeof r=="string"&&!!r.match(fa.hex)}},hs=function(r,e,n,a,i){if(r.required&&e===void 0){Ga(r,e,n,a,i);return}var o=["integer","float","array","regexp","object","method","email","number","date","url","hex"],s=r.type;o.indexOf(s)>-1?dr[s](e)||a.push(qe(i.messages.types[s],r.fullField,r.type)):s&&$e(e)!==r.type&&a.push(qe(i.messages.types[s],r.fullField,r.type))},gs=function(r,e,n,a,i){(/^\s+$/.test(e)||e==="")&&a.push(qe(i.messages.whitespace,r.fullField))};const H={required:Ga,whitespace:gs,type:hs,range:vs,enum:fs,pattern:ds};var ps=function(r,e,n,a,i){var o=[],s=r.required||!r.required&&a.hasOwnProperty(r.field);if(s){if(ye(e)&&!r.required)return n();H.required(r,e,a,o,i)}n(o)},ys=function(r,e,n,a,i){var o=[],s=r.required||!r.required&&a.hasOwnProperty(r.field);if(s){if(e==null&&!r.required)return n();H.required(r,e,a,o,i,"array"),e!=null&&(H.type(r,e,a,o,i),H.range(r,e,a,o,i))}n(o)},bs=function(r,e,n,a,i){var o=[],s=r.required||!r.required&&a.hasOwnProperty(r.field);if(s){if(ye(e)&&!r.required)return n();H.required(r,e,a,o,i),e!==void 0&&H.type(r,e,a,o,i)}n(o)},ws=function(r,e,n,a,i){var o=[],s=r.required||!r.required&&a.hasOwnProperty(r.field);if(s){if(ye(e,"date")&&!r.required)return n();if(H.required(r,e,a,o,i),!ye(e,"date")){var l;e instanceof Date?l=e:l=new Date(e),H.type(r,l,a,o,i),l&&H.range(r,l.getTime(),a,o,i)}}n(o)},Cs="enum",Fs=function(r,e,n,a,i){var o=[],s=r.required||!r.required&&a.hasOwnProperty(r.field);if(s){if(ye(e)&&!r.required)return n();H.required(r,e,a,o,i),e!==void 0&&H[Cs](r,e,a,o,i)}n(o)},Es=function(r,e,n,a,i){var o=[],s=r.required||!r.required&&a.hasOwnProperty(r.field);if(s){if(ye(e)&&!r.required)return n();H.required(r,e,a,o,i),e!==void 0&&(H.type(r,e,a,o,i),H.range(r,e,a,o,i))}n(o)},Ps=function(r,e,n,a,i){var o=[],s=r.required||!r.required&&a.hasOwnProperty(r.field);if(s){if(ye(e)&&!r.required)return n();H.required(r,e,a,o,i),e!==void 0&&(H.type(r,e,a,o,i),H.range(r,e,a,o,i))}n(o)},Os=function(r,e,n,a,i){var o=[],s=r.required||!r.required&&a.hasOwnProperty(r.field);if(s){if(ye(e)&&!r.required)return n();H.required(r,e,a,o,i),e!==void 0&&H.type(r,e,a,o,i)}n(o)},Rs=function(r,e,n,a,i){var o=[],s=r.required||!r.required&&a.hasOwnProperty(r.field);if(s){if(e===""&&(e=void 0),ye(e)&&!r.required)return n();H.required(r,e,a,o,i),e!==void 0&&(H.type(r,e,a,o,i),H.range(r,e,a,o,i))}n(o)},Ss=function(r,e,n,a,i){var o=[],s=r.required||!r.required&&a.hasOwnProperty(r.field);if(s){if(ye(e)&&!r.required)return n();H.required(r,e,a,o,i),e!==void 0&&H.type(r,e,a,o,i)}n(o)},xs=function(r,e,n,a,i){var o=[],s=r.required||!r.required&&a.hasOwnProperty(r.field);if(s){if(ye(e,"string")&&!r.required)return n();H.required(r,e,a,o,i),ye(e,"string")||H.pattern(r,e,a,o,i)}n(o)},Ms=function(r,e,n,a,i){var o=[],s=r.required||!r.required&&a.hasOwnProperty(r.field);if(s){if(ye(e)&&!r.required)return n();H.required(r,e,a,o,i),ye(e)||H.type(r,e,a,o,i)}n(o)},$s=function(r,e,n,a,i){var o=[],s=Array.isArray(e)?"array":$e(e);H.required(r,e,a,o,i,s),n(o)},Vs=function(r,e,n,a,i){var o=[],s=r.required||!r.required&&a.hasOwnProperty(r.field);if(s){if(ye(e,"string")&&!r.required)return n();H.required(r,e,a,o,i,"string"),ye(e,"string")||(H.type(r,e,a,o,i),H.range(r,e,a,o,i),H.pattern(r,e,a,o,i),r.whitespace===!0&&H.whitespace(r,e,a,o,i))}n(o)},wn=function(r,e,n,a,i){var o=r.type,s=[],l=r.required||!r.required&&a.hasOwnProperty(r.field);if(l){if(ye(e,o)&&!r.required)return n();H.required(r,e,a,s,i,o),ye(e,o)||H.type(r,e,a,s,i)}n(s)};const vr={string:Vs,method:Os,number:Rs,boolean:bs,regexp:Ms,integer:Ps,float:Es,array:ys,object:Ss,enum:Fs,pattern:xs,date:ws,url:wn,hex:wn,email:wn,required:$s,any:ps};var br=function(){function t(r){Yt(this,t),C(this,"rules",null),C(this,"_messages",$n),this.define(r)}return er(t,[{key:"define",value:function(e){var n=this;if(!e)throw new Error("Cannot configure a schema with no rules");if($e(e)!=="object"||Array.isArray(e))throw new Error("Rules must be an object");this.rules={},Object.keys(e).forEach(function(a){var i=e[a];n.rules[a]=Array.isArray(i)?i:[i]})}},{key:"messages",value:function(e){return e&&(this._messages=ca(Mn(),e)),this._messages}},{key:"validate",value:function(e){var n=this,a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:function(){},o=e,s=a,l=i;if(typeof s=="function"&&(l=s,s={}),!this.rules||Object.keys(this.rules).length===0)return l&&l(null,o),Promise.resolve(o);function c(h){var g=[],v={};function y(w){if(Array.isArray(w)){var P;g=(P=g).concat.apply(P,j(w))}else g.push(w)}for(var p=0;p<h.length;p++)y(h[p]);g.length?(v=_n(g),l(g,v)):l(null,o)}if(s.messages){var u=this.messages();u===$n&&(u=Mn()),ca(u,s.messages),s.messages=u}else s.messages=this.messages();var d={},m=s.keys||Object.keys(this.rules);m.forEach(function(h){var g=n.rules[h],v=o[h];g.forEach(function(y){var p=y;typeof p.transform=="function"&&(o===e&&(o=S({},o)),v=o[h]=p.transform(v),v!=null&&(p.type=p.type||(Array.isArray(v)?"array":$e(v)))),typeof p=="function"?p={validator:p}:p=S({},p),p.validator=n.getValidationMethod(p),p.validator&&(p.field=h,p.fullField=p.fullField||h,p.type=n.getType(p),d[h]=d[h]||[],d[h].push({rule:p,value:v,source:o,field:h}))})});var b={};return ls(d,s,function(h,g){var v=h.rule,y=(v.type==="object"||v.type==="array")&&($e(v.fields)==="object"||$e(v.defaultField)==="object");y=y&&(v.required||!v.required&&h.value),v.field=h.field;function p(R,M){return S(S({},M),{},{fullField:"".concat(v.fullField,".").concat(R),fullFields:v.fullFields?[].concat(j(v.fullFields),[R]):[R]})}function w(){var R=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],M=Array.isArray(R)?R:[R];!s.suppressWarning&&M.length&&t.warning("async-validator:",M),M.length&&v.message!==void 0&&(M=[].concat(v.message));var O=M.map(ua(v,o));if(s.first&&O.length)return b[v.field]=1,g(O);if(!y)g(O);else{if(v.required&&!h.value)return v.message!==void 0?O=[].concat(v.message).map(ua(v,o)):s.error&&(O=[s.error(v,qe(s.messages.required,v.field))]),g(O);var x={};v.defaultField&&Object.keys(h.value).map(function(_){x[_]=v.defaultField}),x=S(S({},x),h.rule.fields);var V={};Object.keys(x).forEach(function(_){var k=x[_],Z=Array.isArray(k)?k:[k];V[_]=Z.map(p.bind(null,_))});var D=new t(V);D.messages(s.messages),h.rule.options&&(h.rule.options.messages=s.messages,h.rule.options.error=s.error),D.validate(h.value,h.rule.options||s,function(_){var k=[];O&&O.length&&k.push.apply(k,j(O)),_&&_.length&&k.push.apply(k,j(_)),g(k.length?k:null)})}}var P;if(v.asyncValidator)P=v.asyncValidator(v,h.value,w,h.source,s);else if(v.validator){try{P=v.validator(v,h.value,w,h.source,s)}catch(R){var E,F;(E=(F=console).error)===null||E===void 0||E.call(F,R),s.suppressValidatorError||setTimeout(function(){throw R},0),w(R.message)}P===!0?w():P===!1?w(typeof v.message=="function"?v.message(v.fullField||v.field):v.message||"".concat(v.fullField||v.field," fails")):P instanceof Array?w(P):P instanceof Error&&w(P.message)}P&&P.then&&P.then(function(){return w()},function(R){return w(R)})},function(h){c(h)},o)}},{key:"getType",value:function(e){if(e.type===void 0&&e.pattern instanceof RegExp&&(e.type="pattern"),typeof e.validator!="function"&&e.type&&!vr.hasOwnProperty(e.type))throw new Error(qe("Unknown rule type %s",e.type));return e.type||"string"}},{key:"getValidationMethod",value:function(e){if(typeof e.validator=="function")return e.validator;var n=Object.keys(e),a=n.indexOf("message");return a!==-1&&n.splice(a,1),n.length===1&&n[0]==="required"?vr.required:vr[this.getType(e)]||void 0}}]),t}();C(br,"register",function(r,e){if(typeof e!="function")throw new Error("Cannot register a validator by type, validator is not a function");vr[r]=e});C(br,"warning",as);C(br,"messages",$n);C(br,"validators",vr);var We="'${name}' is not a valid ${type}",Za={default:"Validation error on field '${name}'",required:"'${name}' is required",enum:"'${name}' must be one of [${enum}]",whitespace:"'${name}' cannot be empty",date:{format:"'${name}' is invalid for format date",parse:"'${name}' could not be parsed as date",invalid:"'${name}' is invalid date"},types:{string:We,method:We,array:We,object:We,number:We,date:We,boolean:We,integer:We,float:We,regexp:We,email:We,url:We,hex:We},string:{len:"'${name}' must be exactly ${len} characters",min:"'${name}' must be at least ${min} characters",max:"'${name}' cannot be longer than ${max} characters",range:"'${name}' must be between ${min} and ${max} characters"},number:{len:"'${name}' must equal ${len}",min:"'${name}' cannot be less than ${min}",max:"'${name}' cannot be greater than ${max}",range:"'${name}' must be between ${min} and ${max}"},array:{len:"'${name}' must be exactly ${len} in length",min:"'${name}' cannot be less than ${min} in length",max:"'${name}' cannot be greater than ${max} in length",range:"'${name}' must be between ${min} and ${max} in length"},pattern:{mismatch:"'${name}' does not match pattern ${pattern}"}},da=br;function _s(t,r){return t.replace(/\\?\$\{\w+\}/g,function(e){if(e.startsWith("\\"))return e.slice(1);var n=e.slice(2,-1);return r[n]})}var va="CODE_LOGIC_ERROR";function Nn(t,r,e,n,a){return kn.apply(this,arguments)}function kn(){return kn=pr(ot().mark(function t(r,e,n,a,i){var o,s,l,c,u,d,m,b,h;return ot().wrap(function(v){for(;;)switch(v.prev=v.next){case 0:return o=S({},n),delete o.ruleIndex,da.warning=function(){},o.validator&&(s=o.validator,o.validator=function(){try{return s.apply(void 0,arguments)}catch(y){return console.error(y),Promise.reject(va)}}),l=null,o&&o.type==="array"&&o.defaultField&&(l=o.defaultField,delete o.defaultField),c=new da(C({},r,[o])),u=fr(Za,a.validateMessages),c.messages(u),d=[],v.prev=10,v.next=13,Promise.resolve(c.validate(C({},r,e),S({},a)));case 13:v.next=18;break;case 15:v.prev=15,v.t0=v.catch(10),v.t0.errors&&(d=v.t0.errors.map(function(y,p){var w=y.message,P=w===va?u.default:w;return f.isValidElement(P)?f.cloneElement(P,{key:"error_".concat(p)}):P}));case 18:if(!(!d.length&&l)){v.next=23;break}return v.next=21,Promise.all(e.map(function(y,p){return Nn("".concat(r,".").concat(p),y,l,a,i)}));case 21:return m=v.sent,v.abrupt("return",m.reduce(function(y,p){return[].concat(j(y),j(p))},[]));case 23:return b=S(S({},n),{},{name:r,enum:(n.enum||[]).join(", ")},i),h=d.map(function(y){return typeof y=="string"?_s(y,b):y}),v.abrupt("return",h);case 26:case"end":return v.stop()}},t,null,[[10,15]])})),kn.apply(this,arguments)}function Ns(t,r,e,n,a,i){var o=t.join("."),s=e.map(function(u,d){var m=u.validator,b=S(S({},u),{},{ruleIndex:d});return m&&(b.validator=function(h,g,v){var y=!1,p=function(){for(var E=arguments.length,F=new Array(E),R=0;R<E;R++)F[R]=arguments[R];Promise.resolve().then(function(){Ft(!y,"Your validator function has already return a promise. `callback` will be ignored."),y||v.apply(void 0,F)})},w=m(h,g,p);y=w&&typeof w.then=="function"&&typeof w.catch=="function",Ft(y,"`callback` is deprecated. Please return a promise instead."),y&&w.then(function(){v()}).catch(function(P){v(P||" ")})}),b}).sort(function(u,d){var m=u.warningOnly,b=u.ruleIndex,h=d.warningOnly,g=d.ruleIndex;return!!m==!!h?b-g:m?1:-1}),l;if(a===!0)l=new Promise(function(){var u=pr(ot().mark(function d(m,b){var h,g,v;return ot().wrap(function(p){for(;;)switch(p.prev=p.next){case 0:h=0;case 1:if(!(h<s.length)){p.next=12;break}return g=s[h],p.next=5,Nn(o,r,g,n,i);case 5:if(v=p.sent,!v.length){p.next=9;break}return b([{errors:v,rule:g}]),p.abrupt("return");case 9:h+=1,p.next=1;break;case 12:m([]);case 13:case"end":return p.stop()}},d)}));return function(d,m){return u.apply(this,arguments)}}());else{var c=s.map(function(u){return Nn(o,r,u,n,i).then(function(d){return{errors:d,rule:u}})});l=(a?Is(c):ks(c)).then(function(u){return Promise.reject(u)})}return l.catch(function(u){return u}),l}function ks(t){return In.apply(this,arguments)}function In(){return In=pr(ot().mark(function t(r){return ot().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",Promise.all(r).then(function(a){var i,o=(i=[]).concat.apply(i,j(a));return o}));case 1:case"end":return n.stop()}},t)})),In.apply(this,arguments)}function Is(t){return Tn.apply(this,arguments)}function Tn(){return Tn=pr(ot().mark(function t(r){var e;return ot().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return e=0,a.abrupt("return",new Promise(function(i){r.forEach(function(o){o.then(function(s){s.errors.length&&i([s]),e+=1,e===r.length&&i([])})})}));case 2:case"end":return a.stop()}},t)})),Tn.apply(this,arguments)}function fe(t){return xn(t)}function ma(t,r){var e={};return r.forEach(function(n){var a=Vt(t,n);e=yt(e,n,a)}),e}function Qt(t,r){var e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;return t&&t.some(function(n){return Qa(r,n,e)})}function Qa(t,r){var e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;return!t||!r||!e&&t.length!==r.length?!1:r.every(function(n,a){return t[a]===n})}function Ts(t,r){if(t===r)return!0;if(!t&&r||t&&!r||!t||!r||$e(t)!=="object"||$e(r)!=="object")return!1;var e=Object.keys(t),n=Object.keys(r),a=new Set([].concat(e,n));return j(a).every(function(i){var o=t[i],s=r[i];return typeof o=="function"&&typeof s=="function"?!0:o===s})}function As(t){var r=arguments.length<=1?void 0:arguments[1];return r&&r.target&&$e(r.target)==="object"&&t in r.target?r.target[t]:r}function ha(t,r,e){var n=t.length;if(r<0||r>=n||e<0||e>=n)return t;var a=t[r],i=r-e;return i>0?[].concat(j(t.slice(0,e)),[a],j(t.slice(e,r)),j(t.slice(r+1,n))):i<0?[].concat(j(t.slice(0,r)),j(t.slice(r+1,e+1)),[a],j(t.slice(e+1,n))):t}var Ds=["name"],Je=[];function Cn(t,r,e,n,a,i){return typeof t=="function"?t(r,e,"source"in i?{source:i.source}:{}):n!==a}var Un=function(t){zn(e,t);var r=jn(e);function e(n){var a;if(Yt(this,e),a=r.call(this,n),C(K(a),"state",{resetCount:0}),C(K(a),"cancelRegisterFunc",null),C(K(a),"mounted",!1),C(K(a),"touched",!1),C(K(a),"dirty",!1),C(K(a),"validatePromise",void 0),C(K(a),"prevValidating",void 0),C(K(a),"errors",Je),C(K(a),"warnings",Je),C(K(a),"cancelRegister",function(){var l=a.props,c=l.preserve,u=l.isListField,d=l.name;a.cancelRegisterFunc&&a.cancelRegisterFunc(u,c,fe(d)),a.cancelRegisterFunc=null}),C(K(a),"getNamePath",function(){var l=a.props,c=l.name,u=l.fieldContext,d=u.prefixName,m=d===void 0?[]:d;return c!==void 0?[].concat(j(m),j(c)):[]}),C(K(a),"getRules",function(){var l=a.props,c=l.rules,u=c===void 0?[]:c,d=l.fieldContext;return u.map(function(m){return typeof m=="function"?m(d):m})}),C(K(a),"refresh",function(){a.mounted&&a.setState(function(l){var c=l.resetCount;return{resetCount:c+1}})}),C(K(a),"metaCache",null),C(K(a),"triggerMetaEvent",function(l){var c=a.props.onMetaChange;if(c){var u=S(S({},a.getMeta()),{},{destroy:l});Qn(a.metaCache,u)||c(u),a.metaCache=u}else a.metaCache=null}),C(K(a),"onStoreChange",function(l,c,u){var d=a.props,m=d.shouldUpdate,b=d.dependencies,h=b===void 0?[]:b,g=d.onReset,v=u.store,y=a.getNamePath(),p=a.getValue(l),w=a.getValue(v),P=c&&Qt(c,y);switch(u.type==="valueUpdate"&&u.source==="external"&&!Qn(p,w)&&(a.touched=!0,a.dirty=!0,a.validatePromise=null,a.errors=Je,a.warnings=Je,a.triggerMetaEvent()),u.type){case"reset":if(!c||P){a.touched=!1,a.dirty=!1,a.validatePromise=void 0,a.errors=Je,a.warnings=Je,a.triggerMetaEvent(),g==null||g(),a.refresh();return}break;case"remove":{if(m&&Cn(m,l,v,p,w,u)){a.reRender();return}break}case"setField":{var E=u.data;if(P){"touched"in E&&(a.touched=E.touched),"validating"in E&&!("originRCField"in E)&&(a.validatePromise=E.validating?Promise.resolve([]):null),"errors"in E&&(a.errors=E.errors||Je),"warnings"in E&&(a.warnings=E.warnings||Je),a.dirty=!0,a.triggerMetaEvent(),a.reRender();return}else if("value"in E&&Qt(c,y,!0)){a.reRender();return}if(m&&!y.length&&Cn(m,l,v,p,w,u)){a.reRender();return}break}case"dependenciesUpdate":{var F=h.map(fe);if(F.some(function(R){return Qt(u.relatedFields,R)})){a.reRender();return}break}default:if(P||(!h.length||y.length||m)&&Cn(m,l,v,p,w,u)){a.reRender();return}break}m===!0&&a.reRender()}),C(K(a),"validateRules",function(l){var c=a.getNamePath(),u=a.getValue(),d=l||{},m=d.triggerName,b=d.validateOnly,h=b===void 0?!1:b,g=Promise.resolve().then(pr(ot().mark(function v(){var y,p,w,P,E,F,R;return ot().wrap(function(O){for(;;)switch(O.prev=O.next){case 0:if(a.mounted){O.next=2;break}return O.abrupt("return",[]);case 2:if(y=a.props,p=y.validateFirst,w=p===void 0?!1:p,P=y.messageVariables,E=y.validateDebounce,F=a.getRules(),m&&(F=F.filter(function(x){return x}).filter(function(x){var V=x.validateTrigger;if(!V)return!0;var D=xn(V);return D.includes(m)})),!(E&&m)){O.next=10;break}return O.next=8,new Promise(function(x){setTimeout(x,E)});case 8:if(a.validatePromise===g){O.next=10;break}return O.abrupt("return",[]);case 10:return R=Ns(c,u,F,l,w,P),R.catch(function(x){return x}).then(function(){var x=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Je;if(a.validatePromise===g){var V;a.validatePromise=null;var D=[],_=[];(V=x.forEach)===null||V===void 0||V.call(x,function(k){var Z=k.rule.warningOnly,z=k.errors,U=z===void 0?Je:z;Z?_.push.apply(_,j(U)):D.push.apply(D,j(U))}),a.errors=D,a.warnings=_,a.triggerMetaEvent(),a.reRender()}}),O.abrupt("return",R);case 13:case"end":return O.stop()}},v)})));return h||(a.validatePromise=g,a.dirty=!0,a.errors=Je,a.warnings=Je,a.triggerMetaEvent(),a.reRender()),g}),C(K(a),"isFieldValidating",function(){return!!a.validatePromise}),C(K(a),"isFieldTouched",function(){return a.touched}),C(K(a),"isFieldDirty",function(){if(a.dirty||a.props.initialValue!==void 0)return!0;var l=a.props.fieldContext,c=l.getInternalHooks(_t),u=c.getInitialValue;return u(a.getNamePath())!==void 0}),C(K(a),"getErrors",function(){return a.errors}),C(K(a),"getWarnings",function(){return a.warnings}),C(K(a),"isListField",function(){return a.props.isListField}),C(K(a),"isList",function(){return a.props.isList}),C(K(a),"isPreserve",function(){return a.props.preserve}),C(K(a),"getMeta",function(){a.prevValidating=a.isFieldValidating();var l={touched:a.isFieldTouched(),validating:a.prevValidating,errors:a.errors,warnings:a.warnings,name:a.getNamePath(),validated:a.validatePromise===null};return l}),C(K(a),"getOnlyChild",function(l){if(typeof l=="function"){var c=a.getMeta();return S(S({},a.getOnlyChild(l(a.getControlled(),c,a.props.fieldContext))),{},{isFunction:!0})}var u=Aa(l);return u.length!==1||!f.isValidElement(u[0])?{child:u,isFunction:!1}:{child:u[0],isFunction:!1}}),C(K(a),"getValue",function(l){var c=a.props.fieldContext.getFieldsValue,u=a.getNamePath();return Vt(l||c(!0),u)}),C(K(a),"getControlled",function(){var l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},c=a.props,u=c.name,d=c.trigger,m=c.validateTrigger,b=c.getValueFromEvent,h=c.normalize,g=c.valuePropName,v=c.getValueProps,y=c.fieldContext,p=m!==void 0?m:y.validateTrigger,w=a.getNamePath(),P=y.getInternalHooks,E=y.getFieldsValue,F=P(_t),R=F.dispatch,M=a.getValue(),O=v||function(k){return C({},g,k)},x=l[d],V=u!==void 0?O(M):{},D=S(S({},l),V);D[d]=function(){a.touched=!0,a.dirty=!0,a.triggerMetaEvent();for(var k,Z=arguments.length,z=new Array(Z),U=0;U<Z;U++)z[U]=arguments[U];b?k=b.apply(void 0,z):k=As.apply(void 0,[g].concat(z)),h&&(k=h(k,M,E(!0))),k!==M&&R({type:"updateValue",namePath:w,value:k}),x&&x.apply(void 0,z)};var _=xn(p||[]);return _.forEach(function(k){var Z=D[k];D[k]=function(){Z&&Z.apply(void 0,arguments);var z=a.props.rules;z&&z.length&&R({type:"validateField",namePath:w,triggerName:k})}}),D}),n.fieldContext){var i=n.fieldContext.getInternalHooks,o=i(_t),s=o.initEntityValue;s(K(a))}return a}return er(e,[{key:"componentDidMount",value:function(){var a=this.props,i=a.shouldUpdate,o=a.fieldContext;if(this.mounted=!0,o){var s=o.getInternalHooks,l=s(_t),c=l.registerField;this.cancelRegisterFunc=c(this)}i===!0&&this.reRender()}},{key:"componentWillUnmount",value:function(){this.cancelRegister(),this.triggerMetaEvent(!0),this.mounted=!1}},{key:"reRender",value:function(){this.mounted&&this.forceUpdate()}},{key:"render",value:function(){var a=this.state.resetCount,i=this.props.children,o=this.getOnlyChild(i),s=o.child,l=o.isFunction,c;return l?c=s:f.isValidElement(s)?c=f.cloneElement(s,this.getControlled(s.props)):(Ft(!s,"`children` of Field is not validate ReactElement."),c=s),f.createElement(f.Fragment,{key:a},c)}}]),e}(f.Component);C(Un,"contextType",Xt);C(Un,"defaultProps",{trigger:"onChange",valuePropName:"value"});function Ja(t){var r,e=t.name,n=it(t,Ds),a=f.useContext(Xt),i=f.useContext(Kr),o=e!==void 0?fe(e):void 0,s=(r=n.isListField)!==null&&r!==void 0?r:!!i,l="keep";return s||(l="_".concat((o||[]).join("_"))),f.createElement(Un,Ie({key:l,name:o,isListField:s},n,{fieldContext:a}))}function Ls(t){var r=t.name,e=t.initialValue,n=t.children,a=t.rules,i=t.validateTrigger,o=t.isListField,s=f.useContext(Xt),l=f.useContext(Kr),c=f.useRef({keys:[],id:0}),u=c.current,d=f.useMemo(function(){var g=fe(s.prefixName)||[];return[].concat(j(g),j(fe(r)))},[s.prefixName,r]),m=f.useMemo(function(){return S(S({},s),{},{prefixName:d})},[s,d]),b=f.useMemo(function(){return{getKey:function(v){var y=d.length,p=v[y];return[u.keys[p],v.slice(y+1)]}}},[d]);if(typeof n!="function")return Ft(!1,"Form.List only accepts function as children."),null;var h=function(v,y,p){var w=p.source;return w==="internal"?!1:v!==y};return f.createElement(Kr.Provider,{value:b},f.createElement(Xt.Provider,{value:m},f.createElement(Ja,{name:[],shouldUpdate:h,rules:a,validateTrigger:i,initialValue:e,isList:!0,isListField:o??!!l},function(g,v){var y=g.value,p=y===void 0?[]:y,w=g.onChange,P=s.getFieldValue,E=function(){var O=P(d||[]);return O||[]},F={add:function(O,x){var V=E();x>=0&&x<=V.length?(u.keys=[].concat(j(u.keys.slice(0,x)),[u.id],j(u.keys.slice(x))),w([].concat(j(V.slice(0,x)),[O],j(V.slice(x))))):(u.keys=[].concat(j(u.keys),[u.id]),w([].concat(j(V),[O]))),u.id+=1},remove:function(O){var x=E(),V=new Set(Array.isArray(O)?O:[O]);V.size<=0||(u.keys=u.keys.filter(function(D,_){return!V.has(_)}),w(x.filter(function(D,_){return!V.has(_)})))},move:function(O,x){if(O!==x){var V=E();O<0||O>=V.length||x<0||x>=V.length||(u.keys=ha(u.keys,O,x),w(ha(V,O,x)))}}},R=p||[];return Array.isArray(R)||(R=[]),n(R.map(function(M,O){var x=u.keys[O];return x===void 0&&(u.keys[O]=u.id,x=u.keys[O],u.id+=1),{name:O,key:x,isListField:!0}}),F,v)})))}function zs(t){var r=!1,e=t.length,n=[];return t.length?new Promise(function(a,i){t.forEach(function(o,s){o.catch(function(l){return r=!0,l}).then(function(l){e-=1,n[s]=l,!(e>0)&&(r&&i(n),a(n))})})}):Promise.resolve([])}var Xa="__@field_split__";function Fn(t){return t.map(function(r){return"".concat($e(r),":").concat(r)}).join(Xa)}var Ut=function(){function t(){Yt(this,t),C(this,"kvs",new Map)}return er(t,[{key:"set",value:function(e,n){this.kvs.set(Fn(e),n)}},{key:"get",value:function(e){return this.kvs.get(Fn(e))}},{key:"update",value:function(e,n){var a=this.get(e),i=n(a);i?this.set(e,i):this.delete(e)}},{key:"delete",value:function(e){this.kvs.delete(Fn(e))}},{key:"map",value:function(e){return j(this.kvs.entries()).map(function(n){var a=q(n,2),i=a[0],o=a[1],s=i.split(Xa);return e({key:s.map(function(l){var c=l.match(/^([^:]*):(.*)$/),u=q(c,3),d=u[1],m=u[2];return d==="number"?Number(m):m}),value:o})})}},{key:"toJSON",value:function(){var e={};return this.map(function(n){var a=n.key,i=n.value;return e[a.join(".")]=i,null}),e}}]),t}(),js=["name"],Ws=er(function t(r){var e=this;Yt(this,t),C(this,"formHooked",!1),C(this,"forceRootUpdate",void 0),C(this,"subscribable",!0),C(this,"store",{}),C(this,"fieldEntities",[]),C(this,"initialValues",{}),C(this,"callbacks",{}),C(this,"validateMessages",null),C(this,"preserve",null),C(this,"lastValidatePromise",null),C(this,"getForm",function(){return{getFieldValue:e.getFieldValue,getFieldsValue:e.getFieldsValue,getFieldError:e.getFieldError,getFieldWarning:e.getFieldWarning,getFieldsError:e.getFieldsError,isFieldsTouched:e.isFieldsTouched,isFieldTouched:e.isFieldTouched,isFieldValidating:e.isFieldValidating,isFieldsValidating:e.isFieldsValidating,resetFields:e.resetFields,setFields:e.setFields,setFieldValue:e.setFieldValue,setFieldsValue:e.setFieldsValue,validateFields:e.validateFields,submit:e.submit,_init:!0,getInternalHooks:e.getInternalHooks}}),C(this,"getInternalHooks",function(n){return n===_t?(e.formHooked=!0,{dispatch:e.dispatch,initEntityValue:e.initEntityValue,registerField:e.registerField,useSubscribe:e.useSubscribe,setInitialValues:e.setInitialValues,destroyForm:e.destroyForm,setCallbacks:e.setCallbacks,setValidateMessages:e.setValidateMessages,getFields:e.getFields,setPreserve:e.setPreserve,getInitialValue:e.getInitialValue,registerWatch:e.registerWatch}):(Ft(!1,"`getInternalHooks` is internal usage. Should not call directly."),null)}),C(this,"useSubscribe",function(n){e.subscribable=n}),C(this,"prevWithoutPreserves",null),C(this,"setInitialValues",function(n,a){if(e.initialValues=n||{},a){var i,o=fr(n,e.store);(i=e.prevWithoutPreserves)===null||i===void 0||i.map(function(s){var l=s.key;o=yt(o,l,Vt(n,l))}),e.prevWithoutPreserves=null,e.updateStore(o)}}),C(this,"destroyForm",function(n){if(n)e.updateStore({});else{var a=new Ut;e.getFieldEntities(!0).forEach(function(i){e.isMergedPreserve(i.isPreserve())||a.set(i.getNamePath(),!0)}),e.prevWithoutPreserves=a}}),C(this,"getInitialValue",function(n){var a=Vt(e.initialValues,n);return n.length?fr(a):a}),C(this,"setCallbacks",function(n){e.callbacks=n}),C(this,"setValidateMessages",function(n){e.validateMessages=n}),C(this,"setPreserve",function(n){e.preserve=n}),C(this,"watchList",[]),C(this,"registerWatch",function(n){return e.watchList.push(n),function(){e.watchList=e.watchList.filter(function(a){return a!==n})}}),C(this,"notifyWatch",function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];if(e.watchList.length){var a=e.getFieldsValue(),i=e.getFieldsValue(!0);e.watchList.forEach(function(o){o(a,i,n)})}}),C(this,"timeoutId",null),C(this,"warningUnhooked",function(){}),C(this,"updateStore",function(n){e.store=n}),C(this,"getFieldEntities",function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;return n?e.fieldEntities.filter(function(a){return a.getNamePath().length}):e.fieldEntities}),C(this,"getFieldsMap",function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,a=new Ut;return e.getFieldEntities(n).forEach(function(i){var o=i.getNamePath();a.set(o,i)}),a}),C(this,"getFieldEntitiesForNamePathList",function(n){if(!n)return e.getFieldEntities(!0);var a=e.getFieldsMap(!0);return n.map(function(i){var o=fe(i);return a.get(o)||{INVALIDATE_NAME_PATH:fe(i)}})}),C(this,"getFieldsValue",function(n,a){e.warningUnhooked();var i,o,s;if(n===!0||Array.isArray(n)?(i=n,o=a):n&&$e(n)==="object"&&(s=n.strict,o=n.filter),i===!0&&!o)return e.store;var l=e.getFieldEntitiesForNamePathList(Array.isArray(i)?i:null),c=[];return l.forEach(function(u){var d,m,b="INVALIDATE_NAME_PATH"in u?u.INVALIDATE_NAME_PATH:u.getNamePath();if(s){var h,g;if((h=(g=u).isList)!==null&&h!==void 0&&h.call(g))return}else if(!i&&(d=(m=u).isListField)!==null&&d!==void 0&&d.call(m))return;if(!o)c.push(b);else{var v="getMeta"in u?u.getMeta():null;o(v)&&c.push(b)}}),ma(e.store,c.map(fe))}),C(this,"getFieldValue",function(n){e.warningUnhooked();var a=fe(n);return Vt(e.store,a)}),C(this,"getFieldsError",function(n){e.warningUnhooked();var a=e.getFieldEntitiesForNamePathList(n);return a.map(function(i,o){return i&&!("INVALIDATE_NAME_PATH"in i)?{name:i.getNamePath(),errors:i.getErrors(),warnings:i.getWarnings()}:{name:fe(n[o]),errors:[],warnings:[]}})}),C(this,"getFieldError",function(n){e.warningUnhooked();var a=fe(n),i=e.getFieldsError([a])[0];return i.errors}),C(this,"getFieldWarning",function(n){e.warningUnhooked();var a=fe(n),i=e.getFieldsError([a])[0];return i.warnings}),C(this,"isFieldsTouched",function(){e.warningUnhooked();for(var n=arguments.length,a=new Array(n),i=0;i<n;i++)a[i]=arguments[i];var o=a[0],s=a[1],l,c=!1;a.length===0?l=null:a.length===1?Array.isArray(o)?(l=o.map(fe),c=!1):(l=null,c=o):(l=o.map(fe),c=s);var u=e.getFieldEntities(!0),d=function(v){return v.isFieldTouched()};if(!l)return c?u.every(function(g){return d(g)||g.isList()}):u.some(d);var m=new Ut;l.forEach(function(g){m.set(g,[])}),u.forEach(function(g){var v=g.getNamePath();l.forEach(function(y){y.every(function(p,w){return v[w]===p})&&m.update(y,function(p){return[].concat(j(p),[g])})})});var b=function(v){return v.some(d)},h=m.map(function(g){var v=g.value;return v});return c?h.every(b):h.some(b)}),C(this,"isFieldTouched",function(n){return e.warningUnhooked(),e.isFieldsTouched([n])}),C(this,"isFieldsValidating",function(n){e.warningUnhooked();var a=e.getFieldEntities();if(!n)return a.some(function(o){return o.isFieldValidating()});var i=n.map(fe);return a.some(function(o){var s=o.getNamePath();return Qt(i,s)&&o.isFieldValidating()})}),C(this,"isFieldValidating",function(n){return e.warningUnhooked(),e.isFieldsValidating([n])}),C(this,"resetWithFieldInitialValue",function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},a=new Ut,i=e.getFieldEntities(!0);i.forEach(function(l){var c=l.props.initialValue,u=l.getNamePath();if(c!==void 0){var d=a.get(u)||new Set;d.add({entity:l,value:c}),a.set(u,d)}});var o=function(c){c.forEach(function(u){var d=u.props.initialValue;if(d!==void 0){var m=u.getNamePath(),b=e.getInitialValue(m);if(b!==void 0)Ft(!1,"Form already set 'initialValues' with path '".concat(m.join("."),"'. Field can not overwrite it."));else{var h=a.get(m);if(h&&h.size>1)Ft(!1,"Multiple Field with path '".concat(m.join("."),"' set 'initialValue'. Can not decide which one to pick."));else if(h){var g=e.getFieldValue(m),v=u.isListField();!v&&(!n.skipExist||g===void 0)&&e.updateStore(yt(e.store,m,j(h)[0].value))}}}})},s;n.entities?s=n.entities:n.namePathList?(s=[],n.namePathList.forEach(function(l){var c=a.get(l);if(c){var u;(u=s).push.apply(u,j(j(c).map(function(d){return d.entity})))}})):s=i,o(s)}),C(this,"resetFields",function(n){e.warningUnhooked();var a=e.store;if(!n){e.updateStore(fr(e.initialValues)),e.resetWithFieldInitialValue(),e.notifyObservers(a,null,{type:"reset"}),e.notifyWatch();return}var i=n.map(fe);i.forEach(function(o){var s=e.getInitialValue(o);e.updateStore(yt(e.store,o,s))}),e.resetWithFieldInitialValue({namePathList:i}),e.notifyObservers(a,i,{type:"reset"}),e.notifyWatch(i)}),C(this,"setFields",function(n){e.warningUnhooked();var a=e.store,i=[];n.forEach(function(o){var s=o.name,l=it(o,js),c=fe(s);i.push(c),"value"in l&&e.updateStore(yt(e.store,c,l.value)),e.notifyObservers(a,[c],{type:"setField",data:o})}),e.notifyWatch(i)}),C(this,"getFields",function(){var n=e.getFieldEntities(!0),a=n.map(function(i){var o=i.getNamePath(),s=i.getMeta(),l=S(S({},s),{},{name:o,value:e.getFieldValue(o)});return Object.defineProperty(l,"originRCField",{value:!0}),l});return a}),C(this,"initEntityValue",function(n){var a=n.props.initialValue;if(a!==void 0){var i=n.getNamePath(),o=Vt(e.store,i);o===void 0&&e.updateStore(yt(e.store,i,a))}}),C(this,"isMergedPreserve",function(n){var a=n!==void 0?n:e.preserve;return a??!0}),C(this,"registerField",function(n){e.fieldEntities.push(n);var a=n.getNamePath();if(e.notifyWatch([a]),n.props.initialValue!==void 0){var i=e.store;e.resetWithFieldInitialValue({entities:[n],skipExist:!0}),e.notifyObservers(i,[n.getNamePath()],{type:"valueUpdate",source:"internal"})}return function(o,s){var l=arguments.length>2&&arguments[2]!==void 0?arguments[2]:[];if(e.fieldEntities=e.fieldEntities.filter(function(d){return d!==n}),!e.isMergedPreserve(s)&&(!o||l.length>1)){var c=o?void 0:e.getInitialValue(a);if(a.length&&e.getFieldValue(a)!==c&&e.fieldEntities.every(function(d){return!Qa(d.getNamePath(),a)})){var u=e.store;e.updateStore(yt(u,a,c,!0)),e.notifyObservers(u,[a],{type:"remove"}),e.triggerDependenciesUpdate(u,a)}}e.notifyWatch([a])}}),C(this,"dispatch",function(n){switch(n.type){case"updateValue":{var a=n.namePath,i=n.value;e.updateValue(a,i);break}case"validateField":{var o=n.namePath,s=n.triggerName;e.validateFields([o],{triggerName:s});break}}}),C(this,"notifyObservers",function(n,a,i){if(e.subscribable){var o=S(S({},i),{},{store:e.getFieldsValue(!0)});e.getFieldEntities().forEach(function(s){var l=s.onStoreChange;l(n,a,o)})}else e.forceRootUpdate()}),C(this,"triggerDependenciesUpdate",function(n,a){var i=e.getDependencyChildrenFields(a);return i.length&&e.validateFields(i),e.notifyObservers(n,i,{type:"dependenciesUpdate",relatedFields:[a].concat(j(i))}),i}),C(this,"updateValue",function(n,a){var i=fe(n),o=e.store;e.updateStore(yt(e.store,i,a)),e.notifyObservers(o,[i],{type:"valueUpdate",source:"internal"}),e.notifyWatch([i]);var s=e.triggerDependenciesUpdate(o,i),l=e.callbacks.onValuesChange;if(l){var c=ma(e.store,[i]);l(c,e.getFieldsValue())}e.triggerOnFieldsChange([i].concat(j(s)))}),C(this,"setFieldsValue",function(n){e.warningUnhooked();var a=e.store;if(n){var i=fr(e.store,n);e.updateStore(i)}e.notifyObservers(a,null,{type:"valueUpdate",source:"external"}),e.notifyWatch()}),C(this,"setFieldValue",function(n,a){e.setFields([{name:n,value:a,errors:[],warnings:[]}])}),C(this,"getDependencyChildrenFields",function(n){var a=new Set,i=[],o=new Ut;e.getFieldEntities().forEach(function(l){var c=l.props.dependencies;(c||[]).forEach(function(u){var d=fe(u);o.update(d,function(){var m=arguments.length>0&&arguments[0]!==void 0?arguments[0]:new Set;return m.add(l),m})})});var s=function l(c){var u=o.get(c)||new Set;u.forEach(function(d){if(!a.has(d)){a.add(d);var m=d.getNamePath();d.isFieldDirty()&&m.length&&(i.push(m),l(m))}})};return s(n),i}),C(this,"triggerOnFieldsChange",function(n,a){var i=e.callbacks.onFieldsChange;if(i){var o=e.getFields();if(a){var s=new Ut;a.forEach(function(c){var u=c.name,d=c.errors;s.set(u,d)}),o.forEach(function(c){c.errors=s.get(c.name)||c.errors})}var l=o.filter(function(c){var u=c.name;return Qt(n,u)});l.length&&i(l,o)}}),C(this,"validateFields",function(n,a){e.warningUnhooked();var i,o;Array.isArray(n)||typeof n=="string"||typeof a=="string"?(i=n,o=a):o=n;var s=!!i,l=s?i.map(fe):[],c=[],u=String(Date.now()),d=new Set,m=o||{},b=m.recursive,h=m.dirty;e.getFieldEntities(!0).forEach(function(p){if(s||l.push(p.getNamePath()),!(!p.props.rules||!p.props.rules.length)&&!(h&&!p.isFieldDirty())){var w=p.getNamePath();if(d.add(w.join(u)),!s||Qt(l,w,b)){var P=p.validateRules(S({validateMessages:S(S({},Za),e.validateMessages)},o));c.push(P.then(function(){return{name:w,errors:[],warnings:[]}}).catch(function(E){var F,R=[],M=[];return(F=E.forEach)===null||F===void 0||F.call(E,function(O){var x=O.rule.warningOnly,V=O.errors;x?M.push.apply(M,j(V)):R.push.apply(R,j(V))}),R.length?Promise.reject({name:w,errors:R,warnings:M}):{name:w,errors:R,warnings:M}}))}}});var g=zs(c);e.lastValidatePromise=g,g.catch(function(p){return p}).then(function(p){var w=p.map(function(P){var E=P.name;return E});e.notifyObservers(e.store,w,{type:"validateFinish"}),e.triggerOnFieldsChange(w,p)});var v=g.then(function(){return e.lastValidatePromise===g?Promise.resolve(e.getFieldsValue(l)):Promise.reject([])}).catch(function(p){var w=p.filter(function(P){return P&&P.errors.length});return Promise.reject({values:e.getFieldsValue(l),errorFields:w,outOfDate:e.lastValidatePromise!==g})});v.catch(function(p){return p});var y=l.filter(function(p){return d.has(p.join(u))});return e.triggerOnFieldsChange(y),v}),C(this,"submit",function(){e.warningUnhooked(),e.validateFields().then(function(n){var a=e.callbacks.onFinish;if(a)try{a(n)}catch(i){console.error(i)}}).catch(function(n){var a=e.callbacks.onFinishFailed;a&&a(n)})}),this.forceRootUpdate=r});function Ya(t){var r=f.useRef(),e=f.useState({}),n=q(e,2),a=n[1];if(!r.current)if(t)r.current=t;else{var i=function(){a({})},o=new Ws(i);r.current=o.getForm()}return[r.current]}var An=f.createContext({triggerFormChange:function(){},triggerFormFinish:function(){},registerForm:function(){},unregisterForm:function(){}}),ei=function(r){var e=r.validateMessages,n=r.onFormChange,a=r.onFormFinish,i=r.children,o=f.useContext(An),s=f.useRef({});return f.createElement(An.Provider,{value:S(S({},o),{},{validateMessages:S(S({},o.validateMessages),e),triggerFormChange:function(c,u){n&&n(c,{changedFields:u,forms:s.current}),o.triggerFormChange(c,u)},triggerFormFinish:function(c,u){a&&a(c,{values:u,forms:s.current}),o.triggerFormFinish(c,u)},registerForm:function(c,u){c&&(s.current=S(S({},s.current),{},C({},c,u))),o.registerForm(c,u)},unregisterForm:function(c){var u=S({},s.current);delete u[c],s.current=u,o.unregisterForm(c)}})},i)},qs=["name","initialValues","fields","form","preserve","children","component","validateMessages","validateTrigger","onValuesChange","onFieldsChange","onFinish","onFinishFailed","clearOnDestroy"],Hs=function(r,e){var n=r.name,a=r.initialValues,i=r.fields,o=r.form,s=r.preserve,l=r.children,c=r.component,u=c===void 0?"form":c,d=r.validateMessages,m=r.validateTrigger,b=m===void 0?"onChange":m,h=r.onValuesChange,g=r.onFieldsChange,v=r.onFinish,y=r.onFinishFailed,p=r.clearOnDestroy,w=it(r,qs),P=f.useRef(null),E=f.useContext(An),F=Ya(o),R=q(F,1),M=R[0],O=M.getInternalHooks(_t),x=O.useSubscribe,V=O.setInitialValues,D=O.setCallbacks,_=O.setValidateMessages,k=O.setPreserve,Z=O.destroyForm;f.useImperativeHandle(e,function(){return S(S({},M),{},{nativeElement:P.current})}),f.useEffect(function(){return E.registerForm(n,M),function(){E.unregisterForm(n)}},[E,M,n]),_(S(S({},E.validateMessages),d)),D({onValuesChange:h,onFieldsChange:function(T){if(E.triggerFormChange(n,T),g){for(var B=arguments.length,we=new Array(B>1?B-1:0),Ce=1;Ce<B;Ce++)we[Ce-1]=arguments[Ce];g.apply(void 0,[T].concat(we))}},onFinish:function(T){E.triggerFormFinish(n,T),v&&v(T)},onFinishFailed:y}),k(s);var z=f.useRef(null);V(a,!z.current),z.current||(z.current=!0),f.useEffect(function(){return function(){return Z(p)}},[]);var U,ee=typeof l=="function";if(ee){var te=M.getFieldsValue(!0);U=l(te,M)}else U=l;x(!ee);var W=f.useRef();f.useEffect(function(){Ts(W.current||[],i||[])||M.setFields(i||[]),W.current=i},[i,M]);var Se=f.useMemo(function(){return S(S({},M),{},{validateTrigger:b})},[M,b]),ce=f.createElement(Kr.Provider,{value:null},f.createElement(Xt.Provider,{value:Se},U));return u===!1?ce:f.createElement(u,Ie({},w,{ref:P,onSubmit:function(T){T.preventDefault(),T.stopPropagation(),M.submit()},onReset:function(T){var B;T.preventDefault(),M.resetFields(),(B=w.onReset)===null||B===void 0||B.call(w,T)}}),ce)};function ga(t){try{return JSON.stringify(t)}catch{return Math.random()}}function Us(){for(var t=arguments.length,r=new Array(t),e=0;e<t;e++)r[e]=arguments[e];var n=r[0],a=r[1],i=a===void 0?{}:a,o=es(i)?{form:i}:i,s=o.form,l=f.useState(),c=q(l,2),u=c[0],d=c[1],m=f.useMemo(function(){return ga(u)},[u]),b=f.useRef(m);b.current=m;var h=f.useContext(Xt),g=s||h,v=g&&g._init,y=fe(n),p=f.useRef(y);return p.current=y,f.useEffect(function(){if(v){var w=g.getFieldsValue,P=g.getInternalHooks,E=P(_t),F=E.registerWatch,R=function(V,D){var _=o.preserve?D:V;return typeof n=="function"?n(_):Vt(_,p.current)},M=F(function(x,V){var D=R(x,V),_=ga(D);b.current!==_&&(b.current=_,d(D))}),O=R(w(),w(!0));return u!==O&&d(O),M}},[v]),u}var Bs=f.forwardRef(Hs),wr=Bs;wr.FormProvider=ei;wr.Field=Ja;wr.List=Ls;wr.useForm=Ya;wr.useWatch=Us;const ou=f.createContext({labelAlign:"right",vertical:!1,itemRef:()=>{}}),su=f.createContext(null),lu=t=>{const r=Ni(t,["prefixCls"]);return f.createElement(ei,Object.assign({},r))},uu=f.createContext({prefixCls:""}),pa=f.createContext({}),Ks=t=>{let{children:r,status:e,override:n}=t;const a=f.useContext(pa),i=f.useMemo(()=>{const o=Object.assign({},a);return n&&delete o.isFormItemInput,e&&(delete o.status,delete o.hasFeedback,delete o.feedbackIcon),o},[e,n,a]);return f.createElement(pa.Provider,{value:i},r)},cu=f.createContext(void 0),Gs=t=>{const{space:r,form:e,children:n}=t;if(n==null)return null;let a=n;return e&&(a=qr.createElement(Ks,{override:!0,status:!0},a)),r&&(a=qr.createElement(ki,null,a)),a};function Zs(t){return r=>f.createElement(vi,{theme:{token:{motion:!1,zIndexPopupBase:0}}},f.createElement(t,Object.assign({},r)))}const fu=(t,r,e,n,a)=>Zs(o=>{const{prefixCls:s,style:l}=o,c=f.useRef(null),[u,d]=f.useState(0),[m,b]=f.useState(0),[h,g]=Ha(!1,{value:o.open}),{getPrefixCls:v}=f.useContext(Va),y=v(n||"select",s);f.useEffect(()=>{if(g(!0),typeof ResizeObserver<"u"){const P=new ResizeObserver(F=>{const R=F[0].target;d(R.offsetHeight+8),b(R.offsetWidth)}),E=setInterval(()=>{var F;const R=a?`.${a(y)}`:`.${y}-dropdown`,M=(F=c.current)===null||F===void 0?void 0:F.querySelector(R);M&&(clearInterval(E),P.observe(M))},10);return()=>{clearInterval(E),P.disconnect()}}},[]);let p=Object.assign(Object.assign({},o),{style:Object.assign(Object.assign({},l),{margin:0}),open:h,visible:h,getPopupContainer:()=>c.current});r&&Object.assign(p,{[r]:{overflow:{adjustX:!1,adjustY:!1}}});const w={paddingBottom:u,position:"relative",minWidth:m};return f.createElement("div",{ref:c,style:w},f.createElement(t,Object.assign({},p)))}),Qs=function(){if(typeof navigator>"u"||typeof window>"u")return!1;var t=navigator.userAgent||navigator.vendor||window.opera;return/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(t)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw-(n|u)|c55\/|capi|ccwa|cdm-|cell|chtm|cldc|cmd-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc-s|devi|dica|dmob|do(c|p)o|ds(12|-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(-|_)|g1 u|g560|gene|gf-5|g-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd-(m|p|t)|hei-|hi(pt|ta)|hp( i|ip)|hs-c|ht(c(-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i-(20|go|ma)|i230|iac( |-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|-[a-w])|libw|lynx|m1-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|-([1-8]|c))|phil|pire|pl(ay|uc)|pn-2|po(ck|rt|se)|prox|psio|pt-g|qa-a|qc(07|12|21|32|60|-[2-7]|i-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h-|oo|p-)|sdk\/|se(c(-|0|1)|47|mc|nd|ri)|sgh-|shar|sie(-|m)|sk-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h-|v-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl-|tdg-|tel(i|m)|tim-|t-mo|to(pl|sh)|ts(70|m-|m3|m5)|tx-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas-|your|zeto|zte-/i.test(t==null?void 0:t.substr(0,4))};var Js=["prefixCls","invalidate","item","renderItem","responsive","responsiveDisabled","registerSize","itemKey","className","style","children","display","order","component"],Bt=void 0;function Xs(t,r){var e=t.prefixCls,n=t.invalidate,a=t.item,i=t.renderItem,o=t.responsive,s=t.responsiveDisabled,l=t.registerSize,c=t.itemKey,u=t.className,d=t.style,m=t.children,b=t.display,h=t.order,g=t.component,v=g===void 0?"div":g,y=it(t,Js),p=o&&!b;function w(M){l(c,M)}f.useEffect(function(){return function(){w(null)}},[]);var P=i&&a!==Bt?i(a,{index:h}):m,E;n||(E={opacity:p?0:1,height:p?0:Bt,overflowY:p?"hidden":Bt,order:o?h:Bt,pointerEvents:p?"none":Bt,position:p?"absolute":Bt});var F={};p&&(F["aria-hidden"]=!0);var R=f.createElement(v,Ie({className:Re(!n&&e,u),style:S(S({},E),d)},F,y,{ref:r}),P);return o&&(R=f.createElement(yr,{onResize:function(O){var x=O.offsetWidth;w(x)},disabled:s},R)),R}var mr=f.forwardRef(Xs);mr.displayName="Item";function Ys(t){if(typeof MessageChannel>"u")mi(t);else{var r=new MessageChannel;r.port1.onmessage=function(){return t()},r.port2.postMessage(void 0)}}function el(){var t=f.useRef(null),r=function(n){t.current||(t.current=[],Ys(function(){Ma.unstable_batchedUpdates(function(){t.current.forEach(function(a){a()}),t.current=null})})),t.current.push(n)};return r}function ur(t,r){var e=f.useState(r),n=q(e,2),a=n[0],i=n[1],o=at(function(s){t(function(){i(s)})});return[a,o]}var Gr=qr.createContext(null),tl=["component"],rl=["className"],nl=["className"],al=function(r,e){var n=f.useContext(Gr);if(!n){var a=r.component,i=a===void 0?"div":a,o=it(r,tl);return f.createElement(i,Ie({},o,{ref:e}))}var s=n.className,l=it(n,rl),c=r.className,u=it(r,nl);return f.createElement(Gr.Provider,{value:null},f.createElement(mr,Ie({ref:e,className:Re(s,c)},l,u)))},ti=f.forwardRef(al);ti.displayName="RawItem";var il=["prefixCls","data","renderItem","renderRawItem","itemKey","itemWidth","ssr","style","className","maxCount","renderRest","renderRawRest","suffix","component","itemComponent","onVisibleChange"],ri="responsive",ni="invalidate";function ol(t){return"+ ".concat(t.length," ...")}function sl(t,r){var e=t.prefixCls,n=e===void 0?"rc-overflow":e,a=t.data,i=a===void 0?[]:a,o=t.renderItem,s=t.renderRawItem,l=t.itemKey,c=t.itemWidth,u=c===void 0?10:c,d=t.ssr,m=t.style,b=t.className,h=t.maxCount,g=t.renderRest,v=t.renderRawRest,y=t.suffix,p=t.component,w=p===void 0?"div":p,P=t.itemComponent,E=t.onVisibleChange,F=it(t,il),R=d==="full",M=el(),O=ur(M,null),x=q(O,2),V=x[0],D=x[1],_=V||0,k=ur(M,new Map),Z=q(k,2),z=Z[0],U=Z[1],ee=ur(M,0),te=q(ee,2),W=te[0],Se=te[1],ce=ur(M,0),Q=q(ce,2),T=Q[0],B=Q[1],we=ur(M,0),Ce=q(we,2),Fe=Ce[0],me=Ce[1],He=f.useState(null),Ue=q(He,2),de=Ue[0],xe=Ue[1],ve=f.useState(null),he=q(ve,2),Be=he[0],Ve=he[1],ie=f.useMemo(function(){return Be===null&&R?Number.MAX_SAFE_INTEGER:Be||0},[Be,V]),Ee=f.useState(!1),Ke=q(Ee,2),ge=Ke[0],Et=Ke[1],G="".concat(n,"-item"),Ye=Math.max(W,T),Te=h===ri,Y=i.length&&Te,Ae=h===ni,De=Y||typeof h=="number"&&i.length>h,Le=f.useMemo(function(){var A=i;return Y?V===null&&R?A=i:A=i.slice(0,Math.min(i.length,_/u)):typeof h=="number"&&(A=i.slice(0,h)),A},[i,u,V,h,Y]),st=f.useMemo(function(){return Y?i.slice(ie+1):i.slice(Le.length)},[i,Le,Y,ie]),re=f.useCallback(function(A,N){var L;return typeof l=="function"?l(A):(L=l&&(A==null?void 0:A[l]))!==null&&L!==void 0?L:N},[l]),ne=f.useCallback(o||function(A){return A},[o]);function oe(A,N,L){Be===A&&(N===void 0||N===de)||(Ve(A),L||(Et(A<i.length-1),E==null||E(A)),N!==void 0&&xe(N))}function _e(A,N){D(N.clientWidth)}function Nt(A,N){U(function(L){var le=new Map(L);return N===null?le.delete(A):le.set(A,N),le})}function lt(A,N){B(N),Se(T)}function Ne(A,N){me(N)}function Ge(A){return z.get(re(Le[A],A))}Me(function(){if(_&&typeof Ye=="number"&&Le){var A=Fe,N=Le.length,L=N-1;if(!N){oe(0,null);return}for(var le=0;le<N;le+=1){var je=Ge(le);if(R&&(je=je||0),je===void 0){oe(le-1,void 0,!0);break}if(A+=je,L===0&&A<=_||le===L-1&&A+Ge(L)<=_){oe(L,null);break}else if(A+Ye>_){oe(le-1,A-je-Fe+T);break}}y&&Ge(0)+Fe>_&&xe(null)}},[_,z,T,Fe,re,Le]);var kt=ge&&!!st.length,Pt={};de!==null&&Y&&(Pt={position:"absolute",left:de,top:0});var ut={prefixCls:G,responsive:Y,component:P,invalidate:Ae},tr=s?function(A,N){var L=re(A,N);return f.createElement(Gr.Provider,{key:L,value:S(S({},ut),{},{order:N,item:A,itemKey:L,registerSize:Nt,display:N<=ie})},s(A,N))}:function(A,N){var L=re(A,N);return f.createElement(mr,Ie({},ut,{order:N,key:L,item:A,renderItem:ne,itemKey:L,registerSize:Nt,display:N<=ie}))},ze={order:kt?ie:Number.MAX_SAFE_INTEGER,className:"".concat(G,"-rest"),registerSize:lt,display:kt},Pe=g||ol,Ot=v?f.createElement(Gr.Provider,{value:S(S({},ut),ze)},v(st)):f.createElement(mr,Ie({},ut,ze),typeof Pe=="function"?Pe(st):Pe),be=f.createElement(w,Ie({className:Re(!Ae&&n,b),style:m,ref:r},F),Le.map(tr),De?Ot:null,y&&f.createElement(mr,Ie({},ut,{responsive:Te,responsiveDisabled:!Y,order:ie,className:"".concat(G,"-suffix"),registerSize:Ne,display:!0,style:Pt}),y));return Te?f.createElement(yr,{onResize:_e,disabled:!Y},be):be}var Qr=f.forwardRef(sl);Qr.displayName="Overflow";Qr.Item=ti;Qr.RESPONSIVE=ri;Qr.INVALIDATE=ni;function ll(t){var r=t.prefixCls,e=t.align,n=t.arrow,a=t.arrowPos,i=n||{},o=i.className,s=i.content,l=a.x,c=l===void 0?0:l,u=a.y,d=u===void 0?0:u,m=f.useRef();if(!e||!e.points)return null;var b={position:"absolute"};if(e.autoArrow!==!1){var h=e.points[0],g=e.points[1],v=h[0],y=h[1],p=g[0],w=g[1];v===p||!["t","b"].includes(v)?b.top=d:v==="t"?b.top=0:b.bottom=0,y===w||!["l","r"].includes(y)?b.left=c:y==="l"?b.left=0:b.right=0}return f.createElement("div",{ref:m,className:Re("".concat(r,"-arrow"),o),style:b},s)}function ul(t){var r=t.prefixCls,e=t.open,n=t.zIndex,a=t.mask,i=t.motion;return a?f.createElement(_a,Ie({},i,{motionAppear:!0,visible:e,removeOnLeave:!0}),function(o){var s=o.className;return f.createElement("div",{style:{zIndex:n},className:Re("".concat(r,"-mask"),s)})}):null}var cl=f.memo(function(t){var r=t.children;return r},function(t,r){return r.cache}),fl=f.forwardRef(function(t,r){var e=t.popup,n=t.className,a=t.prefixCls,i=t.style,o=t.target,s=t.onVisibleChanged,l=t.open,c=t.keepDom,u=t.fresh,d=t.onClick,m=t.mask,b=t.arrow,h=t.arrowPos,g=t.align,v=t.motion,y=t.maskMotion,p=t.forceRender,w=t.getPopupContainer,P=t.autoDestroy,E=t.portal,F=t.zIndex,R=t.onMouseEnter,M=t.onMouseLeave,O=t.onPointerEnter,x=t.onPointerDownCapture,V=t.ready,D=t.offsetX,_=t.offsetY,k=t.offsetR,Z=t.offsetB,z=t.onAlign,U=t.onPrepare,ee=t.stretch,te=t.targetWidth,W=t.targetHeight,Se=typeof e=="function"?e():e,ce=l||c,Q=(w==null?void 0:w.length)>0,T=f.useState(!w||!Q),B=q(T,2),we=B[0],Ce=B[1];if(Me(function(){!we&&Q&&o&&Ce(!0)},[we,Q,o]),!we)return null;var Fe="auto",me={left:"-1000vw",top:"-1000vh",right:Fe,bottom:Fe};if(V||!l){var He,Ue=g.points,de=g.dynamicInset||((He=g._experimental)===null||He===void 0?void 0:He.dynamicInset),xe=de&&Ue[0][1]==="r",ve=de&&Ue[0][0]==="b";xe?(me.right=k,me.left=Fe):(me.left=D,me.right=Fe),ve?(me.bottom=Z,me.top=Fe):(me.top=_,me.bottom=Fe)}var he={};return ee&&(ee.includes("height")&&W?he.height=W:ee.includes("minHeight")&&W&&(he.minHeight=W),ee.includes("width")&&te?he.width=te:ee.includes("minWidth")&&te&&(he.minWidth=te)),l||(he.pointerEvents="none"),f.createElement(E,{open:p||ce,getContainer:w&&function(){return w(o)},autoDestroy:P},f.createElement(ul,{prefixCls:a,open:l,zIndex:F,mask:m,motion:y}),f.createElement(yr,{onResize:z,disabled:!l},function(Be){return f.createElement(_a,Ie({motionAppear:!0,motionEnter:!0,motionLeave:!0,removeOnLeave:!1,forceRender:p,leavedClassName:"".concat(a,"-hidden")},v,{onAppearPrepare:U,onEnterPrepare:U,visible:l,onVisibleChanged:function(ie){var Ee;v==null||(Ee=v.onVisibleChanged)===null||Ee===void 0||Ee.call(v,ie),s(ie)}}),function(Ve,ie){var Ee=Ve.className,Ke=Ve.style,ge=Re(a,Ee,n);return f.createElement("div",{ref:hi(Be,r,ie),className:ge,style:S(S(S(S({"--arrow-x":"".concat(h.x||0,"px"),"--arrow-y":"".concat(h.y||0,"px")},me),he),Ke),{},{boxSizing:"border-box",zIndex:F},i),onMouseEnter:R,onMouseLeave:M,onPointerEnter:O,onClick:d,onPointerDownCapture:x},b&&f.createElement(ll,{prefixCls:a,arrow:b,arrowPos:h,align:g}),f.createElement(cl,{cache:!l&&!u},Se))})}))}),dl=f.forwardRef(function(t,r){var e=t.children,n=t.getTriggerDOMNode,a=Wn(e),i=f.useCallback(function(s){gi(r,n?n(s):s)},[n]),o=qn(i,Sa(e));return a?f.cloneElement(e,{ref:o}):e}),ya=f.createContext(null);function ba(t){return t?Array.isArray(t)?t:[t]:[]}function vl(t,r,e,n){return f.useMemo(function(){var a=ba(e??r),i=ba(n??r),o=new Set(a),s=new Set(i);return t&&(o.has("hover")&&(o.delete("hover"),o.add("click")),s.has("hover")&&(s.delete("hover"),s.add("click"))),[o,s]},[t,r,e,n])}function ml(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],e=arguments.length>2?arguments[2]:void 0;return e?t[0]===r[0]:t[0]===r[0]&&t[1]===r[1]}function hl(t,r,e,n){for(var a=e.points,i=Object.keys(t),o=0;o<i.length;o+=1){var s,l=i[o];if(ml((s=t[l])===null||s===void 0?void 0:s.points,a,n))return"".concat(r,"-placement-").concat(l)}return""}function wa(t,r,e,n){return r||(e?{motionName:"".concat(t,"-").concat(e)}:n?{motionName:n}:null)}function Cr(t){return t.ownerDocument.defaultView}function Dn(t){for(var r=[],e=t==null?void 0:t.parentElement,n=["hidden","scroll","clip","auto"];e;){var a=Cr(e).getComputedStyle(e),i=a.overflowX,o=a.overflowY,s=a.overflow;[i,o,s].some(function(l){return n.includes(l)})&&r.push(e),e=e.parentElement}return r}function gr(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1;return Number.isNaN(t)?r:t}function cr(t){return gr(parseFloat(t),0)}function Ca(t,r){var e=S({},t);return(r||[]).forEach(function(n){if(!(n instanceof HTMLBodyElement||n instanceof HTMLHtmlElement)){var a=Cr(n).getComputedStyle(n),i=a.overflow,o=a.overflowClipMargin,s=a.borderTopWidth,l=a.borderBottomWidth,c=a.borderLeftWidth,u=a.borderRightWidth,d=n.getBoundingClientRect(),m=n.offsetHeight,b=n.clientHeight,h=n.offsetWidth,g=n.clientWidth,v=cr(s),y=cr(l),p=cr(c),w=cr(u),P=gr(Math.round(d.width/h*1e3)/1e3),E=gr(Math.round(d.height/m*1e3)/1e3),F=(h-g-p-w)*P,R=(m-b-v-y)*E,M=v*E,O=y*E,x=p*P,V=w*P,D=0,_=0;if(i==="clip"){var k=cr(o);D=k*P,_=k*E}var Z=d.x+x-D,z=d.y+M-_,U=Z+d.width+2*D-x-V-F,ee=z+d.height+2*_-M-O-R;e.left=Math.max(e.left,Z),e.top=Math.max(e.top,z),e.right=Math.min(e.right,U),e.bottom=Math.min(e.bottom,ee)}}),e}function Fa(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,e="".concat(r),n=e.match(/^(.*)\%$/);return n?t*(parseFloat(n[1])/100):parseFloat(e)}function Ea(t,r){var e=r||[],n=q(e,2),a=n[0],i=n[1];return[Fa(t.width,a),Fa(t.height,i)]}function Pa(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";return[t[0],t[1]]}function Kt(t,r){var e=r[0],n=r[1],a,i;return e==="t"?i=t.y:e==="b"?i=t.y+t.height:i=t.y+t.height/2,n==="l"?a=t.x:n==="r"?a=t.x+t.width:a=t.x+t.width/2,{x:a,y:i}}function pt(t,r){var e={t:"b",b:"t",l:"r",r:"l"};return t.map(function(n,a){return a===r?e[n]||"c":n}).join("")}function gl(t,r,e,n,a,i,o){var s=f.useState({ready:!1,offsetX:0,offsetY:0,offsetR:0,offsetB:0,arrowX:0,arrowY:0,scaleX:1,scaleY:1,align:a[n]||{}}),l=q(s,2),c=l[0],u=l[1],d=f.useRef(0),m=f.useMemo(function(){return r?Dn(r):[]},[r]),b=f.useRef({}),h=function(){b.current={}};t||h();var g=at(function(){if(r&&e&&t){let Qe=function(zt,ht){var gt=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Ye,lr=T.x+zt,Tr=T.y+ht,fn=lr+xe,dn=Tr+de,vn=Math.max(lr,gt.left),$=Math.max(Tr,gt.top),I=Math.min(fn,gt.right),ae=Math.min(dn,gt.bottom);return Math.max(0,(I-vn)*(ae-$))},Ir=function(){Rt=T.y+L,St=Rt+de,ct=T.x+N,Tt=ct+xe};var p,w,P,E,F=r,R=F.ownerDocument,M=Cr(F),O=M.getComputedStyle(F),x=O.width,V=O.height,D=O.position,_=F.style.left,k=F.style.top,Z=F.style.right,z=F.style.bottom,U=F.style.overflow,ee=S(S({},a[n]),i),te=R.createElement("div");(p=F.parentElement)===null||p===void 0||p.appendChild(te),te.style.left="".concat(F.offsetLeft,"px"),te.style.top="".concat(F.offsetTop,"px"),te.style.position=D,te.style.height="".concat(F.offsetHeight,"px"),te.style.width="".concat(F.offsetWidth,"px"),F.style.left="0",F.style.top="0",F.style.right="auto",F.style.bottom="auto",F.style.overflow="hidden";var W;if(Array.isArray(e))W={x:e[0],y:e[1],width:0,height:0};else{var Se,ce,Q=e.getBoundingClientRect();Q.x=(Se=Q.x)!==null&&Se!==void 0?Se:Q.left,Q.y=(ce=Q.y)!==null&&ce!==void 0?ce:Q.top,W={x:Q.x,y:Q.y,width:Q.width,height:Q.height}}var T=F.getBoundingClientRect();T.x=(w=T.x)!==null&&w!==void 0?w:T.left,T.y=(P=T.y)!==null&&P!==void 0?P:T.top;var B=R.documentElement,we=B.clientWidth,Ce=B.clientHeight,Fe=B.scrollWidth,me=B.scrollHeight,He=B.scrollTop,Ue=B.scrollLeft,de=T.height,xe=T.width,ve=W.height,he=W.width,Be={left:0,top:0,right:we,bottom:Ce},Ve={left:-Ue,top:-He,right:Fe-Ue,bottom:me-He},ie=ee.htmlRegion,Ee="visible",Ke="visibleFirst";ie!=="scroll"&&ie!==Ke&&(ie=Ee);var ge=ie===Ke,Et=Ca(Ve,m),G=Ca(Be,m),Ye=ie===Ee?G:Et,Te=ge?G:Ye;F.style.left="auto",F.style.top="auto",F.style.right="0",F.style.bottom="0";var Y=F.getBoundingClientRect();F.style.left=_,F.style.top=k,F.style.right=Z,F.style.bottom=z,F.style.overflow=U,(E=F.parentElement)===null||E===void 0||E.removeChild(te);var Ae=gr(Math.round(xe/parseFloat(x)*1e3)/1e3),De=gr(Math.round(de/parseFloat(V)*1e3)/1e3);if(Ae===0||De===0||Pn(e)&&!Ii(e))return;var Le=ee.offset,st=ee.targetOffset,re=Ea(T,Le),ne=q(re,2),oe=ne[0],_e=ne[1],Nt=Ea(W,st),lt=q(Nt,2),Ne=lt[0],Ge=lt[1];W.x-=Ne,W.y-=Ge;var kt=ee.points||[],Pt=q(kt,2),ut=Pt[0],tr=Pt[1],ze=Pa(tr),Pe=Pa(ut),Ot=Kt(W,ze),be=Kt(T,Pe),A=S({},ee),N=Ot.x-be.x+oe,L=Ot.y-be.y+_e,le=Qe(N,L),je=Qe(N,L,G),rr=Kt(W,["t","l"]),Ze=Kt(T,["t","l"]),Fr=Kt(W,["b","r"]),nr=Kt(T,["b","r"]),mt=ee.overflow||{},Er=mt.adjustX,Jr=mt.adjustY,ar=mt.shiftX,It=mt.shiftY,ir=function(ht){return typeof ht=="boolean"?ht:ht>=0},Rt,St,ct,Tt;Ir();var xt=ir(Jr),Pr=Pe[0]===ze[0];if(xt&&Pe[0]==="t"&&(St>Te.bottom||b.current.bt)){var Oe=L;Pr?Oe-=de-ve:Oe=rr.y-nr.y-_e;var Or=Qe(N,Oe),Xr=Qe(N,Oe,G);Or>le||Or===le&&(!ge||Xr>=je)?(b.current.bt=!0,L=Oe,_e=-_e,A.points=[pt(Pe,0),pt(ze,0)]):b.current.bt=!1}if(xt&&Pe[0]==="b"&&(Rt<Te.top||b.current.tb)){var Mt=L;Pr?Mt+=de-ve:Mt=Fr.y-Ze.y-_e;var Rr=Qe(N,Mt),Yr=Qe(N,Mt,G);Rr>le||Rr===le&&(!ge||Yr>=je)?(b.current.tb=!0,L=Mt,_e=-_e,A.points=[pt(Pe,0),pt(ze,0)]):b.current.tb=!1}var Sr=ir(Er),xr=Pe[1]===ze[1];if(Sr&&Pe[1]==="l"&&(Tt>Te.right||b.current.rl)){var $t=N;xr?$t-=xe-he:$t=rr.x-nr.x-oe;var Mr=Qe($t,L),At=Qe($t,L,G);Mr>le||Mr===le&&(!ge||At>=je)?(b.current.rl=!0,N=$t,oe=-oe,A.points=[pt(Pe,1),pt(ze,1)]):b.current.rl=!1}if(Sr&&Pe[1]==="r"&&(ct<Te.left||b.current.lr)){var ft=N;xr?ft+=xe-he:ft=Fr.x-Ze.x-oe;var $r=Qe(ft,L),Vr=Qe(ft,L,G);$r>le||$r===le&&(!ge||Vr>=je)?(b.current.lr=!0,N=ft,oe=-oe,A.points=[pt(Pe,1),pt(ze,1)]):b.current.lr=!1}Ir();var tt=ar===!0?0:ar;typeof tt=="number"&&(ct<G.left&&(N-=ct-G.left-oe,W.x+he<G.left+tt&&(N+=W.x-G.left+he-tt)),Tt>G.right&&(N-=Tt-G.right-oe,W.x>G.right-tt&&(N+=W.x-G.right+tt)));var et=It===!0?0:It;typeof et=="number"&&(Rt<G.top&&(L-=Rt-G.top-_e,W.y+ve<G.top+et&&(L+=W.y-G.top+ve-et)),St>G.bottom&&(L-=St-G.bottom-_e,W.y>G.bottom-et&&(L+=W.y-G.bottom+et)));var Dt=T.x+N,Lt=Dt+xe,rt=T.y+L,en=rt+de,_r=W.x,tn=_r+he,or=W.y,rn=or+ve,nn=Math.max(Dt,_r),an=Math.min(Lt,tn),Nr=(nn+an)/2,on=Nr-Dt,sn=Math.max(rt,or),kr=Math.min(en,rn),ln=(sn+kr)/2,un=ln-rt;o==null||o(r,A);var sr=Y.right-T.x-(N+T.width),nt=Y.bottom-T.y-(L+T.height);Ae===1&&(N=Math.round(N),sr=Math.round(sr)),De===1&&(L=Math.round(L),nt=Math.round(nt));var cn={ready:!0,offsetX:N/Ae,offsetY:L/De,offsetR:sr/Ae,offsetB:nt/De,arrowX:on/Ae,arrowY:un/De,scaleX:Ae,scaleY:De,align:A};u(cn)}}),v=function(){d.current+=1;var w=d.current;Promise.resolve().then(function(){d.current===w&&g()})},y=function(){u(function(w){return S(S({},w),{},{ready:!1})})};return Me(y,[n]),Me(function(){t||y()},[t]),[c.ready,c.offsetX,c.offsetY,c.offsetR,c.offsetB,c.arrowX,c.arrowY,c.scaleX,c.scaleY,c.align,v]}function pl(t,r,e,n,a){Me(function(){if(t&&r&&e){let d=function(){n(),a()};var i=r,o=e,s=Dn(i),l=Dn(o),c=Cr(o),u=new Set([c].concat(j(s),j(l)));return u.forEach(function(m){m.addEventListener("scroll",d,{passive:!0})}),c.addEventListener("resize",d,{passive:!0}),n(),function(){u.forEach(function(m){m.removeEventListener("scroll",d),c.removeEventListener("resize",d)})}}},[t,r,e])}function yl(t,r,e,n,a,i,o,s){var l=f.useRef(t);l.current=t;var c=f.useRef(!1);f.useEffect(function(){if(r&&n&&(!a||i)){var d=function(){c.current=!1},m=function(v){var y;l.current&&!o(((y=v.composedPath)===null||y===void 0||(y=y.call(v))===null||y===void 0?void 0:y[0])||v.target)&&!c.current&&s(!1)},b=Cr(n);b.addEventListener("pointerdown",d,!0),b.addEventListener("mousedown",m,!0),b.addEventListener("contextmenu",m,!0);var h=On(e);return h&&(h.addEventListener("mousedown",m,!0),h.addEventListener("contextmenu",m,!0)),function(){b.removeEventListener("pointerdown",d,!0),b.removeEventListener("mousedown",m,!0),b.removeEventListener("contextmenu",m,!0),h&&(h.removeEventListener("mousedown",m,!0),h.removeEventListener("contextmenu",m,!0))}}},[r,e,n,a,i]);function u(){c.current=!0}return u}var bl=["prefixCls","children","action","showAction","hideAction","popupVisible","defaultPopupVisible","onPopupVisibleChange","afterPopupVisibleChange","mouseEnterDelay","mouseLeaveDelay","focusDelay","blurDelay","mask","maskClosable","getPopupContainer","forceRender","autoDestroy","destroyPopupOnHide","popup","popupClassName","popupStyle","popupPlacement","builtinPlacements","popupAlign","zIndex","stretch","getPopupClassNameFromAlign","fresh","alignPoint","onPopupClick","onPopupAlign","arrow","popupMotion","maskMotion","popupTransitionName","popupAnimation","maskTransitionName","maskAnimation","className","getTriggerDOMNode"];function wl(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Ba,r=f.forwardRef(function(e,n){var a=e.prefixCls,i=a===void 0?"rc-trigger-popup":a,o=e.children,s=e.action,l=s===void 0?"hover":s,c=e.showAction,u=e.hideAction,d=e.popupVisible,m=e.defaultPopupVisible,b=e.onPopupVisibleChange,h=e.afterPopupVisibleChange,g=e.mouseEnterDelay,v=e.mouseLeaveDelay,y=v===void 0?.1:v,p=e.focusDelay,w=e.blurDelay,P=e.mask,E=e.maskClosable,F=E===void 0?!0:E,R=e.getPopupContainer,M=e.forceRender,O=e.autoDestroy,x=e.destroyPopupOnHide,V=e.popup,D=e.popupClassName,_=e.popupStyle,k=e.popupPlacement,Z=e.builtinPlacements,z=Z===void 0?{}:Z,U=e.popupAlign,ee=e.zIndex,te=e.stretch,W=e.getPopupClassNameFromAlign,Se=e.fresh,ce=e.alignPoint,Q=e.onPopupClick,T=e.onPopupAlign,B=e.arrow,we=e.popupMotion,Ce=e.maskMotion,Fe=e.popupTransitionName,me=e.popupAnimation,He=e.maskTransitionName,Ue=e.maskAnimation,de=e.className,xe=e.getTriggerDOMNode,ve=it(e,bl),he=O||x||!1,Be=f.useState(!1),Ve=q(Be,2),ie=Ve[0],Ee=Ve[1];Me(function(){Ee(Qs())},[]);var Ke=f.useRef({}),ge=f.useContext(ya),Et=f.useMemo(function(){return{registerSubPopup:function(I,ae){Ke.current[I]=ae,ge==null||ge.registerSubPopup(I,ae)}}},[ge]),G=Ka(),Ye=f.useState(null),Te=q(Ye,2),Y=Te[0],Ae=Te[1],De=f.useRef(null),Le=at(function($){De.current=$,Pn($)&&Y!==$&&Ae($),ge==null||ge.registerSubPopup(G,$)}),st=f.useState(null),re=q(st,2),ne=re[0],oe=re[1],_e=f.useRef(null),Nt=at(function($){Pn($)&&ne!==$&&(oe($),_e.current=$)}),lt=f.Children.only(o),Ne=(lt==null?void 0:lt.props)||{},Ge={},kt=at(function($){var I,ae,pe=ne;return(pe==null?void 0:pe.contains($))||((I=On(pe))===null||I===void 0?void 0:I.host)===$||$===pe||(Y==null?void 0:Y.contains($))||((ae=On(Y))===null||ae===void 0?void 0:ae.host)===$||$===Y||Object.values(Ke.current).some(function(ue){return(ue==null?void 0:ue.contains($))||$===ue})}),Pt=wa(i,we,me,Fe),ut=wa(i,Ce,Ue,He),tr=f.useState(m||!1),ze=q(tr,2),Pe=ze[0],Ot=ze[1],be=d??Pe,A=at(function($){d===void 0&&Ot($)});Me(function(){Ot(d||!1)},[d]);var N=f.useRef(be);N.current=be;var L=f.useRef([]);L.current=[];var le=at(function($){var I;A($),((I=L.current[L.current.length-1])!==null&&I!==void 0?I:be)!==$&&(L.current.push($),b==null||b($))}),je=f.useRef(),rr=function(){clearTimeout(je.current)},Ze=function(I){var ae=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;rr(),ae===0?le(I):je.current=setTimeout(function(){le(I)},ae*1e3)};f.useEffect(function(){return rr},[]);var Fr=f.useState(!1),nr=q(Fr,2),mt=nr[0],Er=nr[1];Me(function($){(!$||be)&&Er(!0)},[be]);var Jr=f.useState(null),ar=q(Jr,2),It=ar[0],ir=ar[1],Rt=f.useState(null),St=q(Rt,2),ct=St[0],Tt=St[1],xt=function(I){Tt([I.clientX,I.clientY])},Pr=gl(be,Y,ce&&ct!==null?ct:ne,k,z,U,T),Oe=q(Pr,11),Or=Oe[0],Xr=Oe[1],Mt=Oe[2],Rr=Oe[3],Yr=Oe[4],Sr=Oe[5],xr=Oe[6],$t=Oe[7],Mr=Oe[8],At=Oe[9],ft=Oe[10],$r=vl(ie,l,c,u),Vr=q($r,2),tt=Vr[0],et=Vr[1],Dt=tt.has("click"),Lt=et.has("click")||et.has("contextMenu"),rt=at(function(){mt||ft()}),en=function(){N.current&&ce&&Lt&&Ze(!1)};pl(be,ne,Y,rt,en),Me(function(){rt()},[ct,k]),Me(function(){be&&!(z!=null&&z[k])&&rt()},[JSON.stringify(U)]);var _r=f.useMemo(function(){var $=hl(z,i,At,ce);return Re($,W==null?void 0:W(At))},[At,W,z,i,ce]);f.useImperativeHandle(n,function(){return{nativeElement:_e.current,popupElement:De.current,forceAlign:rt}});var tn=f.useState(0),or=q(tn,2),rn=or[0],nn=or[1],an=f.useState(0),Nr=q(an,2),on=Nr[0],sn=Nr[1],kr=function(){if(te&&ne){var I=ne.getBoundingClientRect();nn(I.width),sn(I.height)}},ln=function(){kr(),rt()},un=function(I){Er(!1),ft(),h==null||h(I)},sr=function(){return new Promise(function(I){kr(),ir(function(){return I})})};Me(function(){It&&(ft(),It(),ir(null))},[It]);function nt($,I,ae,pe){Ge[$]=function(ue){var Ar;pe==null||pe(ue),Ze(I,ae);for(var mn=arguments.length,Bn=new Array(mn>1?mn-1:0),Dr=1;Dr<mn;Dr++)Bn[Dr-1]=arguments[Dr];(Ar=Ne[$])===null||Ar===void 0||Ar.call.apply(Ar,[Ne,ue].concat(Bn))}}(Dt||Lt)&&(Ge.onClick=function($){var I;N.current&&Lt?Ze(!1):!N.current&&Dt&&(xt($),Ze(!0));for(var ae=arguments.length,pe=new Array(ae>1?ae-1:0),ue=1;ue<ae;ue++)pe[ue-1]=arguments[ue];(I=Ne.onClick)===null||I===void 0||I.call.apply(I,[Ne,$].concat(pe))});var cn=yl(be,Lt,ne,Y,P,F,kt,Ze),Qe=tt.has("hover"),Ir=et.has("hover"),zt,ht;Qe&&(nt("onMouseEnter",!0,g,function($){xt($)}),nt("onPointerEnter",!0,g,function($){xt($)}),zt=function(I){(be||mt)&&Y!==null&&Y!==void 0&&Y.contains(I.target)&&Ze(!0,g)},ce&&(Ge.onMouseMove=function($){var I;(I=Ne.onMouseMove)===null||I===void 0||I.call(Ne,$)})),Ir&&(nt("onMouseLeave",!1,y),nt("onPointerLeave",!1,y),ht=function(){Ze(!1,y)}),tt.has("focus")&&nt("onFocus",!0,p),et.has("focus")&&nt("onBlur",!1,w),tt.has("contextMenu")&&(Ge.onContextMenu=function($){var I;N.current&&et.has("contextMenu")?Ze(!1):(xt($),Ze(!0)),$.preventDefault();for(var ae=arguments.length,pe=new Array(ae>1?ae-1:0),ue=1;ue<ae;ue++)pe[ue-1]=arguments[ue];(I=Ne.onContextMenu)===null||I===void 0||I.call.apply(I,[Ne,$].concat(pe))}),de&&(Ge.className=Re(Ne.className,de));var gt=S(S({},Ne),Ge),lr={},Tr=["onContextMenu","onClick","onMouseDown","onTouchStart","onMouseEnter","onMouseLeave","onFocus","onBlur"];Tr.forEach(function($){ve[$]&&(lr[$]=function(){for(var I,ae=arguments.length,pe=new Array(ae),ue=0;ue<ae;ue++)pe[ue]=arguments[ue];(I=gt[$])===null||I===void 0||I.call.apply(I,[gt].concat(pe)),ve[$].apply(ve,pe)})});var fn=f.cloneElement(lt,S(S({},gt),lr)),dn={x:Sr,y:xr},vn=B?S({},B!==!0?B:{}):null;return f.createElement(f.Fragment,null,f.createElement(yr,{disabled:!be,ref:Nt,onResize:ln},f.createElement(dl,{getTriggerDOMNode:xe},fn)),f.createElement(ya.Provider,{value:Et},f.createElement(fl,{portal:t,ref:Le,prefixCls:i,popup:V,className:Re(D,_r),style:_,target:ne,onMouseEnter:zt,onMouseLeave:ht,onPointerEnter:zt,zIndex:ee,open:be,keepDom:mt,fresh:Se,onClick:Q,onPointerDownCapture:cn,mask:P,motion:Pt,maskMotion:ut,onVisibleChanged:un,onPrepare:sr,forceRender:M,autoDestroy:he,getPopupContainer:R,align:At,arrow:vn,arrowPos:dn,ready:Or,offsetX:Xr,offsetY:Mt,offsetR:Rr,offsetB:Yr,onAlign:rt,stretch:te,targetWidth:rn/$t,targetHeight:on/Mr})))});return r}const Cl=wl(Ba);var Fl={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"}}]},name:"down",theme:"outlined"},El=function(r,e){return f.createElement(pi,Ie({},r,{ref:e,icon:Fl}))},du=f.forwardRef(El);const Pl=(t,r)=>{typeof(t==null?void 0:t.addEventListener)<"u"?t.addEventListener("change",r):typeof(t==null?void 0:t.addListener)<"u"&&t.addListener(r)},Ol=(t,r)=>{typeof(t==null?void 0:t.removeEventListener)<"u"?t.removeEventListener("change",r):typeof(t==null?void 0:t.removeListener)<"u"&&t.removeListener(r)},Rl=["xxl","xl","lg","md","sm","xs"],Sl=t=>({xs:`(max-width: ${t.screenXSMax}px)`,sm:`(min-width: ${t.screenSM}px)`,md:`(min-width: ${t.screenMD}px)`,lg:`(min-width: ${t.screenLG}px)`,xl:`(min-width: ${t.screenXL}px)`,xxl:`(min-width: ${t.screenXXL}px)`}),xl=t=>{const r=t,e=[].concat(Rl).reverse();return e.forEach((n,a)=>{const i=n.toUpperCase(),o=`screen${i}Min`,s=`screen${i}`;if(!(r[o]<=r[s]))throw new Error(`${o}<=${s} fails : !(${r[o]}<=${r[s]})`);if(a<e.length-1){const l=`screen${i}Max`;if(!(r[s]<=r[l]))throw new Error(`${s}<=${l} fails : !(${r[s]}<=${r[l]})`);const u=`screen${e[a+1].toUpperCase()}Min`;if(!(r[l]<=r[u]))throw new Error(`${l}<=${u} fails : !(${r[l]}<=${r[u]})`)}}),t},Ml=()=>{const[,t]=Na(),r=Sl(xl(t));return qr.useMemo(()=>{const e=new Map;let n=-1,a={};return{responsiveMap:r,matchHandlers:{},dispatch(i){return a=i,e.forEach(o=>o(a)),e.size>=1},subscribe(i){return e.size||this.register(),n+=1,e.set(n,i),i(a),n},unsubscribe(i){e.delete(i),e.size||this.unregister()},register(){Object.entries(r).forEach(i=>{let[o,s]=i;const l=u=>{let{matches:d}=u;this.dispatch(Object.assign(Object.assign({},a),{[o]:d}))},c=window.matchMedia(s);Pl(c,l),this.matchHandlers[s]={mql:c,listener:l},l(c)})},unregister(){Object.values(r).forEach(i=>{const o=this.matchHandlers[i];Ol(o==null?void 0:o.mql,o==null?void 0:o.listener)}),e.clear()}}},[t])};function $l(){const[,t]=f.useReducer(r=>r+1,0);return t}function vu(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const e=f.useRef(r),n=$l(),a=Ml();return Me(()=>{const i=a.subscribe(o=>{e.current=o,t&&n()});return()=>a.unsubscribe(i)},[]),e.current}function ai(t){var r=t.children,e=t.prefixCls,n=t.id,a=t.overlayInnerStyle,i=t.bodyClassName,o=t.className,s=t.style;return f.createElement("div",{className:Re("".concat(e,"-content"),o),style:s},f.createElement("div",{className:Re("".concat(e,"-inner"),i),id:n,role:"tooltip",style:a},typeof r=="function"?r():r))}var Gt={shiftX:64,adjustY:1},Zt={adjustX:1,shiftY:!0},Xe=[0,0],Vl={left:{points:["cr","cl"],overflow:Zt,offset:[-4,0],targetOffset:Xe},right:{points:["cl","cr"],overflow:Zt,offset:[4,0],targetOffset:Xe},top:{points:["bc","tc"],overflow:Gt,offset:[0,-4],targetOffset:Xe},bottom:{points:["tc","bc"],overflow:Gt,offset:[0,4],targetOffset:Xe},topLeft:{points:["bl","tl"],overflow:Gt,offset:[0,-4],targetOffset:Xe},leftTop:{points:["tr","tl"],overflow:Zt,offset:[-4,0],targetOffset:Xe},topRight:{points:["br","tr"],overflow:Gt,offset:[0,-4],targetOffset:Xe},rightTop:{points:["tl","tr"],overflow:Zt,offset:[4,0],targetOffset:Xe},bottomRight:{points:["tr","br"],overflow:Gt,offset:[0,4],targetOffset:Xe},rightBottom:{points:["bl","br"],overflow:Zt,offset:[4,0],targetOffset:Xe},bottomLeft:{points:["tl","bl"],overflow:Gt,offset:[0,4],targetOffset:Xe},leftBottom:{points:["br","bl"],overflow:Zt,offset:[-4,0],targetOffset:Xe}},_l=["overlayClassName","trigger","mouseEnterDelay","mouseLeaveDelay","overlayStyle","prefixCls","children","onVisibleChange","afterVisibleChange","transitionName","animation","motion","placement","align","destroyTooltipOnHide","defaultVisible","getTooltipContainer","overlayInnerStyle","arrowContent","overlay","id","showArrow","classNames","styles"],Nl=function(r,e){var n=r.overlayClassName,a=r.trigger,i=a===void 0?["hover"]:a,o=r.mouseEnterDelay,s=o===void 0?0:o,l=r.mouseLeaveDelay,c=l===void 0?.1:l,u=r.overlayStyle,d=r.prefixCls,m=d===void 0?"rc-tooltip":d,b=r.children,h=r.onVisibleChange,g=r.afterVisibleChange,v=r.transitionName,y=r.animation,p=r.motion,w=r.placement,P=w===void 0?"right":w,E=r.align,F=E===void 0?{}:E,R=r.destroyTooltipOnHide,M=R===void 0?!1:R,O=r.defaultVisible,x=r.getTooltipContainer,V=r.overlayInnerStyle;r.arrowContent;var D=r.overlay,_=r.id,k=r.showArrow,Z=k===void 0?!0:k,z=r.classNames,U=r.styles,ee=it(r,_l),te=Ka(_),W=f.useRef(null);f.useImperativeHandle(e,function(){return W.current});var Se=S({},ee);"visible"in r&&(Se.popupVisible=r.visible);var ce=function(){return f.createElement(ai,{key:"content",prefixCls:m,id:te,bodyClassName:z==null?void 0:z.body,overlayInnerStyle:S(S({},V),U==null?void 0:U.body)},D)},Q=function(){var B=f.Children.only(b),we=(B==null?void 0:B.props)||{},Ce=S(S({},we),{},{"aria-describedby":D?te:null});return f.cloneElement(b,Ce)};return f.createElement(Cl,Ie({popupClassName:Re(n,z==null?void 0:z.root),prefixCls:m,popup:ce,action:i,builtinPlacements:Vl,popupPlacement:P,ref:W,popupAlign:F,getPopupContainer:x,onPopupVisibleChange:h,afterPopupVisibleChange:g,popupTransitionName:v,popupAnimation:y,popupMotion:p,defaultPopupVisible:O,autoDestroy:M,mouseLeaveDelay:c,popupStyle:S(S({},u),U==null?void 0:U.root),mouseEnterDelay:s,arrow:Z},Se),Q())};const kl=f.forwardRef(Nl);function Il(t){const{sizePopupArrow:r,borderRadiusXS:e,borderRadiusOuter:n}=t,a=r/2,i=0,o=a,s=n*1/Math.sqrt(2),l=a-n*(1-1/Math.sqrt(2)),c=a-e*(1/Math.sqrt(2)),u=n*(Math.sqrt(2)-1)+e*(1/Math.sqrt(2)),d=2*a-c,m=u,b=2*a-s,h=l,g=2*a-i,v=o,y=a*Math.sqrt(2)+n*(Math.sqrt(2)-2),p=n*(Math.sqrt(2)-1),w=`polygon(${p}px 100%, 50% ${p}px, ${2*a-p}px 100%, ${p}px 100%)`,P=`path('M ${i} ${o} A ${n} ${n} 0 0 0 ${s} ${l} L ${c} ${u} A ${e} ${e} 0 0 1 ${d} ${m} L ${b} ${h} A ${n} ${n} 0 0 0 ${g} ${v} Z')`;return{arrowShadowWidth:y,arrowPath:P,arrowPolygon:w}}const Tl=(t,r,e)=>{const{sizePopupArrow:n,arrowPolygon:a,arrowPath:i,arrowShadowWidth:o,borderRadiusXS:s,calc:l}=t;return{pointerEvents:"none",width:n,height:n,overflow:"hidden","&::before":{position:"absolute",bottom:0,insetInlineStart:0,width:n,height:l(n).div(2).equal(),background:r,clipPath:{_multi_value_:!0,value:[a,i]},content:'""'},"&::after":{content:'""',position:"absolute",width:o,height:o,bottom:0,insetInline:0,margin:"auto",borderRadius:{_skip_check_:!0,value:`0 0 ${hr(s)} 0`},transform:"translateY(50%) rotate(-135deg)",boxShadow:e,zIndex:0,background:"transparent"}}},ii=8;function oi(t){const{contentRadius:r,limitVerticalRadius:e}=t,n=r>12?r+2:12;return{arrowOffsetHorizontal:n,arrowOffsetVertical:e?ii:n}}function zr(t,r){return t?r:{}}function Al(t,r,e){const{componentCls:n,boxShadowPopoverArrow:a,arrowOffsetVertical:i,arrowOffsetHorizontal:o}=t,{arrowDistance:s=0,arrowPlacement:l={left:!0,right:!0,top:!0,bottom:!0}}=e||{};return{[n]:Object.assign(Object.assign(Object.assign(Object.assign({[`${n}-arrow`]:[Object.assign(Object.assign({position:"absolute",zIndex:1,display:"block"},Tl(t,r,a)),{"&:before":{background:r}})]},zr(!!l.top,{[[`&-placement-top > ${n}-arrow`,`&-placement-topLeft > ${n}-arrow`,`&-placement-topRight > ${n}-arrow`].join(",")]:{bottom:s,transform:"translateY(100%) rotate(180deg)"},[`&-placement-top > ${n}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(100%) rotate(180deg)"},"&-placement-topLeft":{"--arrow-offset-horizontal":o,[`> ${n}-arrow`]:{left:{_skip_check_:!0,value:o}}},"&-placement-topRight":{"--arrow-offset-horizontal":`calc(100% - ${hr(o)})`,[`> ${n}-arrow`]:{right:{_skip_check_:!0,value:o}}}})),zr(!!l.bottom,{[[`&-placement-bottom > ${n}-arrow`,`&-placement-bottomLeft > ${n}-arrow`,`&-placement-bottomRight > ${n}-arrow`].join(",")]:{top:s,transform:"translateY(-100%)"},[`&-placement-bottom > ${n}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(-100%)"},"&-placement-bottomLeft":{"--arrow-offset-horizontal":o,[`> ${n}-arrow`]:{left:{_skip_check_:!0,value:o}}},"&-placement-bottomRight":{"--arrow-offset-horizontal":`calc(100% - ${hr(o)})`,[`> ${n}-arrow`]:{right:{_skip_check_:!0,value:o}}}})),zr(!!l.left,{[[`&-placement-left > ${n}-arrow`,`&-placement-leftTop > ${n}-arrow`,`&-placement-leftBottom > ${n}-arrow`].join(",")]:{right:{_skip_check_:!0,value:s},transform:"translateX(100%) rotate(90deg)"},[`&-placement-left > ${n}-arrow`]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(100%) rotate(90deg)"},[`&-placement-leftTop > ${n}-arrow`]:{top:i},[`&-placement-leftBottom > ${n}-arrow`]:{bottom:i}})),zr(!!l.right,{[[`&-placement-right > ${n}-arrow`,`&-placement-rightTop > ${n}-arrow`,`&-placement-rightBottom > ${n}-arrow`].join(",")]:{left:{_skip_check_:!0,value:s},transform:"translateX(-100%) rotate(-90deg)"},[`&-placement-right > ${n}-arrow`]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(-100%) rotate(-90deg)"},[`&-placement-rightTop > ${n}-arrow`]:{top:i},[`&-placement-rightBottom > ${n}-arrow`]:{bottom:i}}))}}function Dl(t,r,e,n){if(n===!1)return{adjustX:!1,adjustY:!1};const a=n&&typeof n=="object"?n:{},i={};switch(t){case"top":case"bottom":i.shiftX=r.arrowOffsetHorizontal*2+e,i.shiftY=!0,i.adjustY=!0;break;case"left":case"right":i.shiftY=r.arrowOffsetVertical*2+e,i.shiftX=!0,i.adjustX=!0;break}const o=Object.assign(Object.assign({},i),a);return o.shiftX||(o.adjustX=!0),o.shiftY||(o.adjustY=!0),o}const Oa={left:{points:["cr","cl"]},right:{points:["cl","cr"]},top:{points:["bc","tc"]},bottom:{points:["tc","bc"]},topLeft:{points:["bl","tl"]},leftTop:{points:["tr","tl"]},topRight:{points:["br","tr"]},rightTop:{points:["tl","tr"]},bottomRight:{points:["tr","br"]},rightBottom:{points:["bl","br"]},bottomLeft:{points:["tl","bl"]},leftBottom:{points:["br","bl"]}},Ll={topLeft:{points:["bl","tc"]},leftTop:{points:["tr","cl"]},topRight:{points:["br","tc"]},rightTop:{points:["tl","cr"]},bottomRight:{points:["tr","bc"]},rightBottom:{points:["bl","cr"]},bottomLeft:{points:["tl","bc"]},leftBottom:{points:["br","cl"]}},zl=new Set(["topLeft","topRight","bottomLeft","bottomRight","leftTop","leftBottom","rightTop","rightBottom"]);function jl(t){const{arrowWidth:r,autoAdjustOverflow:e,arrowPointAtCenter:n,offset:a,borderRadius:i,visibleFirst:o}=t,s=r/2,l={};return Object.keys(Oa).forEach(c=>{const u=n&&Ll[c]||Oa[c],d=Object.assign(Object.assign({},u),{offset:[0,0],dynamicInset:!0});switch(l[c]=d,zl.has(c)&&(d.autoArrow=!1),c){case"top":case"topLeft":case"topRight":d.offset[1]=-s-a;break;case"bottom":case"bottomLeft":case"bottomRight":d.offset[1]=s+a;break;case"left":case"leftTop":case"leftBottom":d.offset[0]=-s-a;break;case"right":case"rightTop":case"rightBottom":d.offset[0]=s+a;break}const m=oi({contentRadius:i,limitVerticalRadius:!0});if(n)switch(c){case"topLeft":case"bottomLeft":d.offset[0]=-m.arrowOffsetHorizontal-s;break;case"topRight":case"bottomRight":d.offset[0]=m.arrowOffsetHorizontal+s;break;case"leftTop":case"rightTop":d.offset[1]=-m.arrowOffsetHorizontal*2+s;break;case"leftBottom":case"rightBottom":d.offset[1]=m.arrowOffsetHorizontal*2-s;break}d.overflow=Dl(c,m,r,e),o&&(d.htmlRegion="visibleFirst")}),l}const Wl=t=>{const{calc:r,componentCls:e,tooltipMaxWidth:n,tooltipColor:a,tooltipBg:i,tooltipBorderRadius:o,zIndexPopup:s,controlHeight:l,boxShadowSecondary:c,paddingSM:u,paddingXS:d,arrowOffsetHorizontal:m,sizePopupArrow:b}=t,h=r(o).add(b).add(m).equal(),g=r(o).mul(2).add(b).equal();return[{[e]:Object.assign(Object.assign(Object.assign(Object.assign({},bi(t)),{position:"absolute",zIndex:s,display:"block",width:"max-content",maxWidth:n,visibility:"visible","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"&-hidden":{display:"none"},"--antd-arrow-background-color":i,[`${e}-inner`]:{minWidth:g,minHeight:l,padding:`${hr(t.calc(u).div(2).equal())} ${hr(d)}`,color:a,textAlign:"start",textDecoration:"none",wordWrap:"break-word",backgroundColor:i,borderRadius:o,boxShadow:c,boxSizing:"border-box"},[["&-placement-topLeft","&-placement-topRight","&-placement-bottomLeft","&-placement-bottomRight"].join(",")]:{minWidth:h},[["&-placement-left","&-placement-leftTop","&-placement-leftBottom","&-placement-right","&-placement-rightTop","&-placement-rightBottom"].join(",")]:{[`${e}-inner`]:{borderRadius:t.min(o,ii)}},[`${e}-content`]:{position:"relative"}}),uo(t,(v,y)=>{let{darkColor:p}=y;return{[`&${e}-${v}`]:{[`${e}-inner`]:{backgroundColor:p},[`${e}-arrow`]:{"--antd-arrow-background-color":p}}}})),{"&-rtl":{direction:"rtl"}})},Al(t,"var(--antd-arrow-background-color)"),{[`${e}-pure`]:{position:"relative",maxWidth:"none",margin:t.sizePopupArrow}}]},ql=t=>Object.assign(Object.assign({zIndexPopup:t.zIndexPopupBase+70},oi({contentRadius:t.borderRadius,limitVerticalRadius:!0})),Il(ka(t,{borderRadiusOuter:Math.min(t.borderRadiusOuter,4)}))),si=function(t){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return yi("Tooltip",n=>{const{borderRadius:a,colorTextLightSolid:i,colorBgSpotlight:o}=n,s=ka(n,{tooltipMaxWidth:250,tooltipColor:i,tooltipBorderRadius:a,tooltipBg:o});return[Wl(s),Ho(n,"zoom-big-fast")]},ql,{resetStyle:!1,injectStyle:r})(t)},Hl=Hr.map(t=>`${t}-inverse`);function Ul(t){return(arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0)?[].concat(j(Hl),j(Hr)).includes(t):Hr.includes(t)}function li(t,r){const e=Ul(r),n=Re({[`${t}-${r}`]:r&&e}),a={},i={};return r&&!e&&(a.background=r,i["--antd-arrow-background-color"]=r),{className:n,overlayStyle:a,arrowStyle:i}}const Bl=t=>{const{prefixCls:r,className:e,placement:n="top",title:a,color:i,overlayInnerStyle:o}=t,{getPrefixCls:s}=f.useContext(Va),l=s("tooltip",r),[c,u,d]=si(l),m=li(l,i),b=m.arrowStyle,h=Object.assign(Object.assign({},o),m.overlayStyle),g=Re(u,d,l,`${l}-pure`,`${l}-placement-${n}`,e,m.className);return c(f.createElement("div",{className:g,style:b},f.createElement("div",{className:`${l}-arrow`}),f.createElement(ai,Object.assign({},t,{className:u,prefixCls:l,overlayInnerStyle:h}),a)))};var Kl=function(t,r){var e={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&r.indexOf(n)<0&&(e[n]=t[n]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,n=Object.getOwnPropertySymbols(t);a<n.length;a++)r.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(t,n[a])&&(e[n[a]]=t[n[a]]);return e};const Gl=f.forwardRef((t,r)=>{var e,n;const{prefixCls:a,openClassName:i,getTooltipContainer:o,color:s,overlayInnerStyle:l,children:c,afterOpenChange:u,afterVisibleChange:d,destroyTooltipOnHide:m,destroyOnHidden:b,arrow:h=!0,title:g,overlay:v,builtinPlacements:y,arrowPointAtCenter:p=!1,autoAdjustOverflow:w=!0,motion:P,getPopupContainer:E,placement:F="top",mouseEnterDelay:R=.1,mouseLeaveDelay:M=.1,overlayStyle:O,rootClassName:x,overlayClassName:V,styles:D,classNames:_}=t,k=Kl(t,["prefixCls","openClassName","getTooltipContainer","color","overlayInnerStyle","children","afterOpenChange","afterVisibleChange","destroyTooltipOnHide","destroyOnHidden","arrow","title","overlay","builtinPlacements","arrowPointAtCenter","autoAdjustOverflow","motion","getPopupContainer","placement","mouseEnterDelay","mouseLeaveDelay","overlayStyle","rootClassName","overlayClassName","styles","classNames"]),Z=!!h,[,z]=Na(),{getPopupContainer:U,getPrefixCls:ee,direction:te,className:W,style:Se,classNames:ce,styles:Q}=wi("tooltip"),T=Ci(),B=f.useRef(null),we=()=>{var re;(re=B.current)===null||re===void 0||re.forceAlign()};f.useImperativeHandle(r,()=>{var re,ne;return{forceAlign:we,forcePopupAlign:()=>{T.deprecated(!1,"forcePopupAlign","forceAlign"),we()},nativeElement:(re=B.current)===null||re===void 0?void 0:re.nativeElement,popupElement:(ne=B.current)===null||ne===void 0?void 0:ne.popupElement}});const[Ce,Fe]=Ha(!1,{value:(e=t.open)!==null&&e!==void 0?e:t.visible,defaultValue:(n=t.defaultOpen)!==null&&n!==void 0?n:t.defaultVisible}),me=!g&&!v&&g!==0,He=re=>{var ne,oe;Fe(me?!1:re),me||((ne=t.onOpenChange)===null||ne===void 0||ne.call(t,re),(oe=t.onVisibleChange)===null||oe===void 0||oe.call(t,re))},Ue=f.useMemo(()=>{var re,ne;let oe=p;return typeof h=="object"&&(oe=(ne=(re=h.pointAtCenter)!==null&&re!==void 0?re:h.arrowPointAtCenter)!==null&&ne!==void 0?ne:p),y||jl({arrowPointAtCenter:oe,autoAdjustOverflow:w,arrowWidth:Z?z.sizePopupArrow:0,borderRadius:z.borderRadius,offset:z.marginXXS,visibleFirst:!0})},[p,h,y,z]),de=f.useMemo(()=>g===0?g:v||g||"",[v,g]),xe=f.createElement(Gs,{space:!0},typeof de=="function"?de():de),ve=ee("tooltip",a),he=ee(),Be=t["data-popover-inject"];let Ve=Ce;!("open"in t)&&!("visible"in t)&&me&&(Ve=!1);const ie=f.isValidElement(c)&&!Fi(c)?c:f.createElement("span",null,c),Ee=ie.props,Ke=!Ee.className||typeof Ee.className=="string"?Re(Ee.className,i||`${ve}-open`):Ee.className,[ge,Et,G]=si(ve,!Be),Ye=li(ve,s),Te=Ye.arrowStyle,Y=Re(V,{[`${ve}-rtl`]:te==="rtl"},Ye.className,x,Et,G,W,ce.root,_==null?void 0:_.root),Ae=Re(ce.body,_==null?void 0:_.body),[De,Le]=Ei("Tooltip",k.zIndex),st=f.createElement(kl,Object.assign({},k,{zIndex:De,showArrow:Z,placement:F,mouseEnterDelay:R,mouseLeaveDelay:M,prefixCls:ve,classNames:{root:Y,body:Ae},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Te),Q.root),Se),O),D==null?void 0:D.root),body:Object.assign(Object.assign(Object.assign(Object.assign({},Q.body),l),D==null?void 0:D.body),Ye.overlayStyle)},getTooltipContainer:E||o||U,ref:B,builtinPlacements:Ue,overlay:xe,visible:Ve,onVisibleChange:He,afterVisibleChange:u??d,arrowContent:f.createElement("span",{className:`${ve}-arrow-content`}),motion:{motionName:fo(he,"zoom-big-fast",t.transitionName),motionDeadline:1e3},destroyTooltipOnHide:b??!!m}),Ve?Pi(ie,{className:Ke}):ie);return ge(f.createElement(Oi.Provider,{value:Le},st))}),Zl=Gl;Zl._InternalPanelDoNotUseOrYouWillBeFired=Bl;var bt,wt,ke,dt,vt,jr,Ln,Ra,Ql=(Ra=class extends Ri{constructor(e,n){super();jt(this,vt);jt(this,bt);jt(this,wt);jt(this,ke);jt(this,dt);Wt(this,bt,e),this.setOptions(n),this.bindMethods(),qt(this,vt,jr).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){var a;const n=this.options;this.options=se(this,bt).defaultMutationOptions(e),Si(this.options,n)||se(this,bt).getMutationCache().notify({type:"observerOptionsUpdated",mutation:se(this,ke),observer:this}),n!=null&&n.mutationKey&&this.options.mutationKey&&Jn(n.mutationKey)!==Jn(this.options.mutationKey)?this.reset():((a=se(this,ke))==null?void 0:a.state.status)==="pending"&&se(this,ke).setOptions(this.options)}onUnsubscribe(){var e;this.hasListeners()||(e=se(this,ke))==null||e.removeObserver(this)}onMutationUpdate(e){qt(this,vt,jr).call(this),qt(this,vt,Ln).call(this,e)}getCurrentResult(){return se(this,wt)}reset(){var e;(e=se(this,ke))==null||e.removeObserver(this),Wt(this,ke,void 0),qt(this,vt,jr).call(this),qt(this,vt,Ln).call(this)}mutate(e,n){var a;return Wt(this,dt,n),(a=se(this,ke))==null||a.removeObserver(this),Wt(this,ke,se(this,bt).getMutationCache().build(se(this,bt),this.options)),se(this,ke).addObserver(this),se(this,ke).execute(e)}},bt=new WeakMap,wt=new WeakMap,ke=new WeakMap,dt=new WeakMap,vt=new WeakSet,jr=function(){var n;const e=((n=se(this,ke))==null?void 0:n.state)??xi();Wt(this,wt,{...e,isPending:e.status==="pending",isSuccess:e.status==="success",isError:e.status==="error",isIdle:e.status==="idle",mutate:this.mutate,reset:this.reset})},Ln=function(e){Ia.batch(()=>{var n,a,i,o,s,l,c,u;if(se(this,dt)&&this.hasListeners()){const d=se(this,wt).variables,m=se(this,wt).context;(e==null?void 0:e.type)==="success"?((a=(n=se(this,dt)).onSuccess)==null||a.call(n,e.data,d,m),(o=(i=se(this,dt)).onSettled)==null||o.call(i,e.data,null,d,m)):(e==null?void 0:e.type)==="error"&&((l=(s=se(this,dt)).onError)==null||l.call(s,e.error,d,m),(u=(c=se(this,dt)).onSettled)==null||u.call(c,void 0,e.error,d,m))}this.listeners.forEach(d=>{d(se(this,wt))})})},Ra);function Jl(t,r){const e=Ta(),[n]=f.useState(()=>new Ql(e,t));f.useEffect(()=>{n.setOptions(t)},[n,t]);const a=f.useSyncExternalStore(f.useCallback(o=>n.subscribe(Ia.batchCalls(o)),[n]),()=>n.getCurrentResult(),()=>n.getCurrentResult()),i=f.useCallback((o,s)=>{n.mutate(o,s).catch(Mi)},[n]);if(a.error&&$i(n.options.throwOnError,[a.error]))throw a.error;return{...a,mutate:i,mutateAsync:a.mutate}}function hu(t,r={}){const{useFormData:e=!0,showSuccessNotification:n=!0,invalidateQueries:a=[],method:i,headers:o,token:s,onSuccess:l,...c}=r,u=Ta(),d=Vi[t];if(!d)throw new Error(`API endpoint "${t}" not found`);const m=i||d.method,b=f.useCallback(async g=>{const{data:v,slug:y}=g&&typeof g=="object"&&"data"in g?g:{data:g,slug:void 0},p=m==="DELETE";return await _i.request(t,{...!p&&{data:v},slug:y,useFormData:!p&&e,showSuccessNotification:n,method:m,customHeaders:o,token:s})},[t,m,e,n,s]),h=f.useCallback((g,v,y)=>{typeof a=="function"?a({data:g,variables:v},u):Xl(a,u,{data:g,variables:v}),l&&l(g,v,y)},[a,u,l]);return Jl({mutationFn:b,onSuccess:h,networkMode:"online",...c})}function Xl(t,r,{data:e,variables:n}){if(t)try{let a=[];typeof t=="function"?a=t({data:e,variables:n}):Array.isArray(t)&&(a=t),a.length>0&&a.forEach(i=>{if(i)if(typeof i=="object"&&i.queryKey){const o={queryKey:Array.isArray(i.queryKey)?i.queryKey:[i.queryKey]};i.exact!==void 0&&(o.exact=i.exact),i.type==="paginated"&&(o.predicate=s=>{const l=s.queryKey,c=o.queryKey;return l[0]===c[0]&&(l.length>1||l.some(u=>typeof u=="string"&&u.includes('"page"')))},delete o.exact),r.invalidateQueries(o)}else r.invalidateQueries({queryKey:Array.isArray(i)?i:[i]})})}catch{}}export{No as A,uu as B,Gs as C,Ya as D,lu as E,Qr as F,ou as G,wr as H,Xt as I,Ls as J,Us as K,Kr as L,Pl as M,su as N,Ol as O,Ba as P,Al as Q,yr as R,oi as S,Zl as T,jl as U,cu as V,Ja as W,ai as X,Jl as a,Ha as b,vu as c,fo as d,$l as e,Ho as f,nu as g,Ka as h,ru as i,Hn as j,Cl as k,pa as l,Il as m,iu as n,au as o,So as p,Po as q,Ro as r,Oo as s,Tl as t,hu as u,Qs as v,Zs as w,du as x,fu as y,Rl as z};
