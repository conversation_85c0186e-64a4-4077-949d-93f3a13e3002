<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Socket.IO Chat API Tester</title>
        <script src="https://unpkg.com/alpinejs" defer></script>
        <script
            src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.min.js"
            defer
        ></script>
        <style>
            body {
                font-family: Arial, sans-serif;
                max-width: 800px;
                margin: 20px auto;
                padding: 0 20px;
            }

            .container {
                display: grid;
                grid-template-columns: 300px 1fr;
                gap: 20px;
            }

            .panel {
                border: 1px solid #ccc;
                padding: 15px;
                margin-bottom: 15px;
                border-radius: 5px;
            }

            .message-container {
                height: 400px;
                overflow-y: auto;
                border: 1px solid #eee;
                padding: 10px;
                margin: 10px 0;
            }

            .message {
                margin: 5px 0;
                padding: 5px;
                background: #f5f5f5;
                border-radius: 3px;
            }

            .room-list {
                height: 400px;
                overflow-y: auto;
                border: 1px solid #eee;
            }

            .room-item {
                padding: 10px;
                cursor: pointer;
                border-bottom: 1px solid #eee;
            }

            .room-item:hover {
                background: #f5f5f5;
            }

            .active {
                background: #e3e3e3;
            }

            .typing-indicator {
                font-style: italic;
                color: #666;
                height: 20px;
            }

            x-cloak {
                display: none;
            }
        </style>
    </head>

    <body>
        <main x-cloak x-data="main">
            <h1>Socket.IO Chat API Tester</h1>

            <div class="panel">
                <h3>Connection</h3>
                <input
                    type="text"
                    id="serverUrl"
                    placeholder="Socket.IO Server URL"
                    style="width: 300px"
                    x-model="url"
                />
                <input
                    type="text"
                    id="jwtToken"
                    placeholder="JWT Token"
                    style="width: 300px"
                    x-model="token"
                />
                <button
                    x-on:click="connect"
                    x-show="!connected"
                    :disabled="connectng"
                    x-text="connectng ? 'Connecting...' : 'Connect'"
                ></button>
                <button x-on:click="disconnect" x-show="connected">
                    Disconnect
                </button>
                <div>
                    Status:
                    <span
                        id="connectionStatus"
                        :style="{ color: connected ? 'green' : 'red' }"
                        x-text="connected ? 'Connected' : 'Disconnected'"
                        >Disconnected</span
                    >
                </div>
            </div>

            <div class="container">
                <div
                    class="panel"
                    id="roomPanel"
                    x-show="connected && !selectedRoomId && !selectedRoomOtherUserId"
                >
                    <h3>Rooms</h3>
                    <button x-on:click="loadRooms">Refresh</button>
                    <button x-on:click="createRoom">New Room</button>
                    <input
                        type="text"
                        id="searchRoom"
                        placeholder="Search rooms"
                        style="width: 100%"
                        x-model="search"
                        x-on:input.debounce.500ms="loadRooms"
                    />
                    <div class="room-list" id="roomList">
                        <template x-for="room in rooms">
                            <div
                                class="room-item"
                                :class="{ 'active': room.id === selectedRoomId }"
                                x-on:click="selectRoom(room)"
                                x-text="room.room_title"
                            ></div>
                        </template>
                    </div>
                </div>

                <div
                    class="panel"
                    id="chatPanel"
                    x-show="connected && (selectedRoomId || selectedRoomOtherUserId)"
                >
                    <h3 id="roomTitle"></h3>
                    <button x-on:click="reloadChatHistory">Refresh</button>
                    <button x-on:click="leaveRoom">Leave Room</button>
                    <div
                        class="message-container"
                        id="messageContainer"
                        x-ref="messageContainer"
                    >
                        <template x-for="message in messages">
                            <div class="message">
                                <strong
                                    x-text="`message.user.name (${message.user.id})`"
                                ></strong
                                >: <span x-text="message.message"></span>
                            </div>
                        </template>
                    </div>
                    <div
                        class="typing-indicator"
                        id="typingIndicator"
                        x-text="typingUsers.length > 0 ? `${typingUsers.length} user${typingUsers.length > 1 ? 's' : ''} is typing...` : ''"
                    ></div>
                    <div>
                        <textarea
                            id="messageInput"
                            placeholder="Type a message"
                            style="width: 100%; margin-bottom: 10px"
                            x-model="message"
                            x-on:input.debounce.500ms="onTyping"
                        ></textarea>
                        <button x-on:click="sendMessage">Send</button>
                        <button x-on:click="loadMoreMessages">
                            Load More Messages
                        </button>
                    </div>
                </div>
            </div>
        </main>

        <script>
            document.addEventListener('alpine:init', () => {
                Alpine.data('main', () => data);
            });

            const data = {
                socket: null,
                url: 'http://127.0.0.1:3335',
                token: '',
                connected: false,
                connectng: false,
                search: '',
                rooms: [],
                selectedRoomId: null,
                selectedRoomTitle: null,
                selectedRoomOtherUserId: null,
                messages: [],
                message: '',
                typing: false,
                typingTimeout: null,
                typingUsers: [],
                roomPanelError: null,
                chatPanelError: null,

                init() {
                    console.log('init');
                    this.loadStorage();
                    this.watch();
                },

                loadStorage() {
                    this.url =
                        window.localStorage.getItem('chat-server') ?? this.url;
                    this.token =
                        window.localStorage.getItem('chat-token') ?? this.token;
                },

                watch() {
                    this.$watch('messages', () =>
                        this.scrollMessagesToBottom(),
                    );
                },

                emit(
                    event,
                    payload,
                    successCallback = null,
                    errorCallback = null,
                ) {
                    console.log('emit', event, payload);
                    this.socket.emit(event, payload, (response) => {
                        console[response.status ? 'log' : 'error']('emit ack', event, response);
                        if (
                            response.status &&
                            typeof successCallback === 'function'
                        ) {
                            successCallback(response.data);
                        } else if (typeof errorCallback === 'function') {
                            errorCallback(response);
                        }
                    });
                },

                on(event, callback) {
                    this.socket.on(event, (response) => {
                        console.log('event', event, response);
                        callback(response);
                    });
                },

                connect() {
                    console.log('connecting');
                    this.connectng = true;

                    window.localStorage.setItem('chat-server', this.url);
                    window.localStorage.setItem('chat-token', this.token);

                    this.socket = io(this.url, {
                        query: { authorization: this.token },
                        transports: ['websocket'],
                        reconnection: true,
                        reconnectionAttempts: 5,
                        reconnectionDelay: 1000,
                    });

                    this.addSocketEventListeners();
                },

                disconnect() {
                    if (this.socket) {
                        console.log('disconnecting');
                        this.socket.disconnect();
                        this.socket = null;
                    }
                },

                addSocketEventListeners() {
                    this.on('connect', () => this.onConnect());
                    this.on('disconnect', () => this.onDisconnect());
                    this.on('connect_error', (error) =>
                        this.onConnectionError(error),
                    );
                    this.on('_receivedMessage', (data) =>
                        this.onReceivedMessage(data),
                    );
                    this.on('_startTyping', (data) =>
                        this.onOtherUsersStartTyping(data),
                    );
                    this.on('_stopTyping', (data) =>
                        this.onOtherUsersStopTyping(data),
                    );
                },

                onConnect() {
                    console.log('Connected to Socket.IO server');
                    this.connected = true;
                    this.connectionError = null;
                    this.connectng = false;
                    this.roomPanelError = null;
                    this.chatPanelError = null;
                    this.loadRooms();
                },

                onDisconnect() {
                    console.log('Disconnected from Socket.IO server');
                    this.connected = false;
                    this.connectng = false;
                    this.rooms = [];
                    this.selectedRoomId = null;
                    this.selectedRoomTitle = null;
                    this.selectedRoomOtherUserId = null;
                    this.messages = [];
                    this.lastMessageId = null;
                    this.message = '';
                    this.typing = false;
                    this.typingTimeout = null;
                    this.typingUsers = [];
                    this.roomPanelError = null;
                    this.chatPanelError = null;
                },

                onConnectionError(error) {
                    console.error('Connection error: ', error);
                    this.connectng = false;
                    this.connectionError = error?.message;
                },

                loadRooms() {
                    this.emit(
                        '_loadRoomsWithCb',
                        { keyword: this.search, limit: 10 },
                        (data) => this.updateRooms(data),
                        (error) => {
                            this.roomPanelError = error?.message;
                        },
                    );
                },

                updateRooms(data) {
                    this.rooms = data;
                },

                selectRoom(room) {
                    this.selectedRoomId = room.id ?? null;
                    this.selectedRoomOtherUserId = room.id
                        ? null
                        : room.other_user_id;
                    this.selectedRoomTitle = room.room_title;
                    this.loadChatHistory(
                        this.selectedRoomId,
                        this.selectedRoomOtherUserId,
                    );
                },

                createRoom() {
                    const otherUserId = Number(prompt('User ID:'));
                    if (Number.isNaN(otherUserId)) {
                        alert('Invalid user ID');
                        return;
                    }

                    const roomTitle = `New Room (User ID: ${otherUserId})`;
                    const newRoom = {
                        id: null,
                        room_title: roomTitle,
                        other_user_id: otherUserId,
                    };

                    this.messages = [];
                    this.selectedRoomId = null;
                    this.selectedRoomOtherUserId = otherUserId;
                    this.selectedRoomTitle = roomTitle;
                    this.rooms = [newRoom, ...this.rooms];
                    this.loadChatHistory(null, otherUserId);
                },

                leaveRoom() {
                    this.emit(
                        '_leaveRoom',
                        {},
                        () => {
                            this.selectedRoomId = null;
                            this.selectedRoomOtherUserId = null;
                            this.selectedRoomTitle = null;
                            this.messages = [];
                            this.loadRooms();
                        },
                        (error) => {
                            this.chatPanelError = error?.message;
                        },
                    );
                },

                loadChatHistory(roomId, otherUserId, lastRecordId = null) {
                    if (!roomId && !otherUserId) return;

                    this.emit(
                        '_loadChatHistoryWithCb',
                        {
                            chat_room_id: roomId,
                            other_user_id: otherUserId,
                            last_record_id: lastRecordId,
                        },
                        (data) => {
                            this.messages = Array.isArray(data)
                                ? data.reverse()
                                : [];
                        },
                        (error) => {
                            this.chatPanelError = error?.message;
                        },
                    );
                },

                reloadChatHistory() {
                    this.loadChatHistory(
                        this.selectedRoomId,
                        this.selectedRoomOtherUserId,
                    );
                },

                loadMoreMessages() {
                    this.loadChatHistory(
                        this.selectedRoomId,
                        this.selectedRoomOtherUserId,
                        this.messages[this.messages.length - 1].id,
                    );
                },

                onReceivedMessage(message) {
                    this.messages.push(message);
                    this.lastMessageId = message.id;
                },

                onOtherUsersStartTyping(data) {
                    this.typingUsers.push(data.user_id);
                },

                onOtherUsersStopTyping(data) {
                    this.typingUsers = this.typingUsers.filter(
                        (userId) => userId !== data.user_id,
                    );
                },

                onTyping() {
                    if (!this.selectedRoomId && !this.selectedRoomOtherUserId)
                        return;

                    this.emit(
                        '_startTyping',
                        {},
                        (response) => {},
                        (error) => {},
                    );

                    clearTimeout(this.typingTimeout);
                    this.typingTimeout = setTimeout(() => {
                        this.emit(
                            '_stopTyping',
                            {},
                            (response) => {},
                            (error) => {},
                        );
                    }, 3000);
                },

                sendMessage() {
                    this.emit(
                        '_sendMessageWithCb',
                        {
                            chat_room_id: this.selectedRoomId,
                            other_user_id: this.selectedRoomOtherUserId,
                            message: this.message,
                            message_type: 'text',
                        },
                        (data) => {
                            // this.messages.push(data);
                            // this.lastMessageId = data.id;
                            this.message = '';
                        },
                        (error) => {
                            this.chatPanelError = error?.message;
                        },
                    );
                },

                scrollMessagesToBottom() {
                    const messageContainer = this.$refs.messageContainer;
                    messageContainer.scrollTop = messageContainer.scrollHeight;
                },
            };
        </script>
    </body>
</html>
