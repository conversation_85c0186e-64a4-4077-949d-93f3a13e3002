.search-bar {
  border: 1px solid #D9D9D9;
  height: 55px !important;
}

.user-info {
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.user-img {
  height: 32px;
  width: 32px;
  border-radius: 50%;
  margin-right: 8px;
}

.user-name {
  font-weight: 500;
}

.icons .icon {
  font-size: 18px;
  margin: 0 8px;
  cursor: pointer;
}

.gallery-img {
  height: 190px;
  margin-top: 10px;
}

.gallery-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 12px;
}

.repost-para {
  box-shadow: 11px 12px 10px -17px #000;
}

.reply-bg {
  border: 1px solid #F0F0F0;
  background-color: #FAFAFA;
  padding: 10px 9px 4px 10px;
  border-radius: 12px;
  margin: 10px 0;
}

.comment-reply {
  margin-left: 90px !important;
}

.comment-reply a {
  text-decoration: underline !important;
}

.my-drop {
  width: 175px;
}

.my-drop>ul>li:first-child {
  background: #E6F7FF;
  color: #1890FF !important;
}

.report-modal .ant-modal-header {
  text-align: center;
  /* font-size: 34px !important; */
}

.report-modal .ant-modal-header .ant-modal-title {
  font-size: 32px;
  color: #3883E2;
}

.report-modal .ant-modal-body p {
  font-size: 18px;
  text-align: center;
  margin-top: 30px !important;
  color: #313131 !important;
}

.report-modal .ant-modal-body textarea {
  font-size: 17px;
  border: 1px solid #F0F0F0 !important;
}

.report-modal .ant-modal-content {
  padding: 35px 86px 50px !important;
}

.modal-submit {
  width: 100%;
  background-color: #3883E2;
  padding: 25px 0;
  color: #fff;
  font-size: 18px;
  margin-top: 30px;
}

.comment-reply {
  background: #fafafa;
  padding: 10px;
  border-radius: 12px;
  margin-bottom: 13px;
}

.mycomment {
  background: #fafafa;
  padding: 10px;
  border-radius: 12px;
  margin-bottom: 13px;
}

.anticon-like.anticon svg {
  color: #18acff;
}

.post-comment-btn {
  background-color: #3883e2;
  border-color: transparent;
  height: 40px;
  flex: 0 0 45px;
}

.post-comment-btn .anticon svg {
  fill: #fff;
}

.post-detail .sphere-post {
  text-align: center;
  overflow: hidden;
  clear: both;
  width: 100%;
  border-radius: 20px;
  border: none;
  position: relative;
  height: auto;
}

.post-detail .sphere-post img {
  all: unset;
}
textarea:focus,textarea:active {
  outline: none !important;
  box-shadow: none !important;
}