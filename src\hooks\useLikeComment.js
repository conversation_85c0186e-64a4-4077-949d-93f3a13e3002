import { useState, useCallback } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import apiClient from "@/services/apiClient";

export const useLikeComment = (
  commentId,
  initialLiked = false,
  initialLikesCount = 0
) => {
  const [isLiked, setIsLiked] = useState(initialLiked);
  const [likesCount, setLikesCount] = useState(initialLikesCount);
  const queryClient = useQueryClient();

  // Helper function to update comment data across all cache entries
  const updateCommentInCache = useCallback((liked) => {
    const likeDelta = liked ? 1 : -1;
    
    // Update all getComments queries
    queryClient.setQueriesData(
      { queryKey: ["getComments"] },
      (oldData) => {
        if (!oldData) return oldData;

        // Handle comments data structure
        if (oldData.data && Array.isArray(oldData.data)) {
          return {
            ...oldData,
            data: oldData.data.map(comment => 
              comment.id === commentId || comment.id === parseInt(commentId)
                ? {
                    ...comment,
                    is_liked: liked,
                    likes_count: Math.max(0, (comment.likes_count || 0) + likeDelta)
                  }
                : comment
            )
          };
        }

        // Handle direct array structure
        if (Array.isArray(oldData)) {
          return oldData.map(comment => 
            comment.id === commentId || comment.id === parseInt(commentId)
              ? {
                  ...comment,
                  is_liked: liked,
                  likes_count: Math.max(0, (comment.likes_count || 0) + likeDelta)
                }
              : comment
          );
        }

        return oldData;
      }
    );
  }, [commentId, queryClient]);

  const { mutate: toggleLike, isPending: isToggling } = useMutation({
    mutationFn: async (liked) => {
      // Use the exact same payload structure as post likes
      const payload = {
        type: "comment",
        value: liked,
      };

      // Use the same likePost API endpoint
      const response = await apiClient.request("likePost", {
        slug: commentId,
        data: payload,
        useFormData: false,
        showSuccessNotification: false,
      });

      return response;
    },
    onMutate: async (liked) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ["getComments"] });

      // Optimistically update local state
      setIsLiked(liked);
      setLikesCount((prev) => (liked ? prev + 1 : Math.max(0, prev - 1)));

      // Optimistically update all cache entries
      updateCommentInCache(liked);

      // Return context for rollback
      return { previousLiked: !liked };
    },
    onError: (error, liked, context) => {
      // Revert local state
      setIsLiked(context?.previousLiked ?? !liked);
      setLikesCount((prev) => (liked ? Math.max(0, prev - 1) : prev + 1));

      // Revert cache updates
      updateCommentInCache(context?.previousLiked ?? !liked);
    },
    onSettled: () => {
      // Invalidate queries as fallback to ensure consistency
      queryClient.invalidateQueries({
        queryKey: ["getComments"],
        exact: false,
      });
      queryClient.invalidateQueries({
        queryKey: ["postItem"],
        exact: false,
      });
    },
  });

  const handleLikeToggle = useCallback(() => {
    const newLikedState = !isLiked;
    toggleLike(newLikedState);
  }, [isLiked, toggleLike]);

  return {
    isLiked,
    likesCount,
    isToggling,
    handleLikeToggle,
  };
};

export default useLikeComment;