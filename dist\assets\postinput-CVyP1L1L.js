import{u as N,r as k,j as e,X as F,q as P}from"./index-Dklazue-.js";import{F as x}from"./flatbutton-B_tUS4QM.js";import{u as S}from"./index-vmMMvWhJ.js";import{h as u}from"./trialUtils-PQN1eD5v.js";import{I as A}from"./index-BUt89ETK.js";import{I as B}from"./index-BNeAK5sW.js";import{B as T}from"./button-DNhBCuue.js";const{TextArea:U}=A,$=({placeholder:h="What's on your mind?",button:r,postCreationHook:s,onCancelEdit:n})=>{const{showAlert:l}=S(),d=N(),{content:b,setContent:g,selectedFile:a,filePreview:o,fileType:f,isCreatingPost:t,isFormValid:j,isEditMode:i,handleImageUpload:z,handleVideoUpload:W,removeFile:v,handleSubmit:c}=s||{},[y,C]=k.useState(""),p=s?b:y,w=s?g:C,R=async()=>{await u(l,d)},I=async()=>{!await u(l,d)&&c&&c()};return e.jsx("div",{style:{maxWidth:"100%",border:"1px solid #ddd",borderRadius:"10px"},className:"mt-4",children:e.jsxs("div",{style:{backgroundColor:"#fff",padding:"10px",borderRadius:"10px"},children:[e.jsx(U,{value:p,onChange:m=>w(m.target.value),onClick:R,placeholder:h,autoSize:{minRows:3,maxRows:5},style:{border:"none",resize:"none",marginBottom:"10px",background:"transparent"},disabled:t}),s&&o&&e.jsxs("div",{className:"mb-3 position-relative",style:{maxWidth:"200px"},children:[f==="image"?e.jsx(B,{width:200,height:150,src:o,style:{objectFit:"cover",borderRadius:"8px"},preview:!1}):e.jsxs("video",{width:200,height:150,controls:!0,style:{borderRadius:"8px",objectFit:"cover"},children:[e.jsx("source",{src:o,type:a==null?void 0:a.type}),"Your browser does not support the video tag."]}),e.jsx(T,{type:"text",danger:!0,icon:e.jsx(F,{}),size:"small",onClick:v,style:{position:"absolute",top:"5px",right:"5px",backgroundColor:"rgba(0,0,0,0.5)",color:"white",border:"none"},disabled:t})]}),e.jsxs("div",{className:"d-flex justify-content-between align-items-center icon-color",children:[e.jsx("div",{className:"d-flex gap-3"}),r||e.jsxs("div",{className:"d-flex gap-2",children:[i&&n&&e.jsx(x,{type:"default",title:"Cancel",className:"px-4 post-btn px-5",onClick:n,disabled:t}),e.jsx(x,{type:"primary",title:t?i?"Updating...":"Posting...":i?"Update":"Post",className:"post-btn px-5",onClick:I,disabled:s?!j||t:!p.trim(),icon:t?e.jsx(P,{}):void 0})]})]})]})})};export{$ as P};
