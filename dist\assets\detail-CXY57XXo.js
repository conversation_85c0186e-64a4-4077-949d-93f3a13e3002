import{r as j,R as $,j as S,f as Ue,m as Ke,o as Qe,u as Ze,p as Je}from"./index-Dklazue-.js";import{I as Te}from"./index-vmMMvWhJ.js";import{a as et}from"./useMutation-BrUrPIzr.js";import{I as ae}from"./iconbutton-DMkwwwLX.js";import{u as tt}from"./useQuery-C3n1GVcJ.js";import{S as st}from"./Skeleton--lTrbnrp.js";import"./button-DNhBCuue.js";import"./index-Cj6uPc4c.js";function ye(t){return t!==null&&typeof t=="object"&&"constructor"in t&&t.constructor===Object}function Se(t,e){t===void 0&&(t={}),e===void 0&&(e={});const s=["__proto__","constructor","prototype"];Object.keys(e).filter(i=>s.indexOf(i)<0).forEach(i=>{typeof t[i]>"u"?t[i]=e[i]:ye(e[i])&&ye(t[i])&&Object.keys(e[i]).length>0&&Se(t[i],e[i])})}const ze={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector(){return null},querySelectorAll(){return[]},getElementById(){return null},createEvent(){return{initEvent(){}}},createElement(){return{children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName(){return[]}}},createElementNS(){return{}},importNode(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function W(){const t=typeof document<"u"?document:{};return Se(t,ze),t}const it={document:ze,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle(){return{getPropertyValue(){return""}}},Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia(){return{}},requestAnimationFrame(t){return typeof setTimeout>"u"?(t(),null):setTimeout(t,0)},cancelAnimationFrame(t){typeof setTimeout>"u"||clearTimeout(t)}};function V(){const t=typeof window<"u"?window:{};return Se(t,it),t}function nt(t){return t===void 0&&(t=""),t.trim().split(" ").filter(e=>!!e.trim())}function rt(t){const e=t;Object.keys(e).forEach(s=>{try{e[s]=null}catch{}try{delete e[s]}catch{}})}function me(t,e){return e===void 0&&(e=0),setTimeout(t,e)}function ee(){return Date.now()}function at(t){const e=V();let s;return e.getComputedStyle&&(s=e.getComputedStyle(t,null)),!s&&t.currentStyle&&(s=t.currentStyle),s||(s=t.style),s}function lt(t,e){e===void 0&&(e="x");const s=V();let i,r,n;const o=at(t);return s.WebKitCSSMatrix?(r=o.transform||o.webkitTransform,r.split(",").length>6&&(r=r.split(", ").map(l=>l.replace(",",".")).join(", ")),n=new s.WebKitCSSMatrix(r==="none"?"":r)):(n=o.MozTransform||o.OTransform||o.MsTransform||o.msTransform||o.transform||o.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),i=n.toString().split(",")),e==="x"&&(s.WebKitCSSMatrix?r=n.m41:i.length===16?r=parseFloat(i[12]):r=parseFloat(i[4])),e==="y"&&(s.WebKitCSSMatrix?r=n.m42:i.length===16?r=parseFloat(i[13]):r=parseFloat(i[5])),r||0}function K(t){return typeof t=="object"&&t!==null&&t.constructor&&Object.prototype.toString.call(t).slice(8,-1)==="Object"}function ot(t){return typeof window<"u"&&typeof window.HTMLElement<"u"?t instanceof HTMLElement:t&&(t.nodeType===1||t.nodeType===11)}function k(){const t=Object(arguments.length<=0?void 0:arguments[0]),e=["__proto__","constructor","prototype"];for(let s=1;s<arguments.length;s+=1){const i=s<0||arguments.length<=s?void 0:arguments[s];if(i!=null&&!ot(i)){const r=Object.keys(Object(i)).filter(n=>e.indexOf(n)<0);for(let n=0,o=r.length;n<o;n+=1){const l=r[n],a=Object.getOwnPropertyDescriptor(i,l);a!==void 0&&a.enumerable&&(K(t[l])&&K(i[l])?i[l].__swiper__?t[l]=i[l]:k(t[l],i[l]):!K(t[l])&&K(i[l])?(t[l]={},i[l].__swiper__?t[l]=i[l]:k(t[l],i[l])):t[l]=i[l])}}}return t}function Z(t,e,s){t.style.setProperty(e,s)}function Ne(t){let{swiper:e,targetPosition:s,side:i}=t;const r=V(),n=-e.translate;let o=null,l;const a=e.params.speed;e.wrapperEl.style.scrollSnapType="none",r.cancelAnimationFrame(e.cssModeFrameID);const d=s>n?"next":"prev",c=(g,v)=>d==="next"&&g>=v||d==="prev"&&g<=v,u=()=>{l=new Date().getTime(),o===null&&(o=l);const g=Math.max(Math.min((l-o)/a,1),0),v=.5-Math.cos(g*Math.PI)/2;let m=n+v*(s-n);if(c(m,s)&&(m=s),e.wrapperEl.scrollTo({[i]:m}),c(m,s)){e.wrapperEl.style.overflow="hidden",e.wrapperEl.style.scrollSnapType="",setTimeout(()=>{e.wrapperEl.style.overflow="",e.wrapperEl.scrollTo({[i]:m})}),r.cancelAnimationFrame(e.cssModeFrameID);return}e.cssModeFrameID=r.requestAnimationFrame(u)};u()}function R(t,e){e===void 0&&(e="");const s=V(),i=[...t.children];return s.HTMLSlotElement&&t instanceof HTMLSlotElement&&i.push(...t.assignedElements()),e?i.filter(r=>r.matches(e)):i}function dt(t,e){const s=[e];for(;s.length>0;){const i=s.shift();if(t===i)return!0;s.push(...i.children,...i.shadowRoot?i.shadowRoot.children:[],...i.assignedElements?i.assignedElements():[])}}function ct(t,e){const s=V();let i=e.contains(t);return!i&&s.HTMLSlotElement&&e instanceof HTMLSlotElement&&(i=[...e.assignedElements()].includes(t),i||(i=dt(t,e))),i}function te(t){try{console.warn(t);return}catch{}}function se(t,e){e===void 0&&(e=[]);const s=document.createElement(t);return s.classList.add(...Array.isArray(e)?e:nt(e)),s}function ut(t,e){const s=[];for(;t.previousElementSibling;){const i=t.previousElementSibling;e?i.matches(e)&&s.push(i):s.push(i),t=i}return s}function ft(t,e){const s=[];for(;t.nextElementSibling;){const i=t.nextElementSibling;e?i.matches(e)&&s.push(i):s.push(i),t=i}return s}function q(t,e){return V().getComputedStyle(t,null).getPropertyValue(e)}function ie(t){let e=t,s;if(e){for(s=0;(e=e.previousSibling)!==null;)e.nodeType===1&&(s+=1);return s}}function Ae(t,e){const s=[];let i=t.parentElement;for(;i;)e?i.matches(e)&&s.push(i):s.push(i),i=i.parentElement;return s}function he(t,e,s){const i=V();return t[e==="width"?"offsetWidth":"offsetHeight"]+parseFloat(i.getComputedStyle(t,null).getPropertyValue(e==="width"?"margin-right":"margin-top"))+parseFloat(i.getComputedStyle(t,null).getPropertyValue(e==="width"?"margin-left":"margin-bottom"))}function D(t){return(Array.isArray(t)?t:[t]).filter(e=>!!e)}function ne(t,e){e===void 0&&(e=""),typeof trustedTypes<"u"?t.innerHTML=trustedTypes.createPolicy("html",{createHTML:s=>s}).createHTML(e):t.innerHTML=e}let le;function pt(){const t=V(),e=W();return{smoothScroll:e.documentElement&&e.documentElement.style&&"scrollBehavior"in e.documentElement.style,touch:!!("ontouchstart"in t||t.DocumentTouch&&e instanceof t.DocumentTouch)}}function _e(){return le||(le=pt()),le}let oe;function mt(t){let{userAgent:e}=t===void 0?{}:t;const s=_e(),i=V(),r=i.navigator.platform,n=e||i.navigator.userAgent,o={ios:!1,android:!1},l=i.screen.width,a=i.screen.height,d=n.match(/(Android);?[\s\/]+([\d.]+)?/);let c=n.match(/(iPad).*OS\s([\d_]+)/);const u=n.match(/(iPod)(.*OS\s([\d_]+))?/),g=!c&&n.match(/(iPhone\sOS|iOS)\s([\d_]+)/),v=r==="Win32";let m=r==="MacIntel";const w=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"];return!c&&m&&s.touch&&w.indexOf(`${l}x${a}`)>=0&&(c=n.match(/(Version)\/([\d.]+)/),c||(c=[0,1,"13_0_0"]),m=!1),d&&!v&&(o.os="android",o.android=!0),(c||g||u)&&(o.os="ios",o.ios=!0),o}function je(t){return t===void 0&&(t={}),oe||(oe=mt(t)),oe}let de;function ht(){const t=V(),e=je();let s=!1;function i(){const l=t.navigator.userAgent.toLowerCase();return l.indexOf("safari")>=0&&l.indexOf("chrome")<0&&l.indexOf("android")<0}if(i()){const l=String(t.navigator.userAgent);if(l.includes("Version/")){const[a,d]=l.split("Version/")[1].split(" ")[0].split(".").map(c=>Number(c));s=a<16||a===16&&d<2}}const r=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(t.navigator.userAgent),n=i(),o=n||r&&e.ios;return{isSafari:s||n,needPerspectiveFix:s,need3dFix:o,isWebView:r}}function $e(){return de||(de=ht()),de}function gt(t){let{swiper:e,on:s,emit:i}=t;const r=V();let n=null,o=null;const l=()=>{!e||e.destroyed||!e.initialized||(i("beforeResize"),i("resize"))},a=()=>{!e||e.destroyed||!e.initialized||(n=new ResizeObserver(u=>{o=r.requestAnimationFrame(()=>{const{width:g,height:v}=e;let m=g,w=v;u.forEach(T=>{let{contentBoxSize:h,contentRect:f,target:p}=T;p&&p!==e.el||(m=f?f.width:(h[0]||h).inlineSize,w=f?f.height:(h[0]||h).blockSize)}),(m!==g||w!==v)&&l()})}),n.observe(e.el))},d=()=>{o&&r.cancelAnimationFrame(o),n&&n.unobserve&&e.el&&(n.unobserve(e.el),n=null)},c=()=>{!e||e.destroyed||!e.initialized||i("orientationchange")};s("init",()=>{if(e.params.resizeObserver&&typeof r.ResizeObserver<"u"){a();return}r.addEventListener("resize",l),r.addEventListener("orientationchange",c)}),s("destroy",()=>{d(),r.removeEventListener("resize",l),r.removeEventListener("orientationchange",c)})}function vt(t){let{swiper:e,extendParams:s,on:i,emit:r}=t;const n=[],o=V(),l=function(c,u){u===void 0&&(u={});const g=o.MutationObserver||o.WebkitMutationObserver,v=new g(m=>{if(e.__preventObserver__)return;if(m.length===1){r("observerUpdate",m[0]);return}const w=function(){r("observerUpdate",m[0])};o.requestAnimationFrame?o.requestAnimationFrame(w):o.setTimeout(w,0)});v.observe(c,{attributes:typeof u.attributes>"u"?!0:u.attributes,childList:e.isElement||(typeof u.childList>"u"?!0:u).childList,characterData:typeof u.characterData>"u"?!0:u.characterData}),n.push(v)},a=()=>{if(e.params.observer){if(e.params.observeParents){const c=Ae(e.hostEl);for(let u=0;u<c.length;u+=1)l(c[u])}l(e.hostEl,{childList:e.params.observeSlideChildren}),l(e.wrapperEl,{attributes:!1})}},d=()=>{n.forEach(c=>{c.disconnect()}),n.splice(0,n.length)};s({observer:!1,observeParents:!1,observeSlideChildren:!1}),i("init",a),i("destroy",d)}var wt={on(t,e,s){const i=this;if(!i.eventsListeners||i.destroyed||typeof e!="function")return i;const r=s?"unshift":"push";return t.split(" ").forEach(n=>{i.eventsListeners[n]||(i.eventsListeners[n]=[]),i.eventsListeners[n][r](e)}),i},once(t,e,s){const i=this;if(!i.eventsListeners||i.destroyed||typeof e!="function")return i;function r(){i.off(t,r),r.__emitterProxy&&delete r.__emitterProxy;for(var n=arguments.length,o=new Array(n),l=0;l<n;l++)o[l]=arguments[l];e.apply(i,o)}return r.__emitterProxy=e,i.on(t,r,s)},onAny(t,e){const s=this;if(!s.eventsListeners||s.destroyed||typeof t!="function")return s;const i=e?"unshift":"push";return s.eventsAnyListeners.indexOf(t)<0&&s.eventsAnyListeners[i](t),s},offAny(t){const e=this;if(!e.eventsListeners||e.destroyed||!e.eventsAnyListeners)return e;const s=e.eventsAnyListeners.indexOf(t);return s>=0&&e.eventsAnyListeners.splice(s,1),e},off(t,e){const s=this;return!s.eventsListeners||s.destroyed||!s.eventsListeners||t.split(" ").forEach(i=>{typeof e>"u"?s.eventsListeners[i]=[]:s.eventsListeners[i]&&s.eventsListeners[i].forEach((r,n)=>{(r===e||r.__emitterProxy&&r.__emitterProxy===e)&&s.eventsListeners[i].splice(n,1)})}),s},emit(){const t=this;if(!t.eventsListeners||t.destroyed||!t.eventsListeners)return t;let e,s,i;for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return typeof n[0]=="string"||Array.isArray(n[0])?(e=n[0],s=n.slice(1,n.length),i=t):(e=n[0].events,s=n[0].data,i=n[0].context||t),s.unshift(i),(Array.isArray(e)?e:e.split(" ")).forEach(a=>{t.eventsAnyListeners&&t.eventsAnyListeners.length&&t.eventsAnyListeners.forEach(d=>{d.apply(i,[a,...s])}),t.eventsListeners&&t.eventsListeners[a]&&t.eventsListeners[a].forEach(d=>{d.apply(i,s)})}),t}};function bt(){const t=this;let e,s;const i=t.el;typeof t.params.width<"u"&&t.params.width!==null?e=t.params.width:e=i.clientWidth,typeof t.params.height<"u"&&t.params.height!==null?s=t.params.height:s=i.clientHeight,!(e===0&&t.isHorizontal()||s===0&&t.isVertical())&&(e=e-parseInt(q(i,"padding-left")||0,10)-parseInt(q(i,"padding-right")||0,10),s=s-parseInt(q(i,"padding-top")||0,10)-parseInt(q(i,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(s)&&(s=0),Object.assign(t,{width:e,height:s,size:t.isHorizontal()?e:s}))}function St(){const t=this;function e(C,P){return parseFloat(C.getPropertyValue(t.getDirectionLabel(P))||0)}const s=t.params,{wrapperEl:i,slidesEl:r,size:n,rtlTranslate:o,wrongRTL:l}=t,a=t.virtual&&s.virtual.enabled,d=a?t.virtual.slides.length:t.slides.length,c=R(r,`.${t.params.slideClass}, swiper-slide`),u=a?t.virtual.slides.length:c.length;let g=[];const v=[],m=[];let w=s.slidesOffsetBefore;typeof w=="function"&&(w=s.slidesOffsetBefore.call(t));let T=s.slidesOffsetAfter;typeof T=="function"&&(T=s.slidesOffsetAfter.call(t));const h=t.snapGrid.length,f=t.slidesGrid.length;let p=s.spaceBetween,b=-w,x=0,y=0;if(typeof n>"u")return;typeof p=="string"&&p.indexOf("%")>=0?p=parseFloat(p.replace("%",""))/100*n:typeof p=="string"&&(p=parseFloat(p)),t.virtualSize=-p,c.forEach(C=>{o?C.style.marginLeft="":C.style.marginRight="",C.style.marginBottom="",C.style.marginTop=""}),s.centeredSlides&&s.cssMode&&(Z(i,"--swiper-centered-offset-before",""),Z(i,"--swiper-centered-offset-after",""));const M=s.grid&&s.grid.rows>1&&t.grid;M?t.grid.initSlides(c):t.grid&&t.grid.unsetSlides();let I;const E=s.slidesPerView==="auto"&&s.breakpoints&&Object.keys(s.breakpoints).filter(C=>typeof s.breakpoints[C].slidesPerView<"u").length>0;for(let C=0;C<u;C+=1){I=0;let P;if(c[C]&&(P=c[C]),M&&t.grid.updateSlide(C,P,c),!(c[C]&&q(P,"display")==="none")){if(s.slidesPerView==="auto"){E&&(c[C].style[t.getDirectionLabel("width")]="");const L=getComputedStyle(P),z=P.style.transform,N=P.style.webkitTransform;if(z&&(P.style.transform="none"),N&&(P.style.webkitTransform="none"),s.roundLengths)I=t.isHorizontal()?he(P,"width"):he(P,"height");else{const A=e(L,"width"),O=e(L,"padding-left"),B=e(L,"padding-right"),G=e(L,"margin-left"),_=e(L,"margin-right"),F=L.getPropertyValue("box-sizing");if(F&&F==="border-box")I=A+G+_;else{const{clientWidth:Ye,offsetWidth:Xe}=P;I=A+O+B+G+_+(Xe-Ye)}}z&&(P.style.transform=z),N&&(P.style.webkitTransform=N),s.roundLengths&&(I=Math.floor(I))}else I=(n-(s.slidesPerView-1)*p)/s.slidesPerView,s.roundLengths&&(I=Math.floor(I)),c[C]&&(c[C].style[t.getDirectionLabel("width")]=`${I}px`);c[C]&&(c[C].swiperSlideSize=I),m.push(I),s.centeredSlides?(b=b+I/2+x/2+p,x===0&&C!==0&&(b=b-n/2-p),C===0&&(b=b-n/2-p),Math.abs(b)<1/1e3&&(b=0),s.roundLengths&&(b=Math.floor(b)),y%s.slidesPerGroup===0&&g.push(b),v.push(b)):(s.roundLengths&&(b=Math.floor(b)),(y-Math.min(t.params.slidesPerGroupSkip,y))%t.params.slidesPerGroup===0&&g.push(b),v.push(b),b=b+I+p),t.virtualSize+=I+p,x=I,y+=1}}if(t.virtualSize=Math.max(t.virtualSize,n)+T,o&&l&&(s.effect==="slide"||s.effect==="coverflow")&&(i.style.width=`${t.virtualSize+p}px`),s.setWrapperSize&&(i.style[t.getDirectionLabel("width")]=`${t.virtualSize+p}px`),M&&t.grid.updateWrapperSize(I,g),!s.centeredSlides){const C=[];for(let P=0;P<g.length;P+=1){let L=g[P];s.roundLengths&&(L=Math.floor(L)),g[P]<=t.virtualSize-n&&C.push(L)}g=C,Math.floor(t.virtualSize-n)-Math.floor(g[g.length-1])>1&&g.push(t.virtualSize-n)}if(a&&s.loop){const C=m[0]+p;if(s.slidesPerGroup>1){const P=Math.ceil((t.virtual.slidesBefore+t.virtual.slidesAfter)/s.slidesPerGroup),L=C*s.slidesPerGroup;for(let z=0;z<P;z+=1)g.push(g[g.length-1]+L)}for(let P=0;P<t.virtual.slidesBefore+t.virtual.slidesAfter;P+=1)s.slidesPerGroup===1&&g.push(g[g.length-1]+C),v.push(v[v.length-1]+C),t.virtualSize+=C}if(g.length===0&&(g=[0]),p!==0){const C=t.isHorizontal()&&o?"marginLeft":t.getDirectionLabel("marginRight");c.filter((P,L)=>!s.cssMode||s.loop?!0:L!==c.length-1).forEach(P=>{P.style[C]=`${p}px`})}if(s.centeredSlides&&s.centeredSlidesBounds){let C=0;m.forEach(L=>{C+=L+(p||0)}),C-=p;const P=C>n?C-n:0;g=g.map(L=>L<=0?-w:L>P?P+T:L)}if(s.centerInsufficientSlides){let C=0;m.forEach(L=>{C+=L+(p||0)}),C-=p;const P=(s.slidesOffsetBefore||0)+(s.slidesOffsetAfter||0);if(C+P<n){const L=(n-C-P)/2;g.forEach((z,N)=>{g[N]=z-L}),v.forEach((z,N)=>{v[N]=z+L})}}if(Object.assign(t,{slides:c,snapGrid:g,slidesGrid:v,slidesSizesGrid:m}),s.centeredSlides&&s.cssMode&&!s.centeredSlidesBounds){Z(i,"--swiper-centered-offset-before",`${-g[0]}px`),Z(i,"--swiper-centered-offset-after",`${t.size/2-m[m.length-1]/2}px`);const C=-t.snapGrid[0],P=-t.slidesGrid[0];t.snapGrid=t.snapGrid.map(L=>L+C),t.slidesGrid=t.slidesGrid.map(L=>L+P)}if(u!==d&&t.emit("slidesLengthChange"),g.length!==h&&(t.params.watchOverflow&&t.checkOverflow(),t.emit("snapGridLengthChange")),v.length!==f&&t.emit("slidesGridLengthChange"),s.watchSlidesProgress&&t.updateSlidesOffset(),t.emit("slidesUpdated"),!a&&!s.cssMode&&(s.effect==="slide"||s.effect==="fade")){const C=`${s.containerModifierClass}backface-hidden`,P=t.el.classList.contains(C);u<=s.maxBackfaceHiddenSlides?P||t.el.classList.add(C):P&&t.el.classList.remove(C)}}function xt(t){const e=this,s=[],i=e.virtual&&e.params.virtual.enabled;let r=0,n;typeof t=="number"?e.setTransition(t):t===!0&&e.setTransition(e.params.speed);const o=l=>i?e.slides[e.getSlideIndexByData(l)]:e.slides[l];if(e.params.slidesPerView!=="auto"&&e.params.slidesPerView>1)if(e.params.centeredSlides)(e.visibleSlides||[]).forEach(l=>{s.push(l)});else for(n=0;n<Math.ceil(e.params.slidesPerView);n+=1){const l=e.activeIndex+n;if(l>e.slides.length&&!i)break;s.push(o(l))}else s.push(o(e.activeIndex));for(n=0;n<s.length;n+=1)if(typeof s[n]<"u"){const l=s[n].offsetHeight;r=l>r?l:r}(r||r===0)&&(e.wrapperEl.style.height=`${r}px`)}function Tt(){const t=this,e=t.slides,s=t.isElement?t.isHorizontal()?t.wrapperEl.offsetLeft:t.wrapperEl.offsetTop:0;for(let i=0;i<e.length;i+=1)e[i].swiperSlideOffset=(t.isHorizontal()?e[i].offsetLeft:e[i].offsetTop)-s-t.cssOverflowAdjustment()}const Ee=(t,e,s)=>{e&&!t.classList.contains(s)?t.classList.add(s):!e&&t.classList.contains(s)&&t.classList.remove(s)};function yt(t){t===void 0&&(t=this&&this.translate||0);const e=this,s=e.params,{slides:i,rtlTranslate:r,snapGrid:n}=e;if(i.length===0)return;typeof i[0].swiperSlideOffset>"u"&&e.updateSlidesOffset();let o=-t;r&&(o=t),e.visibleSlidesIndexes=[],e.visibleSlides=[];let l=s.spaceBetween;typeof l=="string"&&l.indexOf("%")>=0?l=parseFloat(l.replace("%",""))/100*e.size:typeof l=="string"&&(l=parseFloat(l));for(let a=0;a<i.length;a+=1){const d=i[a];let c=d.swiperSlideOffset;s.cssMode&&s.centeredSlides&&(c-=i[0].swiperSlideOffset);const u=(o+(s.centeredSlides?e.minTranslate():0)-c)/(d.swiperSlideSize+l),g=(o-n[0]+(s.centeredSlides?e.minTranslate():0)-c)/(d.swiperSlideSize+l),v=-(o-c),m=v+e.slidesSizesGrid[a],w=v>=0&&v<=e.size-e.slidesSizesGrid[a],T=v>=0&&v<e.size-1||m>1&&m<=e.size||v<=0&&m>=e.size;T&&(e.visibleSlides.push(d),e.visibleSlidesIndexes.push(a)),Ee(d,T,s.slideVisibleClass),Ee(d,w,s.slideFullyVisibleClass),d.progress=r?-u:u,d.originalProgress=r?-g:g}}function Et(t){const e=this;if(typeof t>"u"){const c=e.rtlTranslate?-1:1;t=e&&e.translate&&e.translate*c||0}const s=e.params,i=e.maxTranslate()-e.minTranslate();let{progress:r,isBeginning:n,isEnd:o,progressLoop:l}=e;const a=n,d=o;if(i===0)r=0,n=!0,o=!0;else{r=(t-e.minTranslate())/i;const c=Math.abs(t-e.minTranslate())<1,u=Math.abs(t-e.maxTranslate())<1;n=c||r<=0,o=u||r>=1,c&&(r=0),u&&(r=1)}if(s.loop){const c=e.getSlideIndexByData(0),u=e.getSlideIndexByData(e.slides.length-1),g=e.slidesGrid[c],v=e.slidesGrid[u],m=e.slidesGrid[e.slidesGrid.length-1],w=Math.abs(t);w>=g?l=(w-g)/m:l=(w+m-v)/m,l>1&&(l-=1)}Object.assign(e,{progress:r,progressLoop:l,isBeginning:n,isEnd:o}),(s.watchSlidesProgress||s.centeredSlides&&s.autoHeight)&&e.updateSlidesProgress(t),n&&!a&&e.emit("reachBeginning toEdge"),o&&!d&&e.emit("reachEnd toEdge"),(a&&!n||d&&!o)&&e.emit("fromEdge"),e.emit("progress",r)}const ce=(t,e,s)=>{e&&!t.classList.contains(s)?t.classList.add(s):!e&&t.classList.contains(s)&&t.classList.remove(s)};function Ct(){const t=this,{slides:e,params:s,slidesEl:i,activeIndex:r}=t,n=t.virtual&&s.virtual.enabled,o=t.grid&&s.grid&&s.grid.rows>1,l=u=>R(i,`.${s.slideClass}${u}, swiper-slide${u}`)[0];let a,d,c;if(n)if(s.loop){let u=r-t.virtual.slidesBefore;u<0&&(u=t.virtual.slides.length+u),u>=t.virtual.slides.length&&(u-=t.virtual.slides.length),a=l(`[data-swiper-slide-index="${u}"]`)}else a=l(`[data-swiper-slide-index="${r}"]`);else o?(a=e.find(u=>u.column===r),c=e.find(u=>u.column===r+1),d=e.find(u=>u.column===r-1)):a=e[r];a&&(o||(c=ft(a,`.${s.slideClass}, swiper-slide`)[0],s.loop&&!c&&(c=e[0]),d=ut(a,`.${s.slideClass}, swiper-slide`)[0],s.loop&&!d===0&&(d=e[e.length-1]))),e.forEach(u=>{ce(u,u===a,s.slideActiveClass),ce(u,u===c,s.slideNextClass),ce(u,u===d,s.slidePrevClass)}),t.emitSlidesClasses()}const J=(t,e)=>{if(!t||t.destroyed||!t.params)return;const s=()=>t.isElement?"swiper-slide":`.${t.params.slideClass}`,i=e.closest(s());if(i){let r=i.querySelector(`.${t.params.lazyPreloaderClass}`);!r&&t.isElement&&(i.shadowRoot?r=i.shadowRoot.querySelector(`.${t.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{i.shadowRoot&&(r=i.shadowRoot.querySelector(`.${t.params.lazyPreloaderClass}`),r&&r.remove())})),r&&r.remove()}},ue=(t,e)=>{if(!t.slides[e])return;const s=t.slides[e].querySelector('[loading="lazy"]');s&&s.removeAttribute("loading")},ge=t=>{if(!t||t.destroyed||!t.params)return;let e=t.params.lazyPreloadPrevNext;const s=t.slides.length;if(!s||!e||e<0)return;e=Math.min(e,s);const i=t.params.slidesPerView==="auto"?t.slidesPerViewDynamic():Math.ceil(t.params.slidesPerView),r=t.activeIndex;if(t.params.grid&&t.params.grid.rows>1){const o=r,l=[o-e];l.push(...Array.from({length:e}).map((a,d)=>o+i+d)),t.slides.forEach((a,d)=>{l.includes(a.column)&&ue(t,d)});return}const n=r+i-1;if(t.params.rewind||t.params.loop)for(let o=r-e;o<=n+e;o+=1){const l=(o%s+s)%s;(l<r||l>n)&&ue(t,l)}else for(let o=Math.max(r-e,0);o<=Math.min(n+e,s-1);o+=1)o!==r&&(o>n||o<r)&&ue(t,o)};function Pt(t){const{slidesGrid:e,params:s}=t,i=t.rtlTranslate?t.translate:-t.translate;let r;for(let n=0;n<e.length;n+=1)typeof e[n+1]<"u"?i>=e[n]&&i<e[n+1]-(e[n+1]-e[n])/2?r=n:i>=e[n]&&i<e[n+1]&&(r=n+1):i>=e[n]&&(r=n);return s.normalizeSlideIndex&&(r<0||typeof r>"u")&&(r=0),r}function Mt(t){const e=this,s=e.rtlTranslate?e.translate:-e.translate,{snapGrid:i,params:r,activeIndex:n,realIndex:o,snapIndex:l}=e;let a=t,d;const c=v=>{let m=v-e.virtual.slidesBefore;return m<0&&(m=e.virtual.slides.length+m),m>=e.virtual.slides.length&&(m-=e.virtual.slides.length),m};if(typeof a>"u"&&(a=Pt(e)),i.indexOf(s)>=0)d=i.indexOf(s);else{const v=Math.min(r.slidesPerGroupSkip,a);d=v+Math.floor((a-v)/r.slidesPerGroup)}if(d>=i.length&&(d=i.length-1),a===n&&!e.params.loop){d!==l&&(e.snapIndex=d,e.emit("snapIndexChange"));return}if(a===n&&e.params.loop&&e.virtual&&e.params.virtual.enabled){e.realIndex=c(a);return}const u=e.grid&&r.grid&&r.grid.rows>1;let g;if(e.virtual&&r.virtual.enabled&&r.loop)g=c(a);else if(u){const v=e.slides.find(w=>w.column===a);let m=parseInt(v.getAttribute("data-swiper-slide-index"),10);Number.isNaN(m)&&(m=Math.max(e.slides.indexOf(v),0)),g=Math.floor(m/r.grid.rows)}else if(e.slides[a]){const v=e.slides[a].getAttribute("data-swiper-slide-index");v?g=parseInt(v,10):g=a}else g=a;Object.assign(e,{previousSnapIndex:l,snapIndex:d,previousRealIndex:o,realIndex:g,previousIndex:n,activeIndex:a}),e.initialized&&ge(e),e.emit("activeIndexChange"),e.emit("snapIndexChange"),(e.initialized||e.params.runCallbacksOnInit)&&(o!==g&&e.emit("realIndexChange"),e.emit("slideChange"))}function It(t,e){const s=this,i=s.params;let r=t.closest(`.${i.slideClass}, swiper-slide`);!r&&s.isElement&&e&&e.length>1&&e.includes(t)&&[...e.slice(e.indexOf(t)+1,e.length)].forEach(l=>{!r&&l.matches&&l.matches(`.${i.slideClass}, swiper-slide`)&&(r=l)});let n=!1,o;if(r){for(let l=0;l<s.slides.length;l+=1)if(s.slides[l]===r){n=!0,o=l;break}}if(r&&n)s.clickedSlide=r,s.virtual&&s.params.virtual.enabled?s.clickedIndex=parseInt(r.getAttribute("data-swiper-slide-index"),10):s.clickedIndex=o;else{s.clickedSlide=void 0,s.clickedIndex=void 0;return}i.slideToClickedSlide&&s.clickedIndex!==void 0&&s.clickedIndex!==s.activeIndex&&s.slideToClickedSlide()}var Lt={updateSize:bt,updateSlides:St,updateAutoHeight:xt,updateSlidesOffset:Tt,updateSlidesProgress:yt,updateProgress:Et,updateSlidesClasses:Ct,updateActiveIndex:Mt,updateClickedSlide:It};function Ot(t){t===void 0&&(t=this.isHorizontal()?"x":"y");const e=this,{params:s,rtlTranslate:i,translate:r,wrapperEl:n}=e;if(s.virtualTranslate)return i?-r:r;if(s.cssMode)return r;let o=lt(n,t);return o+=e.cssOverflowAdjustment(),i&&(o=-o),o||0}function zt(t,e){const s=this,{rtlTranslate:i,params:r,wrapperEl:n,progress:o}=s;let l=0,a=0;const d=0;s.isHorizontal()?l=i?-t:t:a=t,r.roundLengths&&(l=Math.floor(l),a=Math.floor(a)),s.previousTranslate=s.translate,s.translate=s.isHorizontal()?l:a,r.cssMode?n[s.isHorizontal()?"scrollLeft":"scrollTop"]=s.isHorizontal()?-l:-a:r.virtualTranslate||(s.isHorizontal()?l-=s.cssOverflowAdjustment():a-=s.cssOverflowAdjustment(),n.style.transform=`translate3d(${l}px, ${a}px, ${d}px)`);let c;const u=s.maxTranslate()-s.minTranslate();u===0?c=0:c=(t-s.minTranslate())/u,c!==o&&s.updateProgress(t),s.emit("setTranslate",s.translate,e)}function Nt(){return-this.snapGrid[0]}function At(){return-this.snapGrid[this.snapGrid.length-1]}function _t(t,e,s,i,r){t===void 0&&(t=0),e===void 0&&(e=this.params.speed),s===void 0&&(s=!0),i===void 0&&(i=!0);const n=this,{params:o,wrapperEl:l}=n;if(n.animating&&o.preventInteractionOnTransition)return!1;const a=n.minTranslate(),d=n.maxTranslate();let c;if(i&&t>a?c=a:i&&t<d?c=d:c=t,n.updateProgress(c),o.cssMode){const u=n.isHorizontal();if(e===0)l[u?"scrollLeft":"scrollTop"]=-c;else{if(!n.support.smoothScroll)return Ne({swiper:n,targetPosition:-c,side:u?"left":"top"}),!0;l.scrollTo({[u?"left":"top"]:-c,behavior:"smooth"})}return!0}return e===0?(n.setTransition(0),n.setTranslate(c),s&&(n.emit("beforeTransitionStart",e,r),n.emit("transitionEnd"))):(n.setTransition(e),n.setTranslate(c),s&&(n.emit("beforeTransitionStart",e,r),n.emit("transitionStart")),n.animating||(n.animating=!0,n.onTranslateToWrapperTransitionEnd||(n.onTranslateToWrapperTransitionEnd=function(g){!n||n.destroyed||g.target===this&&(n.wrapperEl.removeEventListener("transitionend",n.onTranslateToWrapperTransitionEnd),n.onTranslateToWrapperTransitionEnd=null,delete n.onTranslateToWrapperTransitionEnd,n.animating=!1,s&&n.emit("transitionEnd"))}),n.wrapperEl.addEventListener("transitionend",n.onTranslateToWrapperTransitionEnd))),!0}var jt={getTranslate:Ot,setTranslate:zt,minTranslate:Nt,maxTranslate:At,translateTo:_t};function $t(t,e){const s=this;s.params.cssMode||(s.wrapperEl.style.transitionDuration=`${t}ms`,s.wrapperEl.style.transitionDelay=t===0?"0ms":""),s.emit("setTransition",t,e)}function Be(t){let{swiper:e,runCallbacks:s,direction:i,step:r}=t;const{activeIndex:n,previousIndex:o}=e;let l=i;l||(n>o?l="next":n<o?l="prev":l="reset"),e.emit(`transition${r}`),s&&l==="reset"?e.emit(`slideResetTransition${r}`):s&&n!==o&&(e.emit(`slideChangeTransition${r}`),l==="next"?e.emit(`slideNextTransition${r}`):e.emit(`slidePrevTransition${r}`))}function Bt(t,e){t===void 0&&(t=!0);const s=this,{params:i}=s;i.cssMode||(i.autoHeight&&s.updateAutoHeight(),Be({swiper:s,runCallbacks:t,direction:e,step:"Start"}))}function Dt(t,e){t===void 0&&(t=!0);const s=this,{params:i}=s;s.animating=!1,!i.cssMode&&(s.setTransition(0),Be({swiper:s,runCallbacks:t,direction:e,step:"End"}))}var Gt={setTransition:$t,transitionStart:Bt,transitionEnd:Dt};function Vt(t,e,s,i,r){t===void 0&&(t=0),s===void 0&&(s=!0),typeof t=="string"&&(t=parseInt(t,10));const n=this;let o=t;o<0&&(o=0);const{params:l,snapGrid:a,slidesGrid:d,previousIndex:c,activeIndex:u,rtlTranslate:g,wrapperEl:v,enabled:m}=n;if(!m&&!i&&!r||n.destroyed||n.animating&&l.preventInteractionOnTransition)return!1;typeof e>"u"&&(e=n.params.speed);const w=Math.min(n.params.slidesPerGroupSkip,o);let T=w+Math.floor((o-w)/n.params.slidesPerGroup);T>=a.length&&(T=a.length-1);const h=-a[T];if(l.normalizeSlideIndex)for(let M=0;M<d.length;M+=1){const I=-Math.floor(h*100),E=Math.floor(d[M]*100),C=Math.floor(d[M+1]*100);typeof d[M+1]<"u"?I>=E&&I<C-(C-E)/2?o=M:I>=E&&I<C&&(o=M+1):I>=E&&(o=M)}if(n.initialized&&o!==u&&(!n.allowSlideNext&&(g?h>n.translate&&h>n.minTranslate():h<n.translate&&h<n.minTranslate())||!n.allowSlidePrev&&h>n.translate&&h>n.maxTranslate()&&(u||0)!==o))return!1;o!==(c||0)&&s&&n.emit("beforeSlideChangeStart"),n.updateProgress(h);let f;o>u?f="next":o<u?f="prev":f="reset";const p=n.virtual&&n.params.virtual.enabled;if(!(p&&r)&&(g&&-h===n.translate||!g&&h===n.translate))return n.updateActiveIndex(o),l.autoHeight&&n.updateAutoHeight(),n.updateSlidesClasses(),l.effect!=="slide"&&n.setTranslate(h),f!=="reset"&&(n.transitionStart(s,f),n.transitionEnd(s,f)),!1;if(l.cssMode){const M=n.isHorizontal(),I=g?h:-h;if(e===0)p&&(n.wrapperEl.style.scrollSnapType="none",n._immediateVirtual=!0),p&&!n._cssModeVirtualInitialSet&&n.params.initialSlide>0?(n._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{v[M?"scrollLeft":"scrollTop"]=I})):v[M?"scrollLeft":"scrollTop"]=I,p&&requestAnimationFrame(()=>{n.wrapperEl.style.scrollSnapType="",n._immediateVirtual=!1});else{if(!n.support.smoothScroll)return Ne({swiper:n,targetPosition:I,side:M?"left":"top"}),!0;v.scrollTo({[M?"left":"top"]:I,behavior:"smooth"})}return!0}const y=$e().isSafari;return p&&!r&&y&&n.isElement&&n.virtual.update(!1,!1,o),n.setTransition(e),n.setTranslate(h),n.updateActiveIndex(o),n.updateSlidesClasses(),n.emit("beforeTransitionStart",e,i),n.transitionStart(s,f),e===0?n.transitionEnd(s,f):n.animating||(n.animating=!0,n.onSlideToWrapperTransitionEnd||(n.onSlideToWrapperTransitionEnd=function(I){!n||n.destroyed||I.target===this&&(n.wrapperEl.removeEventListener("transitionend",n.onSlideToWrapperTransitionEnd),n.onSlideToWrapperTransitionEnd=null,delete n.onSlideToWrapperTransitionEnd,n.transitionEnd(s,f))}),n.wrapperEl.addEventListener("transitionend",n.onSlideToWrapperTransitionEnd)),!0}function kt(t,e,s,i){t===void 0&&(t=0),s===void 0&&(s=!0),typeof t=="string"&&(t=parseInt(t,10));const r=this;if(r.destroyed)return;typeof e>"u"&&(e=r.params.speed);const n=r.grid&&r.params.grid&&r.params.grid.rows>1;let o=t;if(r.params.loop)if(r.virtual&&r.params.virtual.enabled)o=o+r.virtual.slidesBefore;else{let l;if(n){const g=o*r.params.grid.rows;l=r.slides.find(v=>v.getAttribute("data-swiper-slide-index")*1===g).column}else l=r.getSlideIndexByData(o);const a=n?Math.ceil(r.slides.length/r.params.grid.rows):r.slides.length,{centeredSlides:d}=r.params;let c=r.params.slidesPerView;c==="auto"?c=r.slidesPerViewDynamic():(c=Math.ceil(parseFloat(r.params.slidesPerView,10)),d&&c%2===0&&(c=c+1));let u=a-l<c;if(d&&(u=u||l<Math.ceil(c/2)),i&&d&&r.params.slidesPerView!=="auto"&&!n&&(u=!1),u){const g=d?l<r.activeIndex?"prev":"next":l-r.activeIndex-1<r.params.slidesPerView?"next":"prev";r.loopFix({direction:g,slideTo:!0,activeSlideIndex:g==="next"?l+1:l-a+1,slideRealIndex:g==="next"?r.realIndex:void 0})}if(n){const g=o*r.params.grid.rows;o=r.slides.find(v=>v.getAttribute("data-swiper-slide-index")*1===g).column}else o=r.getSlideIndexByData(o)}return requestAnimationFrame(()=>{r.slideTo(o,e,s,i)}),r}function Ft(t,e,s){e===void 0&&(e=!0);const i=this,{enabled:r,params:n,animating:o}=i;if(!r||i.destroyed)return i;typeof t>"u"&&(t=i.params.speed);let l=n.slidesPerGroup;n.slidesPerView==="auto"&&n.slidesPerGroup===1&&n.slidesPerGroupAuto&&(l=Math.max(i.slidesPerViewDynamic("current",!0),1));const a=i.activeIndex<n.slidesPerGroupSkip?1:l,d=i.virtual&&n.virtual.enabled;if(n.loop){if(o&&!d&&n.loopPreventsSliding)return!1;if(i.loopFix({direction:"next"}),i._clientLeft=i.wrapperEl.clientLeft,i.activeIndex===i.slides.length-1&&n.cssMode)return requestAnimationFrame(()=>{i.slideTo(i.activeIndex+a,t,e,s)}),!0}return n.rewind&&i.isEnd?i.slideTo(0,t,e,s):i.slideTo(i.activeIndex+a,t,e,s)}function Rt(t,e,s){e===void 0&&(e=!0);const i=this,{params:r,snapGrid:n,slidesGrid:o,rtlTranslate:l,enabled:a,animating:d}=i;if(!a||i.destroyed)return i;typeof t>"u"&&(t=i.params.speed);const c=i.virtual&&r.virtual.enabled;if(r.loop){if(d&&!c&&r.loopPreventsSliding)return!1;i.loopFix({direction:"prev"}),i._clientLeft=i.wrapperEl.clientLeft}const u=l?i.translate:-i.translate;function g(f){return f<0?-Math.floor(Math.abs(f)):Math.floor(f)}const v=g(u),m=n.map(f=>g(f)),w=r.freeMode&&r.freeMode.enabled;let T=n[m.indexOf(v)-1];if(typeof T>"u"&&(r.cssMode||w)){let f;n.forEach((p,b)=>{v>=p&&(f=b)}),typeof f<"u"&&(T=w?n[f]:n[f>0?f-1:f])}let h=0;if(typeof T<"u"&&(h=o.indexOf(T),h<0&&(h=i.activeIndex-1),r.slidesPerView==="auto"&&r.slidesPerGroup===1&&r.slidesPerGroupAuto&&(h=h-i.slidesPerViewDynamic("previous",!0)+1,h=Math.max(h,0))),r.rewind&&i.isBeginning){const f=i.params.virtual&&i.params.virtual.enabled&&i.virtual?i.virtual.slides.length-1:i.slides.length-1;return i.slideTo(f,t,e,s)}else if(r.loop&&i.activeIndex===0&&r.cssMode)return requestAnimationFrame(()=>{i.slideTo(h,t,e,s)}),!0;return i.slideTo(h,t,e,s)}function Ht(t,e,s){e===void 0&&(e=!0);const i=this;if(!i.destroyed)return typeof t>"u"&&(t=i.params.speed),i.slideTo(i.activeIndex,t,e,s)}function qt(t,e,s,i){e===void 0&&(e=!0),i===void 0&&(i=.5);const r=this;if(r.destroyed)return;typeof t>"u"&&(t=r.params.speed);let n=r.activeIndex;const o=Math.min(r.params.slidesPerGroupSkip,n),l=o+Math.floor((n-o)/r.params.slidesPerGroup),a=r.rtlTranslate?r.translate:-r.translate;if(a>=r.snapGrid[l]){const d=r.snapGrid[l],c=r.snapGrid[l+1];a-d>(c-d)*i&&(n+=r.params.slidesPerGroup)}else{const d=r.snapGrid[l-1],c=r.snapGrid[l];a-d<=(c-d)*i&&(n-=r.params.slidesPerGroup)}return n=Math.max(n,0),n=Math.min(n,r.slidesGrid.length-1),r.slideTo(n,t,e,s)}function Wt(){const t=this;if(t.destroyed)return;const{params:e,slidesEl:s}=t,i=e.slidesPerView==="auto"?t.slidesPerViewDynamic():e.slidesPerView;let r=t.clickedIndex,n;const o=t.isElement?"swiper-slide":`.${e.slideClass}`;if(e.loop){if(t.animating)return;n=parseInt(t.clickedSlide.getAttribute("data-swiper-slide-index"),10),e.centeredSlides?r<t.loopedSlides-i/2||r>t.slides.length-t.loopedSlides+i/2?(t.loopFix(),r=t.getSlideIndex(R(s,`${o}[data-swiper-slide-index="${n}"]`)[0]),me(()=>{t.slideTo(r)})):t.slideTo(r):r>t.slides.length-i?(t.loopFix(),r=t.getSlideIndex(R(s,`${o}[data-swiper-slide-index="${n}"]`)[0]),me(()=>{t.slideTo(r)})):t.slideTo(r)}else t.slideTo(r)}var Yt={slideTo:Vt,slideToLoop:kt,slideNext:Ft,slidePrev:Rt,slideReset:Ht,slideToClosest:qt,slideToClickedSlide:Wt};function Xt(t,e){const s=this,{params:i,slidesEl:r}=s;if(!i.loop||s.virtual&&s.params.virtual.enabled)return;const n=()=>{R(r,`.${i.slideClass}, swiper-slide`).forEach((g,v)=>{g.setAttribute("data-swiper-slide-index",v)})},o=s.grid&&i.grid&&i.grid.rows>1,l=i.slidesPerGroup*(o?i.grid.rows:1),a=s.slides.length%l!==0,d=o&&s.slides.length%i.grid.rows!==0,c=u=>{for(let g=0;g<u;g+=1){const v=s.isElement?se("swiper-slide",[i.slideBlankClass]):se("div",[i.slideClass,i.slideBlankClass]);s.slidesEl.append(v)}};if(a){if(i.loopAddBlankSlides){const u=l-s.slides.length%l;c(u),s.recalcSlides(),s.updateSlides()}else te("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");n()}else if(d){if(i.loopAddBlankSlides){const u=i.grid.rows-s.slides.length%i.grid.rows;c(u),s.recalcSlides(),s.updateSlides()}else te("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");n()}else n();s.loopFix({slideRealIndex:t,direction:i.centeredSlides?void 0:"next",initial:e})}function Ut(t){let{slideRealIndex:e,slideTo:s=!0,direction:i,setTranslate:r,activeSlideIndex:n,initial:o,byController:l,byMousewheel:a}=t===void 0?{}:t;const d=this;if(!d.params.loop)return;d.emit("beforeLoopFix");const{slides:c,allowSlidePrev:u,allowSlideNext:g,slidesEl:v,params:m}=d,{centeredSlides:w,initialSlide:T}=m;if(d.allowSlidePrev=!0,d.allowSlideNext=!0,d.virtual&&m.virtual.enabled){s&&(!m.centeredSlides&&d.snapIndex===0?d.slideTo(d.virtual.slides.length,0,!1,!0):m.centeredSlides&&d.snapIndex<m.slidesPerView?d.slideTo(d.virtual.slides.length+d.snapIndex,0,!1,!0):d.snapIndex===d.snapGrid.length-1&&d.slideTo(d.virtual.slidesBefore,0,!1,!0)),d.allowSlidePrev=u,d.allowSlideNext=g,d.emit("loopFix");return}let h=m.slidesPerView;h==="auto"?h=d.slidesPerViewDynamic():(h=Math.ceil(parseFloat(m.slidesPerView,10)),w&&h%2===0&&(h=h+1));const f=m.slidesPerGroupAuto?h:m.slidesPerGroup;let p=f;p%f!==0&&(p+=f-p%f),p+=m.loopAdditionalSlides,d.loopedSlides=p;const b=d.grid&&m.grid&&m.grid.rows>1;c.length<h+p||d.params.effect==="cards"&&c.length<h+p*2?te("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):b&&m.grid.fill==="row"&&te("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");const x=[],y=[],M=b?Math.ceil(c.length/m.grid.rows):c.length,I=o&&M-T<h&&!w;let E=I?T:d.activeIndex;typeof n>"u"?n=d.getSlideIndex(c.find(O=>O.classList.contains(m.slideActiveClass))):E=n;const C=i==="next"||!i,P=i==="prev"||!i;let L=0,z=0;const A=(b?c[n].column:n)+(w&&typeof r>"u"?-h/2+.5:0);if(A<p){L=Math.max(p-A,f);for(let O=0;O<p-A;O+=1){const B=O-Math.floor(O/M)*M;if(b){const G=M-B-1;for(let _=c.length-1;_>=0;_-=1)c[_].column===G&&x.push(_)}else x.push(M-B-1)}}else if(A+h>M-p){z=Math.max(A-(M-p*2),f),I&&(z=Math.max(z,h-M+T+1));for(let O=0;O<z;O+=1){const B=O-Math.floor(O/M)*M;b?c.forEach((G,_)=>{G.column===B&&y.push(_)}):y.push(B)}}if(d.__preventObserver__=!0,requestAnimationFrame(()=>{d.__preventObserver__=!1}),d.params.effect==="cards"&&c.length<h+p*2&&(y.includes(n)&&y.splice(y.indexOf(n),1),x.includes(n)&&x.splice(x.indexOf(n),1)),P&&x.forEach(O=>{c[O].swiperLoopMoveDOM=!0,v.prepend(c[O]),c[O].swiperLoopMoveDOM=!1}),C&&y.forEach(O=>{c[O].swiperLoopMoveDOM=!0,v.append(c[O]),c[O].swiperLoopMoveDOM=!1}),d.recalcSlides(),m.slidesPerView==="auto"?d.updateSlides():b&&(x.length>0&&P||y.length>0&&C)&&d.slides.forEach((O,B)=>{d.grid.updateSlide(B,O,d.slides)}),m.watchSlidesProgress&&d.updateSlidesOffset(),s){if(x.length>0&&P){if(typeof e>"u"){const O=d.slidesGrid[E],G=d.slidesGrid[E+L]-O;a?d.setTranslate(d.translate-G):(d.slideTo(E+Math.ceil(L),0,!1,!0),r&&(d.touchEventsData.startTranslate=d.touchEventsData.startTranslate-G,d.touchEventsData.currentTranslate=d.touchEventsData.currentTranslate-G))}else if(r){const O=b?x.length/m.grid.rows:x.length;d.slideTo(d.activeIndex+O,0,!1,!0),d.touchEventsData.currentTranslate=d.translate}}else if(y.length>0&&C)if(typeof e>"u"){const O=d.slidesGrid[E],G=d.slidesGrid[E-z]-O;a?d.setTranslate(d.translate-G):(d.slideTo(E-z,0,!1,!0),r&&(d.touchEventsData.startTranslate=d.touchEventsData.startTranslate-G,d.touchEventsData.currentTranslate=d.touchEventsData.currentTranslate-G))}else{const O=b?y.length/m.grid.rows:y.length;d.slideTo(d.activeIndex-O,0,!1,!0)}}if(d.allowSlidePrev=u,d.allowSlideNext=g,d.controller&&d.controller.control&&!l){const O={slideRealIndex:e,direction:i,setTranslate:r,activeSlideIndex:n,byController:!0};Array.isArray(d.controller.control)?d.controller.control.forEach(B=>{!B.destroyed&&B.params.loop&&B.loopFix({...O,slideTo:B.params.slidesPerView===m.slidesPerView?s:!1})}):d.controller.control instanceof d.constructor&&d.controller.control.params.loop&&d.controller.control.loopFix({...O,slideTo:d.controller.control.params.slidesPerView===m.slidesPerView?s:!1})}d.emit("loopFix")}function Kt(){const t=this,{params:e,slidesEl:s}=t;if(!e.loop||!s||t.virtual&&t.params.virtual.enabled)return;t.recalcSlides();const i=[];t.slides.forEach(r=>{const n=typeof r.swiperSlideIndex>"u"?r.getAttribute("data-swiper-slide-index")*1:r.swiperSlideIndex;i[n]=r}),t.slides.forEach(r=>{r.removeAttribute("data-swiper-slide-index")}),i.forEach(r=>{s.append(r)}),t.recalcSlides(),t.slideTo(t.realIndex,0)}var Qt={loopCreate:Xt,loopFix:Ut,loopDestroy:Kt};function Zt(t){const e=this;if(!e.params.simulateTouch||e.params.watchOverflow&&e.isLocked||e.params.cssMode)return;const s=e.params.touchEventsTarget==="container"?e.el:e.wrapperEl;e.isElement&&(e.__preventObserver__=!0),s.style.cursor="move",s.style.cursor=t?"grabbing":"grab",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1})}function Jt(){const t=this;t.params.watchOverflow&&t.isLocked||t.params.cssMode||(t.isElement&&(t.__preventObserver__=!0),t[t.params.touchEventsTarget==="container"?"el":"wrapperEl"].style.cursor="",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1}))}var es={setGrabCursor:Zt,unsetGrabCursor:Jt};function ts(t,e){e===void 0&&(e=this);function s(i){if(!i||i===W()||i===V())return null;i.assignedSlot&&(i=i.assignedSlot);const r=i.closest(t);return!r&&!i.getRootNode?null:r||s(i.getRootNode().host)}return s(e)}function Ce(t,e,s){const i=V(),{params:r}=t,n=r.edgeSwipeDetection,o=r.edgeSwipeThreshold;return n&&(s<=o||s>=i.innerWidth-o)?n==="prevent"?(e.preventDefault(),!0):!1:!0}function ss(t){const e=this,s=W();let i=t;i.originalEvent&&(i=i.originalEvent);const r=e.touchEventsData;if(i.type==="pointerdown"){if(r.pointerId!==null&&r.pointerId!==i.pointerId)return;r.pointerId=i.pointerId}else i.type==="touchstart"&&i.targetTouches.length===1&&(r.touchId=i.targetTouches[0].identifier);if(i.type==="touchstart"){Ce(e,i,i.targetTouches[0].pageX);return}const{params:n,touches:o,enabled:l}=e;if(!l||!n.simulateTouch&&i.pointerType==="mouse"||e.animating&&n.preventInteractionOnTransition)return;!e.animating&&n.cssMode&&n.loop&&e.loopFix();let a=i.target;if(n.touchEventsTarget==="wrapper"&&!ct(a,e.wrapperEl)||"which"in i&&i.which===3||"button"in i&&i.button>0||r.isTouched&&r.isMoved)return;const d=!!n.noSwipingClass&&n.noSwipingClass!=="",c=i.composedPath?i.composedPath():i.path;d&&i.target&&i.target.shadowRoot&&c&&(a=c[0]);const u=n.noSwipingSelector?n.noSwipingSelector:`.${n.noSwipingClass}`,g=!!(i.target&&i.target.shadowRoot);if(n.noSwiping&&(g?ts(u,a):a.closest(u))){e.allowClick=!0;return}if(n.swipeHandler&&!a.closest(n.swipeHandler))return;o.currentX=i.pageX,o.currentY=i.pageY;const v=o.currentX,m=o.currentY;if(!Ce(e,i,v))return;Object.assign(r,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),o.startX=v,o.startY=m,r.touchStartTime=ee(),e.allowClick=!0,e.updateSize(),e.swipeDirection=void 0,n.threshold>0&&(r.allowThresholdMove=!1);let w=!0;a.matches(r.focusableElements)&&(w=!1,a.nodeName==="SELECT"&&(r.isTouched=!1)),s.activeElement&&s.activeElement.matches(r.focusableElements)&&s.activeElement!==a&&(i.pointerType==="mouse"||i.pointerType!=="mouse"&&!a.matches(r.focusableElements))&&s.activeElement.blur();const T=w&&e.allowTouchMove&&n.touchStartPreventDefault;(n.touchStartForcePreventDefault||T)&&!a.isContentEditable&&i.preventDefault(),n.freeMode&&n.freeMode.enabled&&e.freeMode&&e.animating&&!n.cssMode&&e.freeMode.onTouchStart(),e.emit("touchStart",i)}function is(t){const e=W(),s=this,i=s.touchEventsData,{params:r,touches:n,rtlTranslate:o,enabled:l}=s;if(!l||!r.simulateTouch&&t.pointerType==="mouse")return;let a=t;if(a.originalEvent&&(a=a.originalEvent),a.type==="pointermove"&&(i.touchId!==null||a.pointerId!==i.pointerId))return;let d;if(a.type==="touchmove"){if(d=[...a.changedTouches].find(x=>x.identifier===i.touchId),!d||d.identifier!==i.touchId)return}else d=a;if(!i.isTouched){i.startMoving&&i.isScrolling&&s.emit("touchMoveOpposite",a);return}const c=d.pageX,u=d.pageY;if(a.preventedByNestedSwiper){n.startX=c,n.startY=u;return}if(!s.allowTouchMove){a.target.matches(i.focusableElements)||(s.allowClick=!1),i.isTouched&&(Object.assign(n,{startX:c,startY:u,currentX:c,currentY:u}),i.touchStartTime=ee());return}if(r.touchReleaseOnEdges&&!r.loop)if(s.isVertical()){if(u<n.startY&&s.translate<=s.maxTranslate()||u>n.startY&&s.translate>=s.minTranslate()){i.isTouched=!1,i.isMoved=!1;return}}else{if(o&&(c>n.startX&&-s.translate<=s.maxTranslate()||c<n.startX&&-s.translate>=s.minTranslate()))return;if(!o&&(c<n.startX&&s.translate<=s.maxTranslate()||c>n.startX&&s.translate>=s.minTranslate()))return}if(e.activeElement&&e.activeElement.matches(i.focusableElements)&&e.activeElement!==a.target&&a.pointerType!=="mouse"&&e.activeElement.blur(),e.activeElement&&a.target===e.activeElement&&a.target.matches(i.focusableElements)){i.isMoved=!0,s.allowClick=!1;return}i.allowTouchCallbacks&&s.emit("touchMove",a),n.previousX=n.currentX,n.previousY=n.currentY,n.currentX=c,n.currentY=u;const g=n.currentX-n.startX,v=n.currentY-n.startY;if(s.params.threshold&&Math.sqrt(g**2+v**2)<s.params.threshold)return;if(typeof i.isScrolling>"u"){let x;s.isHorizontal()&&n.currentY===n.startY||s.isVertical()&&n.currentX===n.startX?i.isScrolling=!1:g*g+v*v>=25&&(x=Math.atan2(Math.abs(v),Math.abs(g))*180/Math.PI,i.isScrolling=s.isHorizontal()?x>r.touchAngle:90-x>r.touchAngle)}if(i.isScrolling&&s.emit("touchMoveOpposite",a),typeof i.startMoving>"u"&&(n.currentX!==n.startX||n.currentY!==n.startY)&&(i.startMoving=!0),i.isScrolling||a.type==="touchmove"&&i.preventTouchMoveFromPointerMove){i.isTouched=!1;return}if(!i.startMoving)return;s.allowClick=!1,!r.cssMode&&a.cancelable&&a.preventDefault(),r.touchMoveStopPropagation&&!r.nested&&a.stopPropagation();let m=s.isHorizontal()?g:v,w=s.isHorizontal()?n.currentX-n.previousX:n.currentY-n.previousY;r.oneWayMovement&&(m=Math.abs(m)*(o?1:-1),w=Math.abs(w)*(o?1:-1)),n.diff=m,m*=r.touchRatio,o&&(m=-m,w=-w);const T=s.touchesDirection;s.swipeDirection=m>0?"prev":"next",s.touchesDirection=w>0?"prev":"next";const h=s.params.loop&&!r.cssMode,f=s.touchesDirection==="next"&&s.allowSlideNext||s.touchesDirection==="prev"&&s.allowSlidePrev;if(!i.isMoved){if(h&&f&&s.loopFix({direction:s.swipeDirection}),i.startTranslate=s.getTranslate(),s.setTransition(0),s.animating){const x=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});s.wrapperEl.dispatchEvent(x)}i.allowMomentumBounce=!1,r.grabCursor&&(s.allowSlideNext===!0||s.allowSlidePrev===!0)&&s.setGrabCursor(!0),s.emit("sliderFirstMove",a)}if(new Date().getTime(),r._loopSwapReset!==!1&&i.isMoved&&i.allowThresholdMove&&T!==s.touchesDirection&&h&&f&&Math.abs(m)>=1){Object.assign(n,{startX:c,startY:u,currentX:c,currentY:u,startTranslate:i.currentTranslate}),i.loopSwapReset=!0,i.startTranslate=i.currentTranslate;return}s.emit("sliderMove",a),i.isMoved=!0,i.currentTranslate=m+i.startTranslate;let p=!0,b=r.resistanceRatio;if(r.touchReleaseOnEdges&&(b=0),m>0?(h&&f&&i.allowThresholdMove&&i.currentTranslate>(r.centeredSlides?s.minTranslate()-s.slidesSizesGrid[s.activeIndex+1]-(r.slidesPerView!=="auto"&&s.slides.length-r.slidesPerView>=2?s.slidesSizesGrid[s.activeIndex+1]+s.params.spaceBetween:0)-s.params.spaceBetween:s.minTranslate())&&s.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),i.currentTranslate>s.minTranslate()&&(p=!1,r.resistance&&(i.currentTranslate=s.minTranslate()-1+(-s.minTranslate()+i.startTranslate+m)**b))):m<0&&(h&&f&&i.allowThresholdMove&&i.currentTranslate<(r.centeredSlides?s.maxTranslate()+s.slidesSizesGrid[s.slidesSizesGrid.length-1]+s.params.spaceBetween+(r.slidesPerView!=="auto"&&s.slides.length-r.slidesPerView>=2?s.slidesSizesGrid[s.slidesSizesGrid.length-1]+s.params.spaceBetween:0):s.maxTranslate())&&s.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:s.slides.length-(r.slidesPerView==="auto"?s.slidesPerViewDynamic():Math.ceil(parseFloat(r.slidesPerView,10)))}),i.currentTranslate<s.maxTranslate()&&(p=!1,r.resistance&&(i.currentTranslate=s.maxTranslate()+1-(s.maxTranslate()-i.startTranslate-m)**b))),p&&(a.preventedByNestedSwiper=!0),!s.allowSlideNext&&s.swipeDirection==="next"&&i.currentTranslate<i.startTranslate&&(i.currentTranslate=i.startTranslate),!s.allowSlidePrev&&s.swipeDirection==="prev"&&i.currentTranslate>i.startTranslate&&(i.currentTranslate=i.startTranslate),!s.allowSlidePrev&&!s.allowSlideNext&&(i.currentTranslate=i.startTranslate),r.threshold>0)if(Math.abs(m)>r.threshold||i.allowThresholdMove){if(!i.allowThresholdMove){i.allowThresholdMove=!0,n.startX=n.currentX,n.startY=n.currentY,i.currentTranslate=i.startTranslate,n.diff=s.isHorizontal()?n.currentX-n.startX:n.currentY-n.startY;return}}else{i.currentTranslate=i.startTranslate;return}!r.followFinger||r.cssMode||((r.freeMode&&r.freeMode.enabled&&s.freeMode||r.watchSlidesProgress)&&(s.updateActiveIndex(),s.updateSlidesClasses()),r.freeMode&&r.freeMode.enabled&&s.freeMode&&s.freeMode.onTouchMove(),s.updateProgress(i.currentTranslate),s.setTranslate(i.currentTranslate))}function ns(t){const e=this,s=e.touchEventsData;let i=t;i.originalEvent&&(i=i.originalEvent);let r;if(i.type==="touchend"||i.type==="touchcancel"){if(r=[...i.changedTouches].find(x=>x.identifier===s.touchId),!r||r.identifier!==s.touchId)return}else{if(s.touchId!==null||i.pointerId!==s.pointerId)return;r=i}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(i.type)&&!(["pointercancel","contextmenu"].includes(i.type)&&(e.browser.isSafari||e.browser.isWebView)))return;s.pointerId=null,s.touchId=null;const{params:o,touches:l,rtlTranslate:a,slidesGrid:d,enabled:c}=e;if(!c||!o.simulateTouch&&i.pointerType==="mouse")return;if(s.allowTouchCallbacks&&e.emit("touchEnd",i),s.allowTouchCallbacks=!1,!s.isTouched){s.isMoved&&o.grabCursor&&e.setGrabCursor(!1),s.isMoved=!1,s.startMoving=!1;return}o.grabCursor&&s.isMoved&&s.isTouched&&(e.allowSlideNext===!0||e.allowSlidePrev===!0)&&e.setGrabCursor(!1);const u=ee(),g=u-s.touchStartTime;if(e.allowClick){const x=i.path||i.composedPath&&i.composedPath();e.updateClickedSlide(x&&x[0]||i.target,x),e.emit("tap click",i),g<300&&u-s.lastClickTime<300&&e.emit("doubleTap doubleClick",i)}if(s.lastClickTime=ee(),me(()=>{e.destroyed||(e.allowClick=!0)}),!s.isTouched||!s.isMoved||!e.swipeDirection||l.diff===0&&!s.loopSwapReset||s.currentTranslate===s.startTranslate&&!s.loopSwapReset){s.isTouched=!1,s.isMoved=!1,s.startMoving=!1;return}s.isTouched=!1,s.isMoved=!1,s.startMoving=!1;let v;if(o.followFinger?v=a?e.translate:-e.translate:v=-s.currentTranslate,o.cssMode)return;if(o.freeMode&&o.freeMode.enabled){e.freeMode.onTouchEnd({currentPos:v});return}const m=v>=-e.maxTranslate()&&!e.params.loop;let w=0,T=e.slidesSizesGrid[0];for(let x=0;x<d.length;x+=x<o.slidesPerGroupSkip?1:o.slidesPerGroup){const y=x<o.slidesPerGroupSkip-1?1:o.slidesPerGroup;typeof d[x+y]<"u"?(m||v>=d[x]&&v<d[x+y])&&(w=x,T=d[x+y]-d[x]):(m||v>=d[x])&&(w=x,T=d[d.length-1]-d[d.length-2])}let h=null,f=null;o.rewind&&(e.isBeginning?f=o.virtual&&o.virtual.enabled&&e.virtual?e.virtual.slides.length-1:e.slides.length-1:e.isEnd&&(h=0));const p=(v-d[w])/T,b=w<o.slidesPerGroupSkip-1?1:o.slidesPerGroup;if(g>o.longSwipesMs){if(!o.longSwipes){e.slideTo(e.activeIndex);return}e.swipeDirection==="next"&&(p>=o.longSwipesRatio?e.slideTo(o.rewind&&e.isEnd?h:w+b):e.slideTo(w)),e.swipeDirection==="prev"&&(p>1-o.longSwipesRatio?e.slideTo(w+b):f!==null&&p<0&&Math.abs(p)>o.longSwipesRatio?e.slideTo(f):e.slideTo(w))}else{if(!o.shortSwipes){e.slideTo(e.activeIndex);return}e.navigation&&(i.target===e.navigation.nextEl||i.target===e.navigation.prevEl)?i.target===e.navigation.nextEl?e.slideTo(w+b):e.slideTo(w):(e.swipeDirection==="next"&&e.slideTo(h!==null?h:w+b),e.swipeDirection==="prev"&&e.slideTo(f!==null?f:w))}}function Pe(){const t=this,{params:e,el:s}=t;if(s&&s.offsetWidth===0)return;e.breakpoints&&t.setBreakpoint();const{allowSlideNext:i,allowSlidePrev:r,snapGrid:n}=t,o=t.virtual&&t.params.virtual.enabled;t.allowSlideNext=!0,t.allowSlidePrev=!0,t.updateSize(),t.updateSlides(),t.updateSlidesClasses();const l=o&&e.loop;(e.slidesPerView==="auto"||e.slidesPerView>1)&&t.isEnd&&!t.isBeginning&&!t.params.centeredSlides&&!l?t.slideTo(t.slides.length-1,0,!1,!0):t.params.loop&&!o?t.slideToLoop(t.realIndex,0,!1,!0):t.slideTo(t.activeIndex,0,!1,!0),t.autoplay&&t.autoplay.running&&t.autoplay.paused&&(clearTimeout(t.autoplay.resizeTimeout),t.autoplay.resizeTimeout=setTimeout(()=>{t.autoplay&&t.autoplay.running&&t.autoplay.paused&&t.autoplay.resume()},500)),t.allowSlidePrev=r,t.allowSlideNext=i,t.params.watchOverflow&&n!==t.snapGrid&&t.checkOverflow()}function rs(t){const e=this;e.enabled&&(e.allowClick||(e.params.preventClicks&&t.preventDefault(),e.params.preventClicksPropagation&&e.animating&&(t.stopPropagation(),t.stopImmediatePropagation())))}function as(){const t=this,{wrapperEl:e,rtlTranslate:s,enabled:i}=t;if(!i)return;t.previousTranslate=t.translate,t.isHorizontal()?t.translate=-e.scrollLeft:t.translate=-e.scrollTop,t.translate===0&&(t.translate=0),t.updateActiveIndex(),t.updateSlidesClasses();let r;const n=t.maxTranslate()-t.minTranslate();n===0?r=0:r=(t.translate-t.minTranslate())/n,r!==t.progress&&t.updateProgress(s?-t.translate:t.translate),t.emit("setTranslate",t.translate,!1)}function ls(t){const e=this;J(e,t.target),!(e.params.cssMode||e.params.slidesPerView!=="auto"&&!e.params.autoHeight)&&e.update()}function os(){const t=this;t.documentTouchHandlerProceeded||(t.documentTouchHandlerProceeded=!0,t.params.touchReleaseOnEdges&&(t.el.style.touchAction="auto"))}const De=(t,e)=>{const s=W(),{params:i,el:r,wrapperEl:n,device:o}=t,l=!!i.nested,a=e==="on"?"addEventListener":"removeEventListener",d=e;!r||typeof r=="string"||(s[a]("touchstart",t.onDocumentTouchStart,{passive:!1,capture:l}),r[a]("touchstart",t.onTouchStart,{passive:!1}),r[a]("pointerdown",t.onTouchStart,{passive:!1}),s[a]("touchmove",t.onTouchMove,{passive:!1,capture:l}),s[a]("pointermove",t.onTouchMove,{passive:!1,capture:l}),s[a]("touchend",t.onTouchEnd,{passive:!0}),s[a]("pointerup",t.onTouchEnd,{passive:!0}),s[a]("pointercancel",t.onTouchEnd,{passive:!0}),s[a]("touchcancel",t.onTouchEnd,{passive:!0}),s[a]("pointerout",t.onTouchEnd,{passive:!0}),s[a]("pointerleave",t.onTouchEnd,{passive:!0}),s[a]("contextmenu",t.onTouchEnd,{passive:!0}),(i.preventClicks||i.preventClicksPropagation)&&r[a]("click",t.onClick,!0),i.cssMode&&n[a]("scroll",t.onScroll),i.updateOnWindowResize?t[d](o.ios||o.android?"resize orientationchange observerUpdate":"resize observerUpdate",Pe,!0):t[d]("observerUpdate",Pe,!0),r[a]("load",t.onLoad,{capture:!0}))};function ds(){const t=this,{params:e}=t;t.onTouchStart=ss.bind(t),t.onTouchMove=is.bind(t),t.onTouchEnd=ns.bind(t),t.onDocumentTouchStart=os.bind(t),e.cssMode&&(t.onScroll=as.bind(t)),t.onClick=rs.bind(t),t.onLoad=ls.bind(t),De(t,"on")}function cs(){De(this,"off")}var us={attachEvents:ds,detachEvents:cs};const Me=(t,e)=>t.grid&&e.grid&&e.grid.rows>1;function fs(){const t=this,{realIndex:e,initialized:s,params:i,el:r}=t,n=i.breakpoints;if(!n||n&&Object.keys(n).length===0)return;const o=W(),l=i.breakpointsBase==="window"||!i.breakpointsBase?i.breakpointsBase:"container",a=["window","container"].includes(i.breakpointsBase)||!i.breakpointsBase?t.el:o.querySelector(i.breakpointsBase),d=t.getBreakpoint(n,l,a);if(!d||t.currentBreakpoint===d)return;const u=(d in n?n[d]:void 0)||t.originalParams,g=Me(t,i),v=Me(t,u),m=t.params.grabCursor,w=u.grabCursor,T=i.enabled;g&&!v?(r.classList.remove(`${i.containerModifierClass}grid`,`${i.containerModifierClass}grid-column`),t.emitContainerClasses()):!g&&v&&(r.classList.add(`${i.containerModifierClass}grid`),(u.grid.fill&&u.grid.fill==="column"||!u.grid.fill&&i.grid.fill==="column")&&r.classList.add(`${i.containerModifierClass}grid-column`),t.emitContainerClasses()),m&&!w?t.unsetGrabCursor():!m&&w&&t.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(y=>{if(typeof u[y]>"u")return;const M=i[y]&&i[y].enabled,I=u[y]&&u[y].enabled;M&&!I&&t[y].disable(),!M&&I&&t[y].enable()});const h=u.direction&&u.direction!==i.direction,f=i.loop&&(u.slidesPerView!==i.slidesPerView||h),p=i.loop;h&&s&&t.changeDirection(),k(t.params,u);const b=t.params.enabled,x=t.params.loop;Object.assign(t,{allowTouchMove:t.params.allowTouchMove,allowSlideNext:t.params.allowSlideNext,allowSlidePrev:t.params.allowSlidePrev}),T&&!b?t.disable():!T&&b&&t.enable(),t.currentBreakpoint=d,t.emit("_beforeBreakpoint",u),s&&(f?(t.loopDestroy(),t.loopCreate(e),t.updateSlides()):!p&&x?(t.loopCreate(e),t.updateSlides()):p&&!x&&t.loopDestroy()),t.emit("breakpoint",u)}function ps(t,e,s){if(e===void 0&&(e="window"),!t||e==="container"&&!s)return;let i=!1;const r=V(),n=e==="window"?r.innerHeight:s.clientHeight,o=Object.keys(t).map(l=>{if(typeof l=="string"&&l.indexOf("@")===0){const a=parseFloat(l.substr(1));return{value:n*a,point:l}}return{value:l,point:l}});o.sort((l,a)=>parseInt(l.value,10)-parseInt(a.value,10));for(let l=0;l<o.length;l+=1){const{point:a,value:d}=o[l];e==="window"?r.matchMedia(`(min-width: ${d}px)`).matches&&(i=a):d<=s.clientWidth&&(i=a)}return i||"max"}var ms={setBreakpoint:fs,getBreakpoint:ps};function hs(t,e){const s=[];return t.forEach(i=>{typeof i=="object"?Object.keys(i).forEach(r=>{i[r]&&s.push(e+r)}):typeof i=="string"&&s.push(e+i)}),s}function gs(){const t=this,{classNames:e,params:s,rtl:i,el:r,device:n}=t,o=hs(["initialized",s.direction,{"free-mode":t.params.freeMode&&s.freeMode.enabled},{autoheight:s.autoHeight},{rtl:i},{grid:s.grid&&s.grid.rows>1},{"grid-column":s.grid&&s.grid.rows>1&&s.grid.fill==="column"},{android:n.android},{ios:n.ios},{"css-mode":s.cssMode},{centered:s.cssMode&&s.centeredSlides},{"watch-progress":s.watchSlidesProgress}],s.containerModifierClass);e.push(...o),r.classList.add(...e),t.emitContainerClasses()}function vs(){const t=this,{el:e,classNames:s}=t;!e||typeof e=="string"||(e.classList.remove(...s),t.emitContainerClasses())}var ws={addClasses:gs,removeClasses:vs};function bs(){const t=this,{isLocked:e,params:s}=t,{slidesOffsetBefore:i}=s;if(i){const r=t.slides.length-1,n=t.slidesGrid[r]+t.slidesSizesGrid[r]+i*2;t.isLocked=t.size>n}else t.isLocked=t.snapGrid.length===1;s.allowSlideNext===!0&&(t.allowSlideNext=!t.isLocked),s.allowSlidePrev===!0&&(t.allowSlidePrev=!t.isLocked),e&&e!==t.isLocked&&(t.isEnd=!1),e!==t.isLocked&&t.emit(t.isLocked?"lock":"unlock")}var Ss={checkOverflow:bs},ve={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function xs(t,e){return function(i){i===void 0&&(i={});const r=Object.keys(i)[0],n=i[r];if(typeof n!="object"||n===null){k(e,i);return}if(t[r]===!0&&(t[r]={enabled:!0}),r==="navigation"&&t[r]&&t[r].enabled&&!t[r].prevEl&&!t[r].nextEl&&(t[r].auto=!0),["pagination","scrollbar"].indexOf(r)>=0&&t[r]&&t[r].enabled&&!t[r].el&&(t[r].auto=!0),!(r in t&&"enabled"in n)){k(e,i);return}typeof t[r]=="object"&&!("enabled"in t[r])&&(t[r].enabled=!0),t[r]||(t[r]={enabled:!1}),k(e,i)}}const fe={eventsEmitter:wt,update:Lt,translate:jt,transition:Gt,slide:Yt,loop:Qt,grabCursor:es,events:us,breakpoints:ms,checkOverflow:Ss,classes:ws},pe={};let xe=class H{constructor(){let e,s;for(var i=arguments.length,r=new Array(i),n=0;n<i;n++)r[n]=arguments[n];r.length===1&&r[0].constructor&&Object.prototype.toString.call(r[0]).slice(8,-1)==="Object"?s=r[0]:[e,s]=r,s||(s={}),s=k({},s),e&&!s.el&&(s.el=e);const o=W();if(s.el&&typeof s.el=="string"&&o.querySelectorAll(s.el).length>1){const c=[];return o.querySelectorAll(s.el).forEach(u=>{const g=k({},s,{el:u});c.push(new H(g))}),c}const l=this;l.__swiper__=!0,l.support=_e(),l.device=je({userAgent:s.userAgent}),l.browser=$e(),l.eventsListeners={},l.eventsAnyListeners=[],l.modules=[...l.__modules__],s.modules&&Array.isArray(s.modules)&&l.modules.push(...s.modules);const a={};l.modules.forEach(c=>{c({params:s,swiper:l,extendParams:xs(s,a),on:l.on.bind(l),once:l.once.bind(l),off:l.off.bind(l),emit:l.emit.bind(l)})});const d=k({},ve,a);return l.params=k({},d,pe,s),l.originalParams=k({},l.params),l.passedParams=k({},s),l.params&&l.params.on&&Object.keys(l.params.on).forEach(c=>{l.on(c,l.params.on[c])}),l.params&&l.params.onAny&&l.onAny(l.params.onAny),Object.assign(l,{enabled:l.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal(){return l.params.direction==="horizontal"},isVertical(){return l.params.direction==="vertical"},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:l.params.allowSlideNext,allowSlidePrev:l.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:l.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:l.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),l.emit("_swiper"),l.params.init&&l.init(),l}getDirectionLabel(e){return this.isHorizontal()?e:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[e]}getSlideIndex(e){const{slidesEl:s,params:i}=this,r=R(s,`.${i.slideClass}, swiper-slide`),n=ie(r[0]);return ie(e)-n}getSlideIndexByData(e){return this.getSlideIndex(this.slides.find(s=>s.getAttribute("data-swiper-slide-index")*1===e))}recalcSlides(){const e=this,{slidesEl:s,params:i}=e;e.slides=R(s,`.${i.slideClass}, swiper-slide`)}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){const e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,s){const i=this;e=Math.min(Math.max(e,0),1);const r=i.minTranslate(),o=(i.maxTranslate()-r)*e+r;i.translateTo(o,typeof s>"u"?0:s),i.updateActiveIndex(),i.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const s=e.el.className.split(" ").filter(i=>i.indexOf("swiper")===0||i.indexOf(e.params.containerModifierClass)===0);e.emit("_containerClasses",s.join(" "))}getSlideClasses(e){const s=this;return s.destroyed?"":e.className.split(" ").filter(i=>i.indexOf("swiper-slide")===0||i.indexOf(s.params.slideClass)===0).join(" ")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const s=[];e.slides.forEach(i=>{const r=e.getSlideClasses(i);s.push({slideEl:i,classNames:r}),e.emit("_slideClass",i,r)}),e.emit("_slideClasses",s)}slidesPerViewDynamic(e,s){e===void 0&&(e="current"),s===void 0&&(s=!1);const i=this,{params:r,slides:n,slidesGrid:o,slidesSizesGrid:l,size:a,activeIndex:d}=i;let c=1;if(typeof r.slidesPerView=="number")return r.slidesPerView;if(r.centeredSlides){let u=n[d]?Math.ceil(n[d].swiperSlideSize):0,g;for(let v=d+1;v<n.length;v+=1)n[v]&&!g&&(u+=Math.ceil(n[v].swiperSlideSize),c+=1,u>a&&(g=!0));for(let v=d-1;v>=0;v-=1)n[v]&&!g&&(u+=n[v].swiperSlideSize,c+=1,u>a&&(g=!0))}else if(e==="current")for(let u=d+1;u<n.length;u+=1)(s?o[u]+l[u]-o[d]<a:o[u]-o[d]<a)&&(c+=1);else for(let u=d-1;u>=0;u-=1)o[d]-o[u]<a&&(c+=1);return c}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:s,params:i}=e;i.breakpoints&&e.setBreakpoint(),[...e.el.querySelectorAll('[loading="lazy"]')].forEach(o=>{o.complete&&J(e,o)}),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses();function r(){const o=e.rtlTranslate?e.translate*-1:e.translate,l=Math.min(Math.max(o,e.maxTranslate()),e.minTranslate());e.setTranslate(l),e.updateActiveIndex(),e.updateSlidesClasses()}let n;if(i.freeMode&&i.freeMode.enabled&&!i.cssMode)r(),i.autoHeight&&e.updateAutoHeight();else{if((i.slidesPerView==="auto"||i.slidesPerView>1)&&e.isEnd&&!i.centeredSlides){const o=e.virtual&&i.virtual.enabled?e.virtual.slides:e.slides;n=e.slideTo(o.length-1,0,!1,!0)}else n=e.slideTo(e.activeIndex,0,!1,!0);n||r()}i.watchOverflow&&s!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,s){s===void 0&&(s=!0);const i=this,r=i.params.direction;return e||(e=r==="horizontal"?"vertical":"horizontal"),e===r||e!=="horizontal"&&e!=="vertical"||(i.el.classList.remove(`${i.params.containerModifierClass}${r}`),i.el.classList.add(`${i.params.containerModifierClass}${e}`),i.emitContainerClasses(),i.params.direction=e,i.slides.forEach(n=>{e==="vertical"?n.style.width="":n.style.height=""}),i.emit("changeDirection"),s&&i.update()),i}changeLanguageDirection(e){const s=this;s.rtl&&e==="rtl"||!s.rtl&&e==="ltr"||(s.rtl=e==="rtl",s.rtlTranslate=s.params.direction==="horizontal"&&s.rtl,s.rtl?(s.el.classList.add(`${s.params.containerModifierClass}rtl`),s.el.dir="rtl"):(s.el.classList.remove(`${s.params.containerModifierClass}rtl`),s.el.dir="ltr"),s.update())}mount(e){const s=this;if(s.mounted)return!0;let i=e||s.params.el;if(typeof i=="string"&&(i=document.querySelector(i)),!i)return!1;i.swiper=s,i.parentNode&&i.parentNode.host&&i.parentNode.host.nodeName===s.params.swiperElementNodeName.toUpperCase()&&(s.isElement=!0);const r=()=>`.${(s.params.wrapperClass||"").trim().split(" ").join(".")}`;let o=i&&i.shadowRoot&&i.shadowRoot.querySelector?i.shadowRoot.querySelector(r()):R(i,r())[0];return!o&&s.params.createElements&&(o=se("div",s.params.wrapperClass),i.append(o),R(i,`.${s.params.slideClass}`).forEach(l=>{o.append(l)})),Object.assign(s,{el:i,wrapperEl:o,slidesEl:s.isElement&&!i.parentNode.host.slideSlots?i.parentNode.host:o,hostEl:s.isElement?i.parentNode.host:i,mounted:!0,rtl:i.dir.toLowerCase()==="rtl"||q(i,"direction")==="rtl",rtlTranslate:s.params.direction==="horizontal"&&(i.dir.toLowerCase()==="rtl"||q(i,"direction")==="rtl"),wrongRTL:q(o,"display")==="-webkit-box"}),!0}init(e){const s=this;if(s.initialized||s.mount(e)===!1)return s;s.emit("beforeInit"),s.params.breakpoints&&s.setBreakpoint(),s.addClasses(),s.updateSize(),s.updateSlides(),s.params.watchOverflow&&s.checkOverflow(),s.params.grabCursor&&s.enabled&&s.setGrabCursor(),s.params.loop&&s.virtual&&s.params.virtual.enabled?s.slideTo(s.params.initialSlide+s.virtual.slidesBefore,0,s.params.runCallbacksOnInit,!1,!0):s.slideTo(s.params.initialSlide,0,s.params.runCallbacksOnInit,!1,!0),s.params.loop&&s.loopCreate(void 0,!0),s.attachEvents();const r=[...s.el.querySelectorAll('[loading="lazy"]')];return s.isElement&&r.push(...s.hostEl.querySelectorAll('[loading="lazy"]')),r.forEach(n=>{n.complete?J(s,n):n.addEventListener("load",o=>{J(s,o.target)})}),ge(s),s.initialized=!0,ge(s),s.emit("init"),s.emit("afterInit"),s}destroy(e,s){e===void 0&&(e=!0),s===void 0&&(s=!0);const i=this,{params:r,el:n,wrapperEl:o,slides:l}=i;return typeof i.params>"u"||i.destroyed||(i.emit("beforeDestroy"),i.initialized=!1,i.detachEvents(),r.loop&&i.loopDestroy(),s&&(i.removeClasses(),n&&typeof n!="string"&&n.removeAttribute("style"),o&&o.removeAttribute("style"),l&&l.length&&l.forEach(a=>{a.classList.remove(r.slideVisibleClass,r.slideFullyVisibleClass,r.slideActiveClass,r.slideNextClass,r.slidePrevClass),a.removeAttribute("style"),a.removeAttribute("data-swiper-slide-index")})),i.emit("destroy"),Object.keys(i.eventsListeners).forEach(a=>{i.off(a)}),e!==!1&&(i.el&&typeof i.el!="string"&&(i.el.swiper=null),rt(i)),i.destroyed=!0),null}static extendDefaults(e){k(pe,e)}static get extendedDefaults(){return pe}static get defaults(){return ve}static installModule(e){H.prototype.__modules__||(H.prototype.__modules__=[]);const s=H.prototype.__modules__;typeof e=="function"&&s.indexOf(e)<0&&s.push(e)}static use(e){return Array.isArray(e)?(e.forEach(s=>H.installModule(s)),H):(H.installModule(e),H)}};Object.keys(fe).forEach(t=>{Object.keys(fe[t]).forEach(e=>{xe.prototype[e]=fe[t][e]})});xe.use([gt,vt]);const Ge=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","swiperElementNodeName","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopAdditionalSlides","loopAddBlankSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideFullyVisibleClass","slideNextClass","slidePrevClass","slideBlankClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function Y(t){return typeof t=="object"&&t!==null&&t.constructor&&Object.prototype.toString.call(t).slice(8,-1)==="Object"&&!t.__swiper__}function X(t,e){const s=["__proto__","constructor","prototype"];Object.keys(e).filter(i=>s.indexOf(i)<0).forEach(i=>{typeof t[i]>"u"?t[i]=e[i]:Y(e[i])&&Y(t[i])&&Object.keys(e[i]).length>0?e[i].__swiper__?t[i]=e[i]:X(t[i],e[i]):t[i]=e[i]})}function Ve(t){return t===void 0&&(t={}),t.navigation&&typeof t.navigation.nextEl>"u"&&typeof t.navigation.prevEl>"u"}function ke(t){return t===void 0&&(t={}),t.pagination&&typeof t.pagination.el>"u"}function Fe(t){return t===void 0&&(t={}),t.scrollbar&&typeof t.scrollbar.el>"u"}function Re(t){t===void 0&&(t="");const e=t.split(" ").map(i=>i.trim()).filter(i=>!!i),s=[];return e.forEach(i=>{s.indexOf(i)<0&&s.push(i)}),s.join(" ")}function Ts(t){return t===void 0&&(t=""),t?t.includes("swiper-wrapper")?t:`swiper-wrapper ${t}`:"swiper-wrapper"}function ys(t){let{swiper:e,slides:s,passedParams:i,changedParams:r,nextEl:n,prevEl:o,scrollbarEl:l,paginationEl:a}=t;const d=r.filter(E=>E!=="children"&&E!=="direction"&&E!=="wrapperClass"),{params:c,pagination:u,navigation:g,scrollbar:v,virtual:m,thumbs:w}=e;let T,h,f,p,b,x,y,M;r.includes("thumbs")&&i.thumbs&&i.thumbs.swiper&&!i.thumbs.swiper.destroyed&&c.thumbs&&(!c.thumbs.swiper||c.thumbs.swiper.destroyed)&&(T=!0),r.includes("controller")&&i.controller&&i.controller.control&&c.controller&&!c.controller.control&&(h=!0),r.includes("pagination")&&i.pagination&&(i.pagination.el||a)&&(c.pagination||c.pagination===!1)&&u&&!u.el&&(f=!0),r.includes("scrollbar")&&i.scrollbar&&(i.scrollbar.el||l)&&(c.scrollbar||c.scrollbar===!1)&&v&&!v.el&&(p=!0),r.includes("navigation")&&i.navigation&&(i.navigation.prevEl||o)&&(i.navigation.nextEl||n)&&(c.navigation||c.navigation===!1)&&g&&!g.prevEl&&!g.nextEl&&(b=!0);const I=E=>{e[E]&&(e[E].destroy(),E==="navigation"?(e.isElement&&(e[E].prevEl.remove(),e[E].nextEl.remove()),c[E].prevEl=void 0,c[E].nextEl=void 0,e[E].prevEl=void 0,e[E].nextEl=void 0):(e.isElement&&e[E].el.remove(),c[E].el=void 0,e[E].el=void 0))};r.includes("loop")&&e.isElement&&(c.loop&&!i.loop?x=!0:!c.loop&&i.loop?y=!0:M=!0),d.forEach(E=>{if(Y(c[E])&&Y(i[E]))Object.assign(c[E],i[E]),(E==="navigation"||E==="pagination"||E==="scrollbar")&&"enabled"in i[E]&&!i[E].enabled&&I(E);else{const C=i[E];(C===!0||C===!1)&&(E==="navigation"||E==="pagination"||E==="scrollbar")?C===!1&&I(E):c[E]=i[E]}}),d.includes("controller")&&!h&&e.controller&&e.controller.control&&c.controller&&c.controller.control&&(e.controller.control=c.controller.control),r.includes("children")&&s&&m&&c.virtual.enabled?(m.slides=s,m.update(!0)):r.includes("virtual")&&m&&c.virtual.enabled&&(s&&(m.slides=s),m.update(!0)),r.includes("children")&&s&&c.loop&&(M=!0),T&&w.init()&&w.update(!0),h&&(e.controller.control=c.controller.control),f&&(e.isElement&&(!a||typeof a=="string")&&(a=document.createElement("div"),a.classList.add("swiper-pagination"),a.part.add("pagination"),e.el.appendChild(a)),a&&(c.pagination.el=a),u.init(),u.render(),u.update()),p&&(e.isElement&&(!l||typeof l=="string")&&(l=document.createElement("div"),l.classList.add("swiper-scrollbar"),l.part.add("scrollbar"),e.el.appendChild(l)),l&&(c.scrollbar.el=l),v.init(),v.updateSize(),v.setTranslate()),b&&(e.isElement&&((!n||typeof n=="string")&&(n=document.createElement("div"),n.classList.add("swiper-button-next"),ne(n,e.hostEl.constructor.nextButtonSvg),n.part.add("button-next"),e.el.appendChild(n)),(!o||typeof o=="string")&&(o=document.createElement("div"),o.classList.add("swiper-button-prev"),ne(o,e.hostEl.constructor.prevButtonSvg),o.part.add("button-prev"),e.el.appendChild(o))),n&&(c.navigation.nextEl=n),o&&(c.navigation.prevEl=o),g.init(),g.update()),r.includes("allowSlideNext")&&(e.allowSlideNext=i.allowSlideNext),r.includes("allowSlidePrev")&&(e.allowSlidePrev=i.allowSlidePrev),r.includes("direction")&&e.changeDirection(i.direction,!1),(x||M)&&e.loopDestroy(),(y||M)&&e.loopCreate(),e.update()}function Es(t,e){t===void 0&&(t={}),e===void 0&&(e=!0);const s={on:{}},i={},r={};X(s,ve),s._emitClasses=!0,s.init=!1;const n={},o=Ge.map(a=>a.replace(/_/,"")),l=Object.assign({},t);return Object.keys(l).forEach(a=>{typeof t[a]>"u"||(o.indexOf(a)>=0?Y(t[a])?(s[a]={},r[a]={},X(s[a],t[a]),X(r[a],t[a])):(s[a]=t[a],r[a]=t[a]):a.search(/on[A-Z]/)===0&&typeof t[a]=="function"?e?i[`${a[2].toLowerCase()}${a.substr(3)}`]=t[a]:s.on[`${a[2].toLowerCase()}${a.substr(3)}`]=t[a]:n[a]=t[a])}),["navigation","pagination","scrollbar"].forEach(a=>{s[a]===!0&&(s[a]={}),s[a]===!1&&delete s[a]}),{params:s,passedParams:r,rest:n,events:i}}function Cs(t,e){let{el:s,nextEl:i,prevEl:r,paginationEl:n,scrollbarEl:o,swiper:l}=t;Ve(e)&&i&&r&&(l.params.navigation.nextEl=i,l.originalParams.navigation.nextEl=i,l.params.navigation.prevEl=r,l.originalParams.navigation.prevEl=r),ke(e)&&n&&(l.params.pagination.el=n,l.originalParams.pagination.el=n),Fe(e)&&o&&(l.params.scrollbar.el=o,l.originalParams.scrollbar.el=o),l.init(s)}function Ps(t,e,s,i,r){const n=[];if(!e)return n;const o=a=>{n.indexOf(a)<0&&n.push(a)};if(s&&i){const a=i.map(r),d=s.map(r);a.join("")!==d.join("")&&o("children"),i.length!==s.length&&o("children")}return Ge.filter(a=>a[0]==="_").map(a=>a.replace(/_/,"")).forEach(a=>{if(a in t&&a in e)if(Y(t[a])&&Y(e[a])){const d=Object.keys(t[a]),c=Object.keys(e[a]);d.length!==c.length?o(a):(d.forEach(u=>{t[a][u]!==e[a][u]&&o(a)}),c.forEach(u=>{t[a][u]!==e[a][u]&&o(a)}))}else t[a]!==e[a]&&o(a)}),n}const Ms=t=>{!t||t.destroyed||!t.params.virtual||t.params.virtual&&!t.params.virtual.enabled||(t.updateSlides(),t.updateProgress(),t.updateSlidesClasses(),t.emit("_virtualUpdated"),t.parallax&&t.params.parallax&&t.params.parallax.enabled&&t.parallax.setTranslate())};function re(){return re=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var s=arguments[e];for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(t[i]=s[i])}return t},re.apply(this,arguments)}function He(t){return t.type&&t.type.displayName&&t.type.displayName.includes("SwiperSlide")}function qe(t){const e=[];return $.Children.toArray(t).forEach(s=>{He(s)?e.push(s):s.props&&s.props.children&&qe(s.props.children).forEach(i=>e.push(i))}),e}function Is(t){const e=[],s={"container-start":[],"container-end":[],"wrapper-start":[],"wrapper-end":[]};return $.Children.toArray(t).forEach(i=>{if(He(i))e.push(i);else if(i.props&&i.props.slot&&s[i.props.slot])s[i.props.slot].push(i);else if(i.props&&i.props.children){const r=qe(i.props.children);r.length>0?r.forEach(n=>e.push(n)):s["container-end"].push(i)}else s["container-end"].push(i)}),{slides:e,slots:s}}function Ls(t,e,s){if(!s)return null;const i=c=>{let u=c;return c<0?u=e.length+c:u>=e.length&&(u=u-e.length),u},r=t.isHorizontal()?{[t.rtlTranslate?"right":"left"]:`${s.offset}px`}:{top:`${s.offset}px`},{from:n,to:o}=s,l=t.params.loop?-e.length:0,a=t.params.loop?e.length*2:e.length,d=[];for(let c=l;c<a;c+=1)c>=n&&c<=o&&d.push(e[i(c)]);return d.map((c,u)=>$.cloneElement(c,{swiper:t,style:r,key:c.props.virtualIndex||c.key||`slide-${u}`}))}function Q(t,e){return typeof window>"u"?j.useEffect(t,e):j.useLayoutEffect(t,e)}const Ie=j.createContext(null),Os=j.createContext(null),we=j.forwardRef(function(t,e){let{className:s,tag:i="div",wrapperTag:r="div",children:n,onSwiper:o,...l}=t===void 0?{}:t,a=!1;const[d,c]=j.useState("swiper"),[u,g]=j.useState(null),[v,m]=j.useState(!1),w=j.useRef(!1),T=j.useRef(null),h=j.useRef(null),f=j.useRef(null),p=j.useRef(null),b=j.useRef(null),x=j.useRef(null),y=j.useRef(null),M=j.useRef(null),{params:I,passedParams:E,rest:C,events:P}=Es(l),{slides:L,slots:z}=Is(n),N=()=>{m(!v)};Object.assign(I.on,{_containerClasses(_,F){c(F)}});const A=()=>{Object.assign(I.on,P),a=!0;const _={...I};if(delete _.wrapperClass,h.current=new xe(_),h.current.virtual&&h.current.params.virtual.enabled){h.current.virtual.slides=L;const F={cache:!1,slides:L,renderExternal:g,renderExternalUpdate:!1};X(h.current.params.virtual,F),X(h.current.originalParams.virtual,F)}};T.current||A(),h.current&&h.current.on("_beforeBreakpoint",N);const O=()=>{a||!P||!h.current||Object.keys(P).forEach(_=>{h.current.on(_,P[_])})},B=()=>{!P||!h.current||Object.keys(P).forEach(_=>{h.current.off(_,P[_])})};j.useEffect(()=>()=>{h.current&&h.current.off("_beforeBreakpoint",N)}),j.useEffect(()=>{!w.current&&h.current&&(h.current.emitSlidesClasses(),w.current=!0)}),Q(()=>{if(e&&(e.current=T.current),!!T.current)return h.current.destroyed&&A(),Cs({el:T.current,nextEl:b.current,prevEl:x.current,paginationEl:y.current,scrollbarEl:M.current,swiper:h.current},I),o&&!h.current.destroyed&&o(h.current),()=>{h.current&&!h.current.destroyed&&h.current.destroy(!0,!1)}},[]),Q(()=>{O();const _=Ps(E,f.current,L,p.current,F=>F.key);return f.current=E,p.current=L,_.length&&h.current&&!h.current.destroyed&&ys({swiper:h.current,slides:L,passedParams:E,changedParams:_,nextEl:b.current,prevEl:x.current,scrollbarEl:M.current,paginationEl:y.current}),()=>{B()}}),Q(()=>{Ms(h.current)},[u]);function G(){return I.virtual?Ls(h.current,L,u):L.map((_,F)=>$.cloneElement(_,{swiper:h.current,swiperSlideIndex:F}))}return $.createElement(i,re({ref:T,className:Re(`${d}${s?` ${s}`:""}`)},C),$.createElement(Os.Provider,{value:h.current},z["container-start"],$.createElement(r,{className:Ts(I.wrapperClass)},z["wrapper-start"],G(),z["wrapper-end"]),Ve(I)&&$.createElement($.Fragment,null,$.createElement("div",{ref:x,className:"swiper-button-prev"}),$.createElement("div",{ref:b,className:"swiper-button-next"})),Fe(I)&&$.createElement("div",{ref:M,className:"swiper-scrollbar"}),ke(I)&&$.createElement("div",{ref:y,className:"swiper-pagination"}),z["container-end"]))});we.displayName="Swiper";const be=j.forwardRef(function(t,e){let{tag:s="div",children:i,className:r="",swiper:n,zoom:o,lazy:l,virtualIndex:a,swiperSlideIndex:d,...c}=t===void 0?{}:t;const u=j.useRef(null),[g,v]=j.useState("swiper-slide"),[m,w]=j.useState(!1);function T(b,x,y){x===u.current&&v(y)}Q(()=>{if(typeof d<"u"&&(u.current.swiperSlideIndex=d),e&&(e.current=u.current),!(!u.current||!n)){if(n.destroyed){g!=="swiper-slide"&&v("swiper-slide");return}return n.on("_slideClass",T),()=>{n&&n.off("_slideClass",T)}}}),Q(()=>{n&&u.current&&!n.destroyed&&v(n.getSlideClasses(u.current))},[n]);const h={isActive:g.indexOf("swiper-slide-active")>=0,isVisible:g.indexOf("swiper-slide-visible")>=0,isPrev:g.indexOf("swiper-slide-prev")>=0,isNext:g.indexOf("swiper-slide-next")>=0},f=()=>typeof i=="function"?i(h):i,p=()=>{w(!0)};return $.createElement(s,re({ref:u,className:Re(`${g}${r?` ${r}`:""}`),"data-swiper-slide-index":a,onLoad:p},c),o&&$.createElement(Ie.Provider,{value:h},$.createElement("div",{className:"swiper-zoom-container","data-swiper-zoom":typeof o=="number"?o:void 0},f(),l&&!m&&$.createElement("div",{className:"swiper-lazy-preloader"}))),!o&&$.createElement(Ie.Provider,{value:h},f(),l&&!m&&$.createElement("div",{className:"swiper-lazy-preloader"})))});be.displayName="SwiperSlide";function We(t,e,s,i){return t.params.createElements&&Object.keys(i).forEach(r=>{if(!s[r]&&s.auto===!0){let n=R(t.el,`.${i[r]}`)[0];n||(n=se("div",i[r]),n.className=i[r],t.el.append(n)),s[r]=n,e[r]=n}}),s}function Le(t){let{swiper:e,extendParams:s,on:i,emit:r}=t;s({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),e.navigation={nextEl:null,prevEl:null};function n(m){let w;return m&&typeof m=="string"&&e.isElement&&(w=e.el.querySelector(m)||e.hostEl.querySelector(m),w)?w:(m&&(typeof m=="string"&&(w=[...document.querySelectorAll(m)]),e.params.uniqueNavElements&&typeof m=="string"&&w&&w.length>1&&e.el.querySelectorAll(m).length===1?w=e.el.querySelector(m):w&&w.length===1&&(w=w[0])),m&&!w?m:w)}function o(m,w){const T=e.params.navigation;m=D(m),m.forEach(h=>{h&&(h.classList[w?"add":"remove"](...T.disabledClass.split(" ")),h.tagName==="BUTTON"&&(h.disabled=w),e.params.watchOverflow&&e.enabled&&h.classList[e.isLocked?"add":"remove"](T.lockClass))})}function l(){const{nextEl:m,prevEl:w}=e.navigation;if(e.params.loop){o(w,!1),o(m,!1);return}o(w,e.isBeginning&&!e.params.rewind),o(m,e.isEnd&&!e.params.rewind)}function a(m){m.preventDefault(),!(e.isBeginning&&!e.params.loop&&!e.params.rewind)&&(e.slidePrev(),r("navigationPrev"))}function d(m){m.preventDefault(),!(e.isEnd&&!e.params.loop&&!e.params.rewind)&&(e.slideNext(),r("navigationNext"))}function c(){const m=e.params.navigation;if(e.params.navigation=We(e,e.originalParams.navigation,e.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!(m.nextEl||m.prevEl))return;let w=n(m.nextEl),T=n(m.prevEl);Object.assign(e.navigation,{nextEl:w,prevEl:T}),w=D(w),T=D(T);const h=(f,p)=>{f&&f.addEventListener("click",p==="next"?d:a),!e.enabled&&f&&f.classList.add(...m.lockClass.split(" "))};w.forEach(f=>h(f,"next")),T.forEach(f=>h(f,"prev"))}function u(){let{nextEl:m,prevEl:w}=e.navigation;m=D(m),w=D(w);const T=(h,f)=>{h.removeEventListener("click",f==="next"?d:a),h.classList.remove(...e.params.navigation.disabledClass.split(" "))};m.forEach(h=>T(h,"next")),w.forEach(h=>T(h,"prev"))}i("init",()=>{e.params.navigation.enabled===!1?v():(c(),l())}),i("toEdge fromEdge lock unlock",()=>{l()}),i("destroy",()=>{u()}),i("enable disable",()=>{let{nextEl:m,prevEl:w}=e.navigation;if(m=D(m),w=D(w),e.enabled){l();return}[...m,...w].filter(T=>!!T).forEach(T=>T.classList.add(e.params.navigation.lockClass))}),i("click",(m,w)=>{let{nextEl:T,prevEl:h}=e.navigation;T=D(T),h=D(h);const f=w.target;let p=h.includes(f)||T.includes(f);if(e.isElement&&!p){const b=w.path||w.composedPath&&w.composedPath();b&&(p=b.find(x=>T.includes(x)||h.includes(x)))}if(e.params.navigation.hideOnClick&&!p){if(e.pagination&&e.params.pagination&&e.params.pagination.clickable&&(e.pagination.el===f||e.pagination.el.contains(f)))return;let b;T.length?b=T[0].classList.contains(e.params.navigation.hiddenClass):h.length&&(b=h[0].classList.contains(e.params.navigation.hiddenClass)),r(b===!0?"navigationShow":"navigationHide"),[...T,...h].filter(x=>!!x).forEach(x=>x.classList.toggle(e.params.navigation.hiddenClass))}});const g=()=>{e.el.classList.remove(...e.params.navigation.navigationDisabledClass.split(" ")),c(),l()},v=()=>{e.el.classList.add(...e.params.navigation.navigationDisabledClass.split(" ")),u()};Object.assign(e.navigation,{enable:g,disable:v,update:l,init:c,destroy:u})}function U(t){return t===void 0&&(t=""),`.${t.trim().replace(/([\.:!+\/])/g,"\\$1").replace(/ /g,".")}`}function zs(t){let{swiper:e,extendParams:s,on:i,emit:r}=t;const n="swiper-pagination";s({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:f=>f,formatFractionTotal:f=>f,bulletClass:`${n}-bullet`,bulletActiveClass:`${n}-bullet-active`,modifierClass:`${n}-`,currentClass:`${n}-current`,totalClass:`${n}-total`,hiddenClass:`${n}-hidden`,progressbarFillClass:`${n}-progressbar-fill`,progressbarOppositeClass:`${n}-progressbar-opposite`,clickableClass:`${n}-clickable`,lockClass:`${n}-lock`,horizontalClass:`${n}-horizontal`,verticalClass:`${n}-vertical`,paginationDisabledClass:`${n}-disabled`}}),e.pagination={el:null,bullets:[]};let o,l=0;function a(){return!e.params.pagination.el||!e.pagination.el||Array.isArray(e.pagination.el)&&e.pagination.el.length===0}function d(f,p){const{bulletActiveClass:b}=e.params.pagination;f&&(f=f[`${p==="prev"?"previous":"next"}ElementSibling`],f&&(f.classList.add(`${b}-${p}`),f=f[`${p==="prev"?"previous":"next"}ElementSibling`],f&&f.classList.add(`${b}-${p}-${p}`)))}function c(f,p,b){if(f=f%b,p=p%b,p===f+1)return"next";if(p===f-1)return"previous"}function u(f){const p=f.target.closest(U(e.params.pagination.bulletClass));if(!p)return;f.preventDefault();const b=ie(p)*e.params.slidesPerGroup;if(e.params.loop){if(e.realIndex===b)return;const x=c(e.realIndex,b,e.slides.length);x==="next"?e.slideNext():x==="previous"?e.slidePrev():e.slideToLoop(b)}else e.slideTo(b)}function g(){const f=e.rtl,p=e.params.pagination;if(a())return;let b=e.pagination.el;b=D(b);let x,y;const M=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,I=e.params.loop?Math.ceil(M/e.params.slidesPerGroup):e.snapGrid.length;if(e.params.loop?(y=e.previousRealIndex||0,x=e.params.slidesPerGroup>1?Math.floor(e.realIndex/e.params.slidesPerGroup):e.realIndex):typeof e.snapIndex<"u"?(x=e.snapIndex,y=e.previousSnapIndex):(y=e.previousIndex||0,x=e.activeIndex||0),p.type==="bullets"&&e.pagination.bullets&&e.pagination.bullets.length>0){const E=e.pagination.bullets;let C,P,L;if(p.dynamicBullets&&(o=he(E[0],e.isHorizontal()?"width":"height"),b.forEach(z=>{z.style[e.isHorizontal()?"width":"height"]=`${o*(p.dynamicMainBullets+4)}px`}),p.dynamicMainBullets>1&&y!==void 0&&(l+=x-(y||0),l>p.dynamicMainBullets-1?l=p.dynamicMainBullets-1:l<0&&(l=0)),C=Math.max(x-l,0),P=C+(Math.min(E.length,p.dynamicMainBullets)-1),L=(P+C)/2),E.forEach(z=>{const N=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(A=>`${p.bulletActiveClass}${A}`)].map(A=>typeof A=="string"&&A.includes(" ")?A.split(" "):A).flat();z.classList.remove(...N)}),b.length>1)E.forEach(z=>{const N=ie(z);N===x?z.classList.add(...p.bulletActiveClass.split(" ")):e.isElement&&z.setAttribute("part","bullet"),p.dynamicBullets&&(N>=C&&N<=P&&z.classList.add(...`${p.bulletActiveClass}-main`.split(" ")),N===C&&d(z,"prev"),N===P&&d(z,"next"))});else{const z=E[x];if(z&&z.classList.add(...p.bulletActiveClass.split(" ")),e.isElement&&E.forEach((N,A)=>{N.setAttribute("part",A===x?"bullet-active":"bullet")}),p.dynamicBullets){const N=E[C],A=E[P];for(let O=C;O<=P;O+=1)E[O]&&E[O].classList.add(...`${p.bulletActiveClass}-main`.split(" "));d(N,"prev"),d(A,"next")}}if(p.dynamicBullets){const z=Math.min(E.length,p.dynamicMainBullets+4),N=(o*z-o)/2-L*o,A=f?"right":"left";E.forEach(O=>{O.style[e.isHorizontal()?A:"top"]=`${N}px`})}}b.forEach((E,C)=>{if(p.type==="fraction"&&(E.querySelectorAll(U(p.currentClass)).forEach(P=>{P.textContent=p.formatFractionCurrent(x+1)}),E.querySelectorAll(U(p.totalClass)).forEach(P=>{P.textContent=p.formatFractionTotal(I)})),p.type==="progressbar"){let P;p.progressbarOpposite?P=e.isHorizontal()?"vertical":"horizontal":P=e.isHorizontal()?"horizontal":"vertical";const L=(x+1)/I;let z=1,N=1;P==="horizontal"?z=L:N=L,E.querySelectorAll(U(p.progressbarFillClass)).forEach(A=>{A.style.transform=`translate3d(0,0,0) scaleX(${z}) scaleY(${N})`,A.style.transitionDuration=`${e.params.speed}ms`})}p.type==="custom"&&p.renderCustom?(ne(E,p.renderCustom(e,x+1,I)),C===0&&r("paginationRender",E)):(C===0&&r("paginationRender",E),r("paginationUpdate",E)),e.params.watchOverflow&&e.enabled&&E.classList[e.isLocked?"add":"remove"](p.lockClass)})}function v(){const f=e.params.pagination;if(a())return;const p=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.grid&&e.params.grid.rows>1?e.slides.length/Math.ceil(e.params.grid.rows):e.slides.length;let b=e.pagination.el;b=D(b);let x="";if(f.type==="bullets"){let y=e.params.loop?Math.ceil(p/e.params.slidesPerGroup):e.snapGrid.length;e.params.freeMode&&e.params.freeMode.enabled&&y>p&&(y=p);for(let M=0;M<y;M+=1)f.renderBullet?x+=f.renderBullet.call(e,M,f.bulletClass):x+=`<${f.bulletElement} ${e.isElement?'part="bullet"':""} class="${f.bulletClass}"></${f.bulletElement}>`}f.type==="fraction"&&(f.renderFraction?x=f.renderFraction.call(e,f.currentClass,f.totalClass):x=`<span class="${f.currentClass}"></span> / <span class="${f.totalClass}"></span>`),f.type==="progressbar"&&(f.renderProgressbar?x=f.renderProgressbar.call(e,f.progressbarFillClass):x=`<span class="${f.progressbarFillClass}"></span>`),e.pagination.bullets=[],b.forEach(y=>{f.type!=="custom"&&ne(y,x||""),f.type==="bullets"&&e.pagination.bullets.push(...y.querySelectorAll(U(f.bulletClass)))}),f.type!=="custom"&&r("paginationRender",b[0])}function m(){e.params.pagination=We(e,e.originalParams.pagination,e.params.pagination,{el:"swiper-pagination"});const f=e.params.pagination;if(!f.el)return;let p;typeof f.el=="string"&&e.isElement&&(p=e.el.querySelector(f.el)),!p&&typeof f.el=="string"&&(p=[...document.querySelectorAll(f.el)]),p||(p=f.el),!(!p||p.length===0)&&(e.params.uniqueNavElements&&typeof f.el=="string"&&Array.isArray(p)&&p.length>1&&(p=[...e.el.querySelectorAll(f.el)],p.length>1&&(p=p.find(b=>Ae(b,".swiper")[0]===e.el))),Array.isArray(p)&&p.length===1&&(p=p[0]),Object.assign(e.pagination,{el:p}),p=D(p),p.forEach(b=>{f.type==="bullets"&&f.clickable&&b.classList.add(...(f.clickableClass||"").split(" ")),b.classList.add(f.modifierClass+f.type),b.classList.add(e.isHorizontal()?f.horizontalClass:f.verticalClass),f.type==="bullets"&&f.dynamicBullets&&(b.classList.add(`${f.modifierClass}${f.type}-dynamic`),l=0,f.dynamicMainBullets<1&&(f.dynamicMainBullets=1)),f.type==="progressbar"&&f.progressbarOpposite&&b.classList.add(f.progressbarOppositeClass),f.clickable&&b.addEventListener("click",u),e.enabled||b.classList.add(f.lockClass)}))}function w(){const f=e.params.pagination;if(a())return;let p=e.pagination.el;p&&(p=D(p),p.forEach(b=>{b.classList.remove(f.hiddenClass),b.classList.remove(f.modifierClass+f.type),b.classList.remove(e.isHorizontal()?f.horizontalClass:f.verticalClass),f.clickable&&(b.classList.remove(...(f.clickableClass||"").split(" ")),b.removeEventListener("click",u))})),e.pagination.bullets&&e.pagination.bullets.forEach(b=>b.classList.remove(...f.bulletActiveClass.split(" ")))}i("changeDirection",()=>{if(!e.pagination||!e.pagination.el)return;const f=e.params.pagination;let{el:p}=e.pagination;p=D(p),p.forEach(b=>{b.classList.remove(f.horizontalClass,f.verticalClass),b.classList.add(e.isHorizontal()?f.horizontalClass:f.verticalClass)})}),i("init",()=>{e.params.pagination.enabled===!1?h():(m(),v(),g())}),i("activeIndexChange",()=>{typeof e.snapIndex>"u"&&g()}),i("snapIndexChange",()=>{g()}),i("snapGridLengthChange",()=>{v(),g()}),i("destroy",()=>{w()}),i("enable disable",()=>{let{el:f}=e.pagination;f&&(f=D(f),f.forEach(p=>p.classList[e.enabled?"remove":"add"](e.params.pagination.lockClass)))}),i("lock unlock",()=>{g()}),i("click",(f,p)=>{const b=p.target,x=D(e.pagination.el);if(e.params.pagination.el&&e.params.pagination.hideOnClick&&x&&x.length>0&&!b.classList.contains(e.params.pagination.bulletClass)){if(e.navigation&&(e.navigation.nextEl&&b===e.navigation.nextEl||e.navigation.prevEl&&b===e.navigation.prevEl))return;const y=x[0].classList.contains(e.params.pagination.hiddenClass);r(y===!0?"paginationShow":"paginationHide"),x.forEach(M=>M.classList.toggle(e.params.pagination.hiddenClass))}});const T=()=>{e.el.classList.remove(e.params.pagination.paginationDisabledClass);let{el:f}=e.pagination;f&&(f=D(f),f.forEach(p=>p.classList.remove(e.params.pagination.paginationDisabledClass))),m(),v(),g()},h=()=>{e.el.classList.add(e.params.pagination.paginationDisabledClass);let{el:f}=e.pagination;f&&(f=D(f),f.forEach(p=>p.classList.add(e.params.pagination.paginationDisabledClass))),w()};Object.assign(e.pagination,{enable:T,disable:h,render:v,update:g,init:m,destroy:w})}function Oe(t){let{swiper:e,extendParams:s,on:i}=t;s({thumbs:{swiper:null,multipleActiveThumbs:!0,autoScrollOffset:0,slideThumbActiveClass:"swiper-slide-thumb-active",thumbsContainerClass:"swiper-thumbs"}});let r=!1,n=!1;e.thumbs={swiper:null};function o(){const d=e.thumbs.swiper;if(!d||d.destroyed)return;const c=d.clickedIndex,u=d.clickedSlide;if(u&&u.classList.contains(e.params.thumbs.slideThumbActiveClass)||typeof c>"u"||c===null)return;let g;d.params.loop?g=parseInt(d.clickedSlide.getAttribute("data-swiper-slide-index"),10):g=c,e.params.loop?e.slideToLoop(g):e.slideTo(g)}function l(){const{thumbs:d}=e.params;if(r)return!1;r=!0;const c=e.constructor;if(d.swiper instanceof c){if(d.swiper.destroyed)return r=!1,!1;e.thumbs.swiper=d.swiper,Object.assign(e.thumbs.swiper.originalParams,{watchSlidesProgress:!0,slideToClickedSlide:!1}),Object.assign(e.thumbs.swiper.params,{watchSlidesProgress:!0,slideToClickedSlide:!1}),e.thumbs.swiper.update()}else if(K(d.swiper)){const u=Object.assign({},d.swiper);Object.assign(u,{watchSlidesProgress:!0,slideToClickedSlide:!1}),e.thumbs.swiper=new c(u),n=!0}return e.thumbs.swiper.el.classList.add(e.params.thumbs.thumbsContainerClass),e.thumbs.swiper.on("tap",o),!0}function a(d){const c=e.thumbs.swiper;if(!c||c.destroyed)return;const u=c.params.slidesPerView==="auto"?c.slidesPerViewDynamic():c.params.slidesPerView;let g=1;const v=e.params.thumbs.slideThumbActiveClass;if(e.params.slidesPerView>1&&!e.params.centeredSlides&&(g=e.params.slidesPerView),e.params.thumbs.multipleActiveThumbs||(g=1),g=Math.floor(g),c.slides.forEach(T=>T.classList.remove(v)),c.params.loop||c.params.virtual&&c.params.virtual.enabled)for(let T=0;T<g;T+=1)R(c.slidesEl,`[data-swiper-slide-index="${e.realIndex+T}"]`).forEach(h=>{h.classList.add(v)});else for(let T=0;T<g;T+=1)c.slides[e.realIndex+T]&&c.slides[e.realIndex+T].classList.add(v);const m=e.params.thumbs.autoScrollOffset,w=m&&!c.params.loop;if(e.realIndex!==c.realIndex||w){const T=c.activeIndex;let h,f;if(c.params.loop){const p=c.slides.find(b=>b.getAttribute("data-swiper-slide-index")===`${e.realIndex}`);h=c.slides.indexOf(p),f=e.activeIndex>e.previousIndex?"next":"prev"}else h=e.realIndex,f=h>e.previousIndex?"next":"prev";w&&(h+=f==="next"?m:-1*m),c.visibleSlidesIndexes&&c.visibleSlidesIndexes.indexOf(h)<0&&(c.params.centeredSlides?h>T?h=h-Math.floor(u/2)+1:h=h+Math.floor(u/2)-1:h>T&&c.params.slidesPerGroup,c.slideTo(h,d?0:void 0))}}i("beforeInit",()=>{const{thumbs:d}=e.params;if(!(!d||!d.swiper))if(typeof d.swiper=="string"||d.swiper instanceof HTMLElement){const c=W(),u=()=>{const v=typeof d.swiper=="string"?c.querySelector(d.swiper):d.swiper;if(v&&v.swiper)d.swiper=v.swiper,l(),a(!0);else if(v){const m=`${e.params.eventsPrefix}init`,w=T=>{d.swiper=T.detail[0],v.removeEventListener(m,w),l(),a(!0),d.swiper.update(),e.update()};v.addEventListener(m,w)}return v},g=()=>{if(e.destroyed)return;u()||requestAnimationFrame(g)};requestAnimationFrame(g)}else l(),a(!0)}),i("slideChange update resize observerUpdate",()=>{a()}),i("setTransition",(d,c)=>{const u=e.thumbs.swiper;!u||u.destroyed||u.setTransition(c)}),i("beforeDestroy",()=>{const d=e.thumbs.swiper;!d||d.destroyed||n&&d.destroy()}),Object.assign(e.thumbs,{init:l,update:a})}const Ns=({images:t=[],alt:e="Property Image",className:s=""})=>{const[i,r]=j.useState(null),n=t&&t.length>0?t:[{url:"/assets/img/property-placeholder.png",id:"placeholder"}];return n.length===1?S.jsx("div",{className:`image-slider single-image ${s}`,children:S.jsx("div",{className:"main-image",children:S.jsx("img",{src:n[0].url||n[0].image_url||"/assets/img/image-1.png",alt:e,onError:o=>{o.target.src="/assets/img/image-1.png"}})})}):S.jsxs("div",{className:`image-slider ${s}`,children:[S.jsx(we,{spaceBetween:10,navigation:!0,thumbs:{swiper:i&&!i.destroyed?i:null},modules:[Le,Oe,zs],className:"main-swiper",pagination:{type:"fraction"},children:n.map((o,l)=>S.jsx(be,{children:S.jsx("div",{className:"main-image",children:S.jsx("img",{src:o.url||o.image_url||"/assets/img/placeholder.jpg",alt:`${e} ${l+1}`,onError:a=>{a.target.src="/assets/img/placeholder.jpg"}})})},o.id||l))}),n.length>1&&S.jsx(we,{onSwiper:r,spaceBetween:10,slidesPerView:4,freeMode:!0,watchSlidesProgress:!0,modules:[Le,Oe],className:"thumbs-swiper",breakpoints:{320:{slidesPerView:3,spaceBetween:8},480:{slidesPerView:4,spaceBetween:10},768:{slidesPerView:5,spaceBetween:10},1024:{slidesPerView:6,spaceBetween:12}},children:n.map((o,l)=>S.jsx(be,{children:S.jsx("div",{className:"thumbnail",children:S.jsx("img",{src:o.url||o.image_url||"/assets/img/image-1.png",alt:`${e} thumbnail ${l+1}`,onError:a=>{a.target.src="/assets/img/image-1.png"}})})},`thumb-${o.id||l}`))})]})},ks=()=>{var w,T,h,f,p,b,x;const{id:t}=Ue(),e=Ke(),s=Qe(),i=Ze(),{data:r,isLoading:n,dataUpdatedAt:o,refetch:l}=tt("getProperty",{slug:t,staleTime:0,gcTime:0,refetchOnMount:!0,refetchOnWindowFocus:!0}),a=(r==null?void 0:r.data)||r;console.log("property",a);const d=et({mutationFn:async y=>await Je.request("addProperty",{slug:`${t}/favorite`,data:{value:y}}),onSuccess:()=>{s.invalidateQueries({queryKey:["properties"],exact:!1}),s.invalidateQueries({queryKey:["getProperty"],exact:!1})}}),c=y=>{if(y.preventDefault(),y.stopPropagation(),d.isPending)return;const M=!a.is_favorite;d.mutate(M)};$.useEffect(()=>{l()},[t,e.pathname,l]),$.useEffect(()=>{const y=M=>{M.key===`property_updated_${t}`&&l()};return window.addEventListener("storage",y),()=>window.removeEventListener("storage",y)},[t,l]);const u=y=>{if(!y)return y;const M=parseFloat(y);return isNaN(M)?y:Math.floor(M).toString()},g=y=>{if(!y)return"$0";const M=parseFloat(y);return isNaN(M)?y:`${Math.floor(M).toLocaleString()}`},v=y=>{y.stopPropagation(),i("/inbox",{state:{directMessage:!0,targetUser:a==null?void 0:a.user}})};if(n)return S.jsx(Te,{children:S.jsx("div",{className:"container-fluid",children:S.jsx("div",{className:"row mt-5",children:S.jsx("div",{className:"col-12",children:S.jsx(st,{active:!0,paragraph:{rows:10}})})})})});const m={leftItems:[{label:"Basement",value:a.basement?"Yes":"No",icon:"/assets/img/base-1.png"},{label:"Garage",value:u(a.garage)||"No",icon:"/assets/img/base-2.png"},{label:"Lot Size",value:a.lot_size?`${u(a.lot_size)} ${a.lot_type||"sqft"}`:"No",icon:"/assets/img/base-3.png"},{label:"Property Type",value:((w=a.type)==null?void 0:w.name)||"No",icon:"/assets/img/base-4.png"},{label:"Zip Code",value:a.zip||"No",icon:"/assets/img/base-5.png"},{label:"HOA",value:a.hoa?a.hoa_price?`${u(a.hoa_price)}/${a.hoa_type||"month"}`:"Yes":"No",icon:"/assets/img/base-6.png"},{label:"Buy-Side Compensation",value:a.buy_side_compensation?`${a.buy_side_compensation} ${a.buy_side_compensation_type||""}`:"No",icon:"/assets/img/buy-side.png"}],rightItems:[{label:"State",value:a.state||"No",icon:"/assets/img/base-7.png"},{label:"City",value:a.city||"No",icon:"/assets/img/base-7.png"},{label:"Property Size",value:a.size?`${u(a.size)} ${a.size_type||"sqft"}`:"No",icon:"/assets/img/base-3.png"},{label:"Home Style",value:((T=a.home_style)==null?void 0:T.name)||"No",icon:"/assets/img/base-8.png"},{label:"Year Built",value:u(a.year_built)||"No",icon:"/assets/img/base-9.png"},{label:"MLS ID",value:a.mls_id||"No",icon:"/assets/img/base-10.png"},{label:"Phone",value:a.mobile_no,icon:"/assets/img/phone-rounded.png"}]};return S.jsx(Te,{children:S.jsxs("div",{className:"container-fluid",children:[S.jsx("div",{className:"row mt-5",children:S.jsxs("div",{className:"col-12",children:[S.jsx("div",{className:"text-end fav-icon",children:S.jsx("div",{onClick:c,style:{cursor:d.isPending?"not-allowed":"pointer",opacity:d.isPending?.7:1,transition:"opacity 0.2s ease",display:"inline-block"},children:S.jsx("img",{src:a.is_favorite?"/assets/img/heart-active.png":"/assets/img/heart-icon.png",alt:"favorite"})})}),S.jsxs("div",{className:"d-flex align-items-center justify-content-between",children:[S.jsxs("div",{children:[S.jsx("p",{className:"font-32 font-600",children:a.name||`${u(a.bed)} Bed ${u(a.bath)} Bath ${((h=a.type)==null?void 0:h.name)||"Property"}`}),S.jsx("p",{children:a.address?`${a.address}, ${a.city}, ${a.state}`:`${a.city||""}, ${a.state||""}`})]}),S.jsxs("div",{className:"text-end",children:[S.jsxs("p",{className:"font-32 font-600",children:["$",g(a.price)]}),S.jsx("p",{className:"font-18",children:a.size&&a.price?`$${u(parseFloat(a.price)/parseFloat(a.size))} /${a.size_type}`:""})]})]})]})}),S.jsx("div",{className:"row mt-3",children:S.jsx("div",{className:"col-12 col-sm-7 col-md-7 col-lg-8 col-xl-12",children:S.jsx(Ns,{images:(a==null?void 0:a.images)||[],alt:(a==null?void 0:a.name)||"Property",className:"property-image-slider"})})}),S.jsxs("div",{className:"row mt-4",children:[S.jsx("div",{className:"col-12 col-sm-7",children:S.jsxs("div",{className:"col-12 col-md-11",children:[S.jsx("p",{className:"font-24 font-600 mb-3",children:"Property details"}),S.jsxs("div",{className:"property-detail-area",children:[S.jsxs("div",{className:"d-flex align-items-center",children:[S.jsx("div",{children:S.jsx("img",{src:"/assets/img/area-icon.png",alt:""})}),S.jsxs("div",{className:"ms-2",children:[S.jsx("p",{className:"font-16",children:"Total area"}),S.jsxs("p",{className:"font-14",children:[u(a.size)||"N/A"," ",a.size_type||"Sq. Ft"]})]})]}),S.jsxs("div",{className:"d-flex align-items-center",children:[S.jsx("div",{children:S.jsx("img",{src:"/assets/img/beddetail-icon.png",alt:""})}),S.jsxs("div",{className:"ms-2",children:[S.jsx("p",{className:"font-16",children:"Bedrooms"}),S.jsx("p",{className:"font-14",children:u(a.bed)||"N/A"})]})]}),S.jsxs("div",{className:"d-flex align-items-center",children:[S.jsx("div",{children:S.jsx("img",{src:"/assets/img/bath-tub-icon.png",alt:""})}),S.jsxs("div",{className:"ms-2",children:[S.jsx("p",{className:"font-16",children:"Bathrooms"}),S.jsx("p",{className:"font-14",children:u(a.bath)||"N/A"})]})]})]}),S.jsxs("div",{className:"row mt-5  ",children:[S.jsx("div",{className:"col-12 col-md-6",children:m.leftItems.map((y,M)=>S.jsxs("div",{className:"d-flex align-items-center justify-content-between item-border-bottom mb-2 pb-2",children:[S.jsxs("div",{className:"d-flex align-items-center",children:[S.jsx("div",{className:"base-img",children:S.jsx("img",{src:y.icon,alt:y.label})}),S.jsx("div",{className:"ms-2",children:S.jsx("p",{className:"color-grey",children:y.label})})]}),S.jsx("div",{children:S.jsx("p",{className:"color-black",children:y.value})})]},M))}),S.jsx("div",{className:"col-12 col-md-6",children:m.rightItems.map((y,M)=>S.jsxs("div",{className:"d-flex align-items-center justify-content-between item-border-bottom mb-2 pb-2",children:[S.jsxs("div",{className:"d-flex align-items-center",children:[S.jsx("div",{className:"base-img",children:S.jsx("img",{src:y.icon,alt:y.label})}),S.jsx("div",{className:"ms-2",children:S.jsx("p",{className:"color-grey",children:y.label})})]}),S.jsx("div",{children:S.jsx("p",{className:"color-black",children:y.value})})]},M))})]}),S.jsx("div",{className:"row mt-4 mb-5",children:S.jsxs("div",{className:"col-12",children:[S.jsx("p",{className:"font-24 font-600 color-black",children:"Description"}),S.jsx("p",{className:"color-grey font-16 mt-2",children:a.description||"No description available."})]})})]})}),S.jsx("div",{className:"col-12 col-sm-5",children:S.jsx("div",{className:"mt-5",children:S.jsxs("div",{className:"profile-detail-area",children:[S.jsxs("div",{className:"d-flex align-items-center mb-3",children:[S.jsx("div",{className:"profile",children:S.jsx("img",{src:(f=a==null?void 0:a.user)==null?void 0:f.image_url,alt:""})}),S.jsxs("div",{className:"ms-3 detail",children:[S.jsx("p",{className:"font-16",children:((p=a==null?void 0:a.user)==null?void 0:p.name)||"Agent Name"}),S.jsx("p",{className:"white-light",children:window.helper.getLabel("professional_type",(b=a==null?void 0:a.user)==null?void 0:b.professional_type)})]})]}),S.jsxs("div",{className:"d-flex align-items-center",children:[S.jsx("div",{className:"me-3",children:S.jsx(ae,{icon:S.jsx("img",{src:"/assets/img/call-icon.png"}),title:((x=a==null?void 0:a.user)==null?void 0:x.mobile_no)||"***** 456789",className:"number-button"})}),S.jsx("div",{className:"me-3",children:S.jsx(ae,{icon:S.jsx("img",{src:"/assets/img/phone-rounded.png"}),title:"Call",className:"number-button"})}),S.jsx("div",{className:"me-3",children:S.jsx(ae,{icon:S.jsx("img",{src:"/assets/img/chat.png"}),title:"Chat",className:"number-button",onClick:v})})]})]})})})]})]})})};export{ks as default};
