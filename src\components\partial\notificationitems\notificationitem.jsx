import { useNavigate } from "react-router-dom";

// NotificationItem.js
const NotificationItem = ({ avatar, message, time, notification }) => {
  const navigate = useNavigate();
  const handleClick = () => {
    const { recordId, redirectTo, customData } = notification;

    if (redirectTo == "post") {
      navigate(`/sphare-it/${recordId}`);
    }
    if (redirectTo == "post" && customData?.is_anonymous == true) {
      navigate(`/contract/${recordId}`);
    }
  };
  return (
    <div
      className="not-box d-flex align-items-center"
      onClick={handleClick}
      style={{ cursor: "pointer" }}
    >
      <div className="notifoication-avatar">
        <img src={avatar} alt="avatar" />
      </div>
      <div className="ms-2">
        <p>{message}</p>
        <p>{time}</p>
      </div>
    </div>
  );
};

export default NotificationItem;
