import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import SpherePost from "@/components/shared/card/spherepost";
import { useQuery, useMutation } from "@/hooks/reactQuery";
import { Skeleton, Empty, message } from "antd";
import { usePostCreation } from "@/pages/sphereit/hooks";
import useSweetAlert from "@/hooks/useSweetAlert";
import _ from "lodash";

const AgentPosts = ({ userId, isMyProfile = false }) => {
  const navigate = useNavigate();
  const { showAlert } = useSweetAlert();
  const [editingPost, setEditingPost] = useState(null);

  // Fetch posts for specific user
  const {
    data: postsResponse,
    isLoading,
    error,
    refetch,
  } = useQuery("postItem", {
    params: { user_id: userId },
    enabled: !!userId,
    staleTime: 0,
    gcTime: 0,
  });

  // Post creation hook for editing
  const postCreationHook = usePostCreation(editingPost, () => {
    setEditingPost(null);
    refetch();
  });

  // Delete post mutation
  const { mutate: deletePost, isPending: isDeleting } = useMutation(
    "deletePost",
    {
      useFormData: false,
      showSuccessNotification: true,
      invalidateQueries: [{ queryKey: ["postItem"], type: "all" }],
      onSuccess: () => {
        refetch();
      },
    }
  );

  const posts = postsResponse?.data || [];

  const handleEditPost = (postId) => {
    const postToEdit = posts.find((post) => post.id === postId);
    if (postToEdit) {
      setEditingPost(postToEdit);
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  const handleDeletePost = async (postId) => {
    const result = await showAlert({
      title: "Are you sure?",
      text: "Are you sure you want to delete this post?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "Cancel",
    });

    if (result.isConfirmed) {
      deletePost({ slug: postId, data: "" });
    }
  };

  const checkPostOwnership = (post) => {
    return (
      isMyProfile ||
      post?.user?.id === window.user?.id ||
      post?.user_id === window.user?.id
    );
  };

  if (isLoading) {
    return (
      <div className="row mt-4">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="col-12 col-md-6 col-lg-4 col-xl-3 mb-4">
            <div className="card">
              <div className="card-body">
                <div className="d-flex align-items-center mb-3">
                  <Skeleton.Avatar size={40} />
                  <div className="ms-3 flex-grow-1">
                    <Skeleton.Input style={{ width: 120, height: 16 }} />
                    <div className="mt-1">
                      <Skeleton.Input style={{ width: 80, height: 12 }} />
                    </div>
                  </div>
                </div>

                <Skeleton.Image
                  active
                  className="w-100 text-center align-items-center d-flex mb-2"
                  style={{ width: "100%", height: 200, display: "block" }}
                />

                <Skeleton paragraph={{ rows: 2, width: ["100%", "80%"] }} />
                <div className="d-flex justify-content-between align-items-center mt-3">
                  <div className="d-flex gap-3">
                    <Skeleton.Input style={{ width: 60, height: 20 }} />
                    <Skeleton.Input style={{ width: 60, height: 20 }} />
                    <Skeleton.Input style={{ width: 60, height: 20 }} />
                  </div>
                  <Skeleton.Input style={{ width: 40, height: 20 }} />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="row mt-4">
        <div className="col-12 d-flex justify-content-center">
          <Empty
            description="Failed to load posts. Please try again."
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        </div>
      </div>
    );
  }

  if (_.isEmpty(posts)) {
    return (
      <div className="row py-5">
        <div className="col-12 d-flex justify-content-center">
          <Empty description={"No data"} image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </div>
      </div>
    );
  }

  return (
    <div className="row mt-4">
      {posts.map((post) => (
        <div key={post.id} className="col-12 col-md-6 col-lg-4 col-xl-3 mb-4">
          <SpherePost
            {...post}
            onClick={() => navigate(`/sphare-it/${post.id}`)}
            showActions={checkPostOwnership(post)}
            onEdit={handleEditPost}
            onDelete={handleDeletePost}
          />
        </div>
      ))}
    </div>
  );
};

export default AgentPosts;
