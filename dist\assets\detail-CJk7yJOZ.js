import{u as _,r as P,j as e,_ as T,R as k,f as B}from"./index-Dklazue-.js";import{u as S,I as y}from"./index-vmMMvWhJ.js";import{I as b}from"./iconbutton-DMkwwwLX.js";import{F as E}from"./flatbutton-B_tUS4QM.js";import{u as A}from"./useQuery-C3n1GVcJ.js";import{u as L,S as F}from"./spherepost-C2uEIc3S.js";import{u as D}from"./useMutation-BrUrPIzr.js";import{S as i}from"./Skeleton--lTrbnrp.js";import{E as f}from"./index-CHbHgJvR.js";import{t as $,P as R}from"./propertyUtils-DF3QzgTW.js";import{E as C}from"./EmptyState-YbiQZMha.js";import{B as z}from"./button-DNhBCuue.js";import"./index-Cj6uPc4c.js";import"./useLikePost-BsM_zOKm.js";import"./EditOutlined-DxjsqgjX.js";import"./DeleteOutlined-BjE_e6LE.js";import"./useLocale-BNhrTARD.js";const U=({userId:s,isMyProfile:u=!1})=>{const m=_(),{showAlert:x}=S(),[c,r]=P.useState(null),{data:d,isLoading:p,error:N,refetch:g}=A("postItem",{params:{user_id:s},enabled:!!s,staleTime:0,gcTime:0});L(c,()=>{r(null),g()});const{mutate:a,isPending:v}=D("deletePost",{useFormData:!1,showSuccessNotification:!0,invalidateQueries:[{queryKey:["postItem"],type:"all"}],onSuccess:()=>{g()}}),n=(d==null?void 0:d.data)||[],h=t=>{const o=n.find(w=>w.id===t);o&&(r(o),window.scrollTo({top:0,behavior:"smooth"}))},l=async t=>{(await x({title:"Are you sure?",text:"Are you sure you want to delete this post?",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, delete it!",cancelButtonText:"Cancel"})).isConfirmed&&a({slug:t,data:""})},j=t=>{var o,w,I;return u||((o=t==null?void 0:t.user)==null?void 0:o.id)===((w=window.user)==null?void 0:w.id)||(t==null?void 0:t.user_id)===((I=window.user)==null?void 0:I.id)};return p?e.jsx("div",{className:"row mt-4",children:Array.from({length:6}).map((t,o)=>e.jsx("div",{className:"col-12 col-md-6 col-lg-4 col-xl-3 mb-4",children:e.jsx("div",{className:"card",children:e.jsxs("div",{className:"card-body",children:[e.jsxs("div",{className:"d-flex align-items-center mb-3",children:[e.jsx(i.Avatar,{size:40}),e.jsxs("div",{className:"ms-3 flex-grow-1",children:[e.jsx(i.Input,{style:{width:120,height:16}}),e.jsx("div",{className:"mt-1",children:e.jsx(i.Input,{style:{width:80,height:12}})})]})]}),e.jsx(i.Image,{active:!0,className:"w-100 text-center align-items-center d-flex mb-2",style:{width:"100%",height:200,display:"block"}}),e.jsx(i,{paragraph:{rows:2,width:["100%","80%"]}}),e.jsxs("div",{className:"d-flex justify-content-between align-items-center mt-3",children:[e.jsxs("div",{className:"d-flex gap-3",children:[e.jsx(i.Input,{style:{width:60,height:20}}),e.jsx(i.Input,{style:{width:60,height:20}}),e.jsx(i.Input,{style:{width:60,height:20}})]}),e.jsx(i.Input,{style:{width:40,height:20}})]})]})})},o))}):N?e.jsx("div",{className:"row mt-4",children:e.jsx("div",{className:"col-12 d-flex justify-content-center",children:e.jsx(f,{description:"Failed to load posts. Please try again.",image:f.PRESENTED_IMAGE_SIMPLE})})}):T.isEmpty(n)?e.jsx("div",{className:"row py-5",children:e.jsx("div",{className:"col-12 d-flex justify-content-center",children:e.jsx(f,{description:"No data",image:f.PRESENTED_IMAGE_SIMPLE})})}):e.jsx("div",{className:"row mt-4",children:n.map(t=>e.jsx("div",{className:"col-12 col-md-6 col-lg-4 col-xl-3 mb-4",children:e.jsx(F,{...t,onClick:()=>m(`/sphare-it/${t.id}`),showActions:j(t),onEdit:h,onDelete:l})},t.id))})},q=({userId:s,isMyProfile:u=!1})=>{const m=_(),{showAlert:x}=S(),{data:c,isLoading:r,error:d}=A("properties",{params:{user_id:s},enabled:!!s,staleTime:0,gcTime:0}),{mutate:p,isPending:N}=D("deleteProperty",{showSuccessNotification:!0,invalidateQueries:[{queryKey:["properties"]},{queryKey:["getProperty"]},{queryKey:["getUser"]}]}),g=n=>{m(`/listing/add/${n.id}`)},a=async n=>{(await x({title:"Are you sure?",text:"Do you want to delete this property",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, delete it!",cancelButtonText:"Cancel"})).isConfirmed&&p({slug:n.id,data:""})},v=k.useMemo(()=>c!=null&&c.data?$(c.data,{showActions:!0,onEdit:g,onDelete:a}):[],[c]);return r?e.jsx("div",{className:"row mt-4",children:Array.from({length:12}).map((n,h)=>e.jsx("div",{className:"col-12 col-sm-6 col-md-4 col-lg-3",children:e.jsx(i,{active:!0,paragraph:{rows:4}})},h))}):d?e.jsx(C,{title:"Failed to load properties",description:"Failed to load properties. Please try again."}):T.isEmpty(v)?e.jsx(C,{title:"No properties found",description:u?"You haven't listed any properties yet":"This agent hasn't listed any properties yet"}):e.jsx("div",{className:"row mt-3",children:v.map((n,h)=>e.jsx("div",{className:"col-12 col-sm-6 col-md-4 col-lg-3",children:e.jsx(R,{...n})},n.id||h))})},ne=({isMyProfile:s=!1})=>{var h;const u=B(),m=_(),[x,c]=P.useState("posts"),r=s?(h=window.user)==null?void 0:h.id:u.user_id,d=u.type,{data:p,isLoading:N,error:g}=A("getUser",{slug:r,enabled:!s&&!!r,staleTime:0,gcTime:0}),a=s?window.user:p==null?void 0:p.data,v=l=>{var j;return l?l.professional_types&&Array.isArray(l.professional_types)?l.professional_types.map(t=>{var o;return((o=window.helper)==null?void 0:o.getLabel("professional_type",t.name))||t.name}).join(", "):((j=window.helper)==null?void 0:j.getLabel("professional_type",l.professional_type))||l.professional_type||"Real Estate Professional":"Real Estate Professional"};P.useEffect(()=>{if(d&&["posts","properties"].includes(d))c(d);else{const l=s?"/profile/posts":`/agent/detail/${r}/posts`;m(l,{replace:!0})}},[d,m,s,r]);const n=l=>{c(l);const j=s?`/profile/${l}`:`/agent/detail/${r}/${l}`;m(j)};return!s&&N?e.jsx(y,{children:e.jsx("div",{className:"container-fluid",children:e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-12",children:e.jsxs("div",{className:"agent-box mt-5",children:[e.jsx("div",{className:"agent-header w-100",children:e.jsx(i.Image,{active:!0,className:"w-100 text-center align-items-center d-flex ",style:{width:"100%",height:200,display:"block"}})}),e.jsxs("div",{className:"agent-body d-flex align-items-center justify-content-between p-4",children:[e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx(i.Avatar,{size:80}),e.jsxs("div",{className:"ms-3",children:[e.jsx(i.Input,{style:{width:150,height:20}}),e.jsx("div",{className:"mt-2",children:e.jsx(i.Input,{style:{width:120,height:16}})}),e.jsx("div",{className:"mt-2",children:e.jsx(i.Input,{style:{width:100,height:16}})})]})]}),e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx(i.Button,{size:"default",className:"me-3"}),e.jsx(i.Button,{size:"default",className:"me-3"}),e.jsx(i.Button,{size:"default"})]})]})]})})})})}):!s&&g||!a?e.jsx(y,{children:e.jsx("div",{className:"container-fluid",children:e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-12 mt-4",children:e.jsx(f,{description:s?"Profile data not available":"Agent not found",image:f.PRESENTED_IMAGE_SIMPLE,children:e.jsx(z,{type:"primary",onClick:()=>m(s?"/home":"/agent"),children:s?"Go to Home":"Back to Agents"})})})})})}):e.jsx(y,{children:e.jsx("div",{className:"container-fluid",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12",children:e.jsxs("div",{className:"agent-box mt-5",children:[e.jsx("div",{className:"agent-header",children:e.jsx("img",{src:a.cover_image||"/assets/img/home-img.png",alt:"Cover",style:{width:"100%",height:"200px",objectFit:"cover"}})}),e.jsxs("div",{className:"agent-body d-flex align-items-center justify-content-between",children:[e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("div",{className:"agent-profile",children:e.jsx("img",{src:a.image_url||"/assets/img/avatar-2.png",alt:"Profile",style:{width:"80px",height:"80px",borderRadius:"50%",objectFit:"cover"}})}),e.jsxs("div",{className:"ms-3",children:[e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("p",{className:"me-2 font-600",children:a.name||"Unknown User"}),a.is_verified&&e.jsx("img",{src:"/assets/img/badge.png",alt:"Verified",className:"img-fluid"})]}),e.jsx("p",{className:"color-light",children:v(a)}),e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("div",{children:e.jsx("img",{src:"/assets/img/location_on.png",alt:"Location"})}),e.jsx("div",{children:e.jsx("p",{children:a.city||a.state||"Location not specified"})})]})]})]}),e.jsx("div",{className:"d-flex align-items-center",children:s?e.jsx("div",{className:"me-3",children:e.jsx(E,{title:"Edit your Profile",className:"blue-btn",onClick:()=>m("/editprofile")})}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"me-3",children:e.jsx(b,{icon:e.jsx("img",{src:"/assets/img/call-icon.png",alt:"Call"}),title:"Call",className:"gray-btn",onClick:()=>window.open(`tel:${a.mobile_no}`)})}),e.jsx("div",{className:"me-3",children:e.jsx(b,{icon:e.jsx("img",{src:"/assets/img/message-icon.png",alt:"Message"}),title:"Message",className:"gray-btn"})}),e.jsx("div",{className:"me-3",children:e.jsx(b,{icon:e.jsx("img",{src:"/assets/img/mail-icon.png",alt:"Mail"}),title:"Mail",className:"blue-btn",onClick:()=>window.open(`mailto:${a.email}`)})})]})})]})]})}),e.jsx("div",{className:"col-12 mt-5",children:e.jsxs("div",{className:"text-center",children:[e.jsx(E,{title:"Posts",className:x==="posts"?"active-tab-button":"post-tab-button",onClick:()=>n("posts")}),e.jsx(E,{title:"Listings",className:x==="properties"?"active-tab-button":"post-tab-button",onClick:()=>n("properties")})]})}),e.jsxs("div",{className:"col-12",children:[x==="posts"&&e.jsx(U,{userId:r,isMyProfile:s}),x==="properties"&&e.jsx(q,{userId:r,isMyProfile:s})]})]})})})};export{ne as default};
