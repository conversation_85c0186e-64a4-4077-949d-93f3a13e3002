import React, { useState } from "react";
import {
  PictureOutlined,
  VideoCameraOutlined,
  CloseOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import FlatButton from "@/components/shared/button/flatbutton";
import BaseInput from "@/components/shared/inputs";
import { Input, Upload, Image, Button } from "antd";
import useStartupData from "@/hooks/reactQuery/useStartupData";
import useSweetAlert from "@/hooks/useSweetAlert";
import { useNavigate } from "react-router-dom";
import { handleTrialCheck } from "@/utils/trialUtils";

const { TextArea } = Input;

const ContractPostInput = ({
  placeholder = "What's on your mind?",
  postCreationHook,
  onCancelEdit,
}) => {
  const { showAlert } = useSweetAlert();
  const { data: startupResponse, isLoading: startupLoading } = useStartupData();
  const startupData = startupResponse?.data;
  const navigate = useNavigate();
  const {
    content,
    setContent,
    selectedFile,
    filePreview,
    fileType,
    questionId,
    setQuestionId,
    isCreatingPost,
    isFormValid,
    isEditMode,
    handleImageUpload,
    handleVideoUpload,
    removeFile,
    handleSubmit,
  } = postCreationHook || {};

  // Generate question options from startup data
  const questionOptions = React.useMemo(() => {
    if (!startupData?.postQuestions) return [];
    return startupData.postQuestions.map((question) => ({
      value: question.id,
      label: question.name || question.title || question.question,
    }));
  }, [startupData?.postQuestions]);

  const handleTextAreaClick = async () => {
    await handleTrialCheck(showAlert, navigate);
  };

  const handleImageUploadClick = async (file) => {
    const trialCheckFailed = await handleTrialCheck(showAlert, navigate);
    if (!trialCheckFailed && handleImageUpload) {
      return handleImageUpload({ file });
    }
    return false;
  };

  const handleVideoUploadClick = async (file) => {
    const trialCheckFailed = await handleTrialCheck(showAlert, navigate);
    if (!trialCheckFailed && handleVideoUpload) {
      return handleVideoUpload({ file });
    }
    return false;
  };

  const handlePostSubmit = async () => {
    const trialCheckFailed = await handleTrialCheck(showAlert, navigate);
    if (!trialCheckFailed && handleSubmit) {
      handleSubmit();
    }
  };

  return (
    <>
      <div className="mb-3">
        <BaseInput
          name="question_id"
          type="select"
          placeholder="Question"
          value={questionId}
          handlechange={setQuestionId}
          options={questionOptions}
          loading={startupLoading}
          disabled={isCreatingPost}
        />
      </div>
      <div
        style={{
          maxWidth: "100%",
          border: "1px solid #ddd",
          borderRadius: "10px",
        }}
        className="mt-4"
      >
        <div
          style={{
            backgroundColor: "#fff",
            padding: "10px",
            borderRadius: "10px",
          }}
        >
          {/* Question Selector */}

          <TextArea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            onClick={handleTextAreaClick}
            placeholder={placeholder}
            autoSize={{ minRows: 3, maxRows: 5 }}
            style={{
              border: "none",
              resize: "none",
              marginBottom: "10px",
              background: "transparent",
            }}
            disabled={isCreatingPost}
          />

          {/* File Preview */}
          {filePreview && (
            <div
              className="mb-3 position-relative"
              style={{ maxWidth: "200px" }}
            >
              {fileType === "image" ? (
                <Image
                  width={200}
                  height={150}
                  src={filePreview}
                  style={{ objectFit: "cover", borderRadius: "8px" }}
                  preview={false}
                />
              ) : (
                <video
                  width={200}
                  height={150}
                  controls
                  style={{ borderRadius: "8px", objectFit: "cover" }}
                >
                  <source src={filePreview} type={selectedFile?.type} />
                  Your browser does not support the video tag.
                </video>
              )}
              <Button
                type="text"
                danger
                icon={<CloseOutlined />}
                size="small"
                onClick={removeFile}
                style={{
                  position: "absolute",
                  top: "5px",
                  right: "5px",
                  backgroundColor: "rgba(0,0,0,0.5)",
                  color: "white",
                  border: "none",
                }}
                disabled={isCreatingPost}
              />
            </div>
          )}

          <div className="d-flex justify-content-between align-items-center icon-color">
            <div className="d-flex gap-3">
              {/* Image Upload */}
              <Upload
                showUploadList={false}
                beforeUpload={handleImageUploadClick}
                accept="image/*"
                disabled={isCreatingPost}
              >
                <PictureOutlined
                  style={{
                    cursor: isCreatingPost ? "not-allowed" : "pointer",
                    fontSize: "24px",
                    color: fileType === "image" ? "#1890ff" : undefined,
                  }}
                  title="Upload Image"
                />
              </Upload>

              {/* Video Upload */}
              <Upload
                showUploadList={false}
                beforeUpload={handleVideoUploadClick}
                accept="video/*"
                disabled={isCreatingPost}
              >
                <VideoCameraOutlined
                  style={{
                    cursor: isCreatingPost ? "not-allowed" : "pointer",
                    fontSize: "24px",
                    color: fileType === "video" ? "#1890ff" : undefined,
                  }}
                  title="Upload Video"
                />
              </Upload>
            </div>

            <div className="d-flex gap-2">
              {isEditMode && onCancelEdit && (
                <FlatButton
                  type="default"
                  title="Cancel"
                  className="px-4 post-btn px-5"
                  onClick={onCancelEdit}
                  disabled={isCreatingPost}
                />
              )}
              <FlatButton
                type="primary"
                title={
                  isCreatingPost
                    ? isEditMode
                      ? "Updating..."
                      : "Posting..."
                    : isEditMode
                    ? "Update"
                    : "Post"
                }
                className="post-btn px-5"
                onClick={handlePostSubmit}
                disabled={!isFormValid || isCreatingPost}
                icon={isCreatingPost ? <LoadingOutlined /> : undefined}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ContractPostInput;