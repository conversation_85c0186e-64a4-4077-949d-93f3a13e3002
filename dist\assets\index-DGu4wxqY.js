import{j as e,F as A,r as c,R as q,_ as L}from"./index-Dklazue-.js";import{I as O}from"./index-vmMMvWhJ.js";import{t as R,P as T}from"./propertyUtils-DF3QzgTW.js";import{S as k}from"./searchbar-BCS1MYCc.js";import{B as w}from"./index-BUt89ETK.js";import{F as D}from"./Filter-D5O_6hp-.js";import{R as G}from"./ReusablePagination-B7_yMXG_.js";import{E as H}from"./EmptyState-YbiQZMha.js";import{u as M}from"./useSearchFilterPagination-BuDUyyEy.js";import{u as Y}from"./useLocationData-CQHRFCOb.js";import{u as Z}from"./useStartupData-QD8kuEYy.js";import{S as s}from"./Skeleton--lTrbnrp.js";import"./useMutation-BrUrPIzr.js";import"./button-DNhBCuue.js";import"./index-Cj6uPc4c.js";import"./EditOutlined-DxjsqgjX.js";import"./DeleteOutlined-BjE_e6LE.js";import"./index-CjGjc6T5.js";import"./index-CHbHgJvR.js";import"./useLocale-BNhrTARD.js";import"./react-stripe.esm-ypQSOYN5.js";import"./languageUtils-BKYM3hOY.js";import"./index-CatU6E6a.js";import"./index-ChnPz6Q1.js";import"./fade-B36sOBLs.js";import"./useQuery-C3n1GVcJ.js";const $=()=>{var b,N;const[l,j]=c.useState(""),[i,p]=c.useState(""),[r,u]=c.useState(!1),{data:x,loading:d}=Z(),n=x==null?void 0:x.data,{stateOptions:m,cityOptions:h,statesLoading:I,citiesLoading:g,handleStateChange:y}=Y(),{data:o,isLoading:f,pagination:C,handlePageChange:_}=M("properties",{pageSize:12,enabled:r&&l&&i,defaultParams:r?{state:l,city:i}:{}});c.useEffect(()=>{if(!d&&m.length>0&&!l){const t=m[0].value;j(t),y(t)}},[d,m,l,y]),c.useEffect(()=>{if(l&&!g&&h.length>0&&!i){const t=h[0].value;p(t),u(!0)}},[l,g,h,i]);const P=t=>{j(t),p(""),y(t)},z=t=>{p(t),u(!0)},v=q.useMemo(()=>o!=null&&o.data?R(o.data):[],[o]),S=Array.from({length:10},(t,a)=>({value:a+1,label:a+1})),B=Array.from({length:10},(t,a)=>({value:a+1,label:`${a+1} Car${a+1>1?"s":""}`})),F=[{value:"sqft",label:"Sq.Ft"},{value:"sq.m",label:"Sq.M"},{value:"sq.yd",label:"Sq.Yd"},{value:"acres",label:"Acres"}],E=[{name:"type_id",label:"Property Type",type:"select",placeholder:"Select Property Type",options:(b=n==null?void 0:n.propertyTypes)==null?void 0:b.map(t=>({value:t.id,label:t.name})),loading:d},{name:"home_style_id",label:"Home Style",type:"select",placeholder:"Select Home Style",options:(N=n==null?void 0:n.homeStyles)==null?void 0:N.map(t=>({value:t.id,label:t.name})),loading:d},{name:"state",label:"State",type:"select",placeholder:"Select State"},{name:"city",label:"City",type:"select",placeholder:"Select City"},{name:"zip",label:"Zip Code",type:"input",placeholder:"Enter Zip Code"},{name:"basement",label:"Basement",type:"radio",options:[{value:!0,label:"Yes"},{value:!1,label:"No"}]},{name:"max_bed",label:"Bed",type:"select",placeholder:"Select Bedrooms",options:S},{name:"max_bath",label:"Bath",type:"select",placeholder:"Select Bathrooms",options:S},{name:"max_garage",label:"Garage",type:"select",placeholder:"Select Garage",options:B},{name:"size_type",label:"Size Type",type:"select",placeholder:"Select Size Type",options:F}];return e.jsx(O,{children:e.jsxs("div",{className:"container-fluid",children:[e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12 state-filter",children:e.jsx(k,{})}),e.jsx("div",{className:"col-12 mt-3",children:e.jsxs("div",{className:"d-flex align-items-center justify-content-between",children:[e.jsx("div",{children:r?e.jsx("p",{className:"font-36 font-600",children:i}):e.jsx(s.Input,{style:{width:200,height:36}})}),e.jsx("div",{className:"d-flex align-items-center state-select-area",children:r?e.jsxs(e.Fragment,{children:[e.jsx(w,{type:"select",placeholder:"State",className:"ms-3",options:m,loading:I,value:l,handlechange:P,showSearch:!0}),e.jsx(w,{type:"select",placeholder:"City",options:h,loading:g,value:i,handlechange:z,disabled:!l,showSearch:!0},l)]}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"ms-3",children:e.jsx(s.Input,{style:{width:150,height:40}})}),e.jsx("div",{children:e.jsx(s.Input,{style:{width:150,height:40}})})]})})]})})]}),e.jsx(D,{fields:E}),e.jsx("div",{className:"row mt-3",children:r?f?Array.from({length:12}).map((t,a)=>e.jsx("div",{className:"col-12 col-sm-6 col-md-4 col-lg-3",children:e.jsx("div",{className:"card",children:e.jsxs("div",{className:"card-body",children:[e.jsxs("div",{className:"d-flex align-items-center mb-3",children:[e.jsx(s.Avatar,{size:40}),e.jsxs("div",{className:"ms-3 flex-grow-1",children:[e.jsx(s.Input,{style:{width:120,height:16}}),e.jsx("div",{className:"mt-1",children:e.jsx(s.Input,{style:{width:80,height:12}})})]})]}),e.jsx(s.Image,{active:!0,className:"w-100 text-center align-items-center d-flex mb-2",style:{width:"100%",height:200,display:"block"}}),e.jsx(s,{paragraph:{rows:2,width:["100%","80%"]}}),e.jsxs("div",{className:"d-flex justify-content-between align-items-center mt-3",children:[e.jsxs("div",{className:"d-flex gap-3",children:[e.jsx(s.Input,{style:{width:60,height:20}}),e.jsx(s.Input,{style:{width:60,height:20}}),e.jsx(s.Input,{style:{width:60,height:20}})]}),e.jsx(s.Input,{style:{width:40,height:20}})]})]})})},a)):L.isEmpty(v)?e.jsx(H,{title:"No properties found",description:"No properties available in this location"}):v.map((t,a)=>e.jsx("div",{className:"col-12 col-sm-6 col-md-4 col-lg-3",children:e.jsx(T,{...t})},t.id||a)):Array.from({length:12}).map((t,a)=>e.jsx("div",{className:"col-12 col-sm-6 col-md-4 col-lg-3",children:e.jsx("div",{className:"card",children:e.jsxs("div",{className:"card-body",children:[e.jsxs("div",{className:"d-flex align-items-center mb-3",children:[e.jsx(s.Avatar,{size:40}),e.jsxs("div",{className:"ms-3 flex-grow-1",children:[e.jsx(s.Input,{style:{width:120,height:16}}),e.jsx("div",{className:"mt-1",children:e.jsx(s.Input,{style:{width:80,height:12}})})]})]}),e.jsx(s.Image,{active:!0,className:"w-100 text-center align-items-center d-flex mb-2",style:{width:"100%",height:200,display:"block"}}),e.jsx(s,{paragraph:{rows:2,width:["100%","80%"]}}),e.jsxs("div",{className:"d-flex justify-content-between align-items-center mt-3",children:[e.jsxs("div",{className:"d-flex gap-3",children:[e.jsx(s.Input,{style:{width:60,height:20}}),e.jsx(s.Input,{style:{width:60,height:20}}),e.jsx(s.Input,{style:{width:60,height:20}})]}),e.jsx(s.Input,{style:{width:40,height:20}})]})]})})},a))}),r&&e.jsx(G,{pagination:C,handlePageChange:_,isLoading:f,itemName:"properties",pageSizeOptions:["12","24","48"]})]})})},fe=()=>e.jsx(A,{children:e.jsx($,{})});export{fe as default};
