import React, { memo, useState, useEffect } from "react";
import AuthLayout from "@/components/shared/layout/authlayout";
import BaseInput from "@/components/shared/inputs/index";
import FlatButton from "@/components/shared/button/flatbutton";
import { Checkbox, Form } from "antd";
import { Link, useNavigate } from "react-router-dom";
import { useMutation } from "@/hooks/reactQuery";
import { combineRules, validations } from "@/config/rules";
import Helper from "@/helpers";
import notificationService from "@/services/notification";
import {
  signInWithPopup,
  signInWithRedirect,
  getRedirectResult,
} from "firebase/auth";
import { auth, googleProvider } from "@/firebase";

const Login = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [rememberMe, setRememberMe] = useState(false);
  const [initialValues, setInitialValues] = useState({});
  const [deviceToken, setDeviceToken] = useState(null);
  const [isGoogleSignInLoading, setIsGoogleSignInLoading] = useState(false);

  useEffect(() => {
    const initNotifications = async () => {
      try {
        const token = await notificationService.init();
        setDeviceToken(token);
      } catch (error) {
        const fallbackToken = `web_init_error_${Date.now()}`;
        setDeviceToken(fallbackToken);
        localStorage.setItem("device_token", fallbackToken);
      }
    };

    initNotifications();

    // Check if we're returning from Google Sign-In redirect
    const wasGoogleSignInLoading = localStorage.getItem("googleSignInLoading");
    if (wasGoogleSignInLoading === "true") {
      setIsGoogleSignInLoading(true);
    }
  }, []);
  const { mutate, isPending } = useMutation("login", {
    useFormData: false,
    onSuccess: async (data) => {
      if (data) {
        await Helper.setStorageData("session", data.data);
        window.user = data.data;
        localStorage.removeItem("googleSignupData");
        localStorage.removeItem("googleUserData");
        const formValues = form.getFieldsValue();
        if (rememberMe) {
          await Helper.setStorageData("rememberedCredentials", {
            email: formValues.email,
            password: formValues.password,
            rememberMe: true,
            savedAt: Date.now(),
          });
        } else {
          localStorage.removeItem("rememberedCredentials");
        }

        navigate("/home", { replace: true });
      }
    },
    onError: (error, variables) => {
      console.log("error", error);

      // Handle ONLY Google Sign-In 400 error - redirect to signup with pre-filled data
      // Do NOT redirect for regular email login errors
      if (variables.type === "social" && error?.status === 400) {
        const googleUserData = JSON.parse(
          localStorage.getItem("googleUserData") || "{}"
        );
        if (googleUserData.email) {
          // Store Google data for signup page
          localStorage.setItem(
            "googleSignupData",
            JSON.stringify({
              email: googleUserData.email,
              full_name:
                googleUserData.displayName || googleUserData.name || "",
              social_id: googleUserData.uid,
              social_platform: "google",
            })
          );

          // Clean up temporary storage
          localStorage.removeItem("googleUserData");

          // Redirect to signup
          navigate("/signup", { replace: true });
          return;
        }
      }

      // Clean up loading states for any error
      localStorage.removeItem("googleSignInLoading");
      setIsGoogleSignInLoading(false);
    },
  });

  useEffect(() => {
    const loadSavedCredentials = async () => {
      try {
        const savedCredentials = await Helper.getStorageData(
          "rememberedCredentials"
        );

        if (savedCredentials && savedCredentials.email) {
          const values = {
            email: savedCredentials.email,
            password: savedCredentials.password || "",
          };

          setInitialValues(values);
          form.setFieldsValue(values);
          setRememberMe(true);
        }
      } catch (error) {}
    };

    loadSavedCredentials();
  }, [form]);

  // Handle Google Sign-In redirect result
  useEffect(() => {
    const handleRedirectResult = async () => {
      try {
        const result = await getRedirectResult(auth);
        if (result) {
          const user = result.user;

          // Get device token
          let currentToken =
            deviceToken ||
            notificationService.getToken() ||
            localStorage.getItem("device_token");

          if (!currentToken) {
            currentToken = `web_final_fallback_${Date.now()}`;
            localStorage.setItem("device_token", currentToken);
          }

          // Store Google user data for potential signup redirect
          localStorage.setItem(
            "googleUserData",
            JSON.stringify({
              uid: user.uid,
              email: user.email,
              displayName: user.displayName,
              name: user.displayName,
            })
          );

          // Prepare data in the format expected by your API
          const googleSignInData = {
            type: "social",
            social_id: user.uid,
            social_platform: "google",
            email: user.email,
            device: "web",
            device_token: currentToken,
          };

          // Clean up loading state
          localStorage.removeItem("googleSignInLoading");

          // Use the same mutation as regular login
          mutate(googleSignInData);
        } else {
          // No redirect result, clean up loading state if it exists
          const wasLoading = localStorage.getItem("googleSignInLoading");
          if (wasLoading === "true") {
            localStorage.removeItem("googleSignInLoading");
            setIsGoogleSignInLoading(false);
          }
        }
      } catch (error) {
        console.error("Google Sign-In Redirect Error:", error);
        localStorage.removeItem("googleSignInLoading");
        setIsGoogleSignInLoading(false);
      }
    };

    handleRedirectResult();
  }, [deviceToken, mutate]);

  const onFinish = (values) => {
    let currentToken =
      deviceToken ||
      notificationService.getToken() ||
      localStorage.getItem("device_token");

    if (!currentToken) {
      currentToken = `web_final_fallback_${Date.now()}`;
      localStorage.setItem("device_token", currentToken);
    }

    const transformedData = {
      ...values,
      device: "web",
      device_token: currentToken,
      type: "email",
    };
    mutate(transformedData);
  };

  const onRememberMeChange = (e) => {
    const checked = e.target.checked;
    setRememberMe(checked);

    if (!checked) {
      localStorage.removeItem("rememberedCredentials");
    }
  };

  const processGoogleSignInResult = async (user) => {
    // Get device token
    let currentToken =
      deviceToken ||
      notificationService.getToken() ||
      localStorage.getItem("device_token");

    if (!currentToken) {
      currentToken = `web_final_fallback_${Date.now()}`;
      localStorage.setItem("device_token", currentToken);
    }

    // Store Google user data for potential signup redirect
    localStorage.setItem(
      "googleUserData",
      JSON.stringify({
        uid: user.uid,
        email: user.email,
        displayName: user.displayName,
        name: user.displayName,
      })
    );

    // Prepare data in the format expected by your API
    const googleSignInData = {
      type: "social",
      social_id: user.uid,
      social_platform: "google",
      email: user.email,
      device: "web",
      device_token: currentToken,
    };

    // Clean up loading state
    localStorage.removeItem("googleSignInLoading");
    setIsGoogleSignInLoading(false);

    // Use the same mutation as regular login
    mutate(googleSignInData);
  };

  const handleGoogleSignIn = async () => {
    setIsGoogleSignInLoading(true);

    try {
      // First try popup method
      const result = await signInWithPopup(auth, googleProvider);
      await processGoogleSignInResult(result.user);
    } catch (popupError) {
      console.log("Popup failed, trying redirect:", popupError.code);

      // If popup fails due to COOP or other issues, fall back to redirect
      if (
        popupError.code === "auth/popup-blocked" ||
        popupError.code === "auth/popup-closed-by-user" ||
        popupError.code === "auth/cancelled-popup-request" ||
        popupError.message.includes("Cross-Origin-Opener-Policy")
      ) {
        try {
          // Store loading state in localStorage to persist across redirect
          localStorage.setItem("googleSignInLoading", "true");
          await signInWithRedirect(auth, googleProvider);
        } catch (redirectError) {
          console.error("Both popup and redirect failed:", redirectError);
          setIsGoogleSignInLoading(false);
          localStorage.removeItem("googleSignInLoading");
          // You could show an error message to the user here
        }
      } else {
        console.error("Google Sign-In Error:", popupError);
        setIsGoogleSignInLoading(false);
        localStorage.removeItem("googleSignInLoading");
        // You could show an error message to the user here
      }
    }
  };

  return (
    <AuthLayout showSidebar={false}>
      <div className="text-center logo mb-3">
        <img src="/assets/img/suscribtion-img.png" alt="Auth Logo" />
      </div>
      <div className="col-12 text-center">
        <h1 className="font-46 color-black mb-1">SPHERE</h1>
        <h1 className="font-46 color-black mb-1">Real Estate Network</h1>
        <p>Log in to continue using your account</p>
      </div>
      <Form
        form={form}
        name="login"
        layout="vertical"
        onFinish={onFinish}
        initialValues={initialValues}
        autoComplete="off"
      >
        <BaseInput
          name="email"
          placeholder="Email"
          label="Email Address"
          rules={combineRules("email", validations.required, validations.email)}
        />
        <BaseInput
          type="password"
          name="password"
          placeholder="Password"
          label="Password"
          rules={combineRules(
            "password",
            validations.required,
            validations.password
          )}
        />
        <div className="d-flex align-items-center justify-content-between mt-0">
          <div className="mt-2 check-item">
            <Checkbox checked={rememberMe} onChange={onRememberMeChange}>
              Remember me
            </Checkbox>
          </div>
          <div>
            <Link
              to="/forgetpassword"
              className="mt-1 font-600 font-14 d-block color-blue "
            >
              Forgot Password?
            </Link>
          </div>
        </div>
        <div>
          <FlatButton
            title={isPending ? "Sign In..." : "Sign In"}
            className="mx-auto mt-4 signin-btn mt-5"
            htmlType="submit"
            loading={isPending}
            disabled={isPending}
          />
        </div>
        <div className="login-p">
          <p>Or login with</p>
        </div>
        <div
          className="socail-login"
          onClick={handleGoogleSignIn}
          style={{
            cursor: isGoogleSignInLoading ? "not-allowed" : "pointer",
            opacity: isGoogleSignInLoading ? 0.6 : 1,
            pointerEvents: isGoogleSignInLoading ? "none" : "auto",
          }}
        >
          <div>
            <img src="/assets/img/google.png" alt="" />
          </div>
          <div>
            <p>
              {isGoogleSignInLoading ? "Signing in..." : "Sign in with Google"}
            </p>
          </div>
        </div>
        <div>
          <p className="signup-text">
            Don't have an account?
            <Link to="/signup" className="color-blue font-600 font-16 ms-1">
              Sign up
            </Link>
          </p>
        </div>
      </Form>
    </AuthLayout>
  );
};

export default memo(Login);
