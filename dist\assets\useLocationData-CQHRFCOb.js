import{R as o}from"./index-Dklazue-.js";import{u as b}from"./useQuery-C3n1GVcJ.js";import{u as p}from"./useStartupData-QD8kuEYy.js";const h=(u="")=>{const[n,c]=o.useState(u),{data:a,isLoading:m}=p(),s=a==null?void 0:a.data,l=o.useMemo(()=>{if(!n||!(s!=null&&s.states))return"";const t=s.states.find(e=>e.name===n);return(t==null?void 0:t.isoCode)||""},[n,s==null?void 0:s.states]),{data:r,isLoading:f}=b("cities",{params:{stateCode:l},enabled:!!l,staleTime:0,gcTime:0,select:t=>Array.isArray(t==null?void 0:t.data)?t:[]}),i=r==null?void 0:r.data,C=o.useMemo(()=>s!=null&&s.states?s.states.filter(e=>e.countryCode==="US"&&!e.isoCode.includes("UM")).map(e=>({value:e.name,label:e.name,isoCode:e.isoCode})):[],[s==null?void 0:s.states]),S=o.useMemo(()=>!i||!Array.isArray(i)?[]:i.map(e=>{const d=e.name||e.city_name||e.label||e;return{value:d,label:d,id:e.id}}),[i]),g=o.useCallback((t,e=null)=>{c(t),e&&e.setFieldsValue({city:void 0})},[]),y=o.useCallback(t=>{c(t)},[]);return{selectedState:n,stateOptions:C,cityOptions:S,statesLoading:m,citiesLoading:f,handleStateChange:g,updateSelectedState:y}};export{h as u};
