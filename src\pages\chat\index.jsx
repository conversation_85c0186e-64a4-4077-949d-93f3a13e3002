import React, { useState, useEffect, useRef } from "react";
import { useLocation } from "react-router-dom";
import InnerLayout from "@/components/shared/layout/innerlayout";
import { SearchOutlined } from "@ant-design/icons";
import BaseInput from "@/components/shared/inputs";
import ChatList from "./chatlist";
import ChatDetail from "./chatdetail";

const Inbox = () => {
  const location = useLocation();
  const [connected, setConnected] = useState(false);
  const [rooms, setRooms] = useState([]);
  const [selectedRoom, setSelectedRoom] = useState(null);
  const [messages, setMessages] = useState([]);
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(false);
  const socketRef = useRef(null);
  const selectedRoomRef = useRef(null);

  // Keep selectedRoomRef in sync with selectedRoom
  useEffect(() => {
    selectedRoomRef.current = selectedRoom;
  }, [selectedRoom]);

  useEffect(() => {
    if (window.socket && !socketRef.current) {
      socketRef.current = window.socket;
      setupSocketListeners();
      setConnected(true);
      loadRooms();
    }
  }, []);

  // Handle direct message from agent listing
  useEffect(() => {
    if (
      location.state?.directMessage &&
      location.state?.targetUser &&
      rooms.length > 0
    ) {
      const targetUser = location.state.targetUser;
      const currentUserId = window.user?.id;

      // Check if there's already an existing room with this user
      const existingRoom = rooms.find((room) =>
        room.users?.some(
          (user) => user.id === targetUser.id && user.id !== currentUserId
        )
      );

      if (existingRoom) {
        // Use existing room and load chat history
        setSelectedRoom(existingRoom);
        setMessages([]);
        loadChatHistory(existingRoom.id);
      } else {
        // Create a temporary room for new direct messaging
        const directRoom = {
          id: `temp_${targetUser.id}`,
          users: [
            {
              id: window.user?.id,
              name: window.user?.name,
              image_url: window.user?.image_url,
            },
            targetUser,
          ],
          isDirectMessage: true,
          targetUserId: targetUser.id,
        };

        setSelectedRoom(directRoom);
        setMessages([]);
      }

      // Clear the location state to prevent re-triggering
      window.history.replaceState({}, document.title);
    }
  }, [location.state, rooms]);

  const setupSocketListeners = () => {
    const socket = socketRef.current;
    if (!socket) return;

    socket.on("connect", () => {
      setConnected(true);
      loadRooms();
    });

    socket.on("disconnect", () => {
      setConnected(false);
      setRooms([]);
      setSelectedRoom(null);
      setMessages([]);
    });

    socket.on("_receivedMessage", (message) => {
      console.log("Received message:", message);

      // Extract the actual message data from the response
      const messageData = message?.data;
      if (!messageData) return;

      // Don't add message if it's from the current user (already added when sending)
      const currentUserId = window.user?.id;
      const isMyMessage = messageData.user_id === currentUserId;

      // Check if this message belongs to the currently selected room using ref
      const currentSelectedRoom = selectedRoomRef.current;
      if (
        currentSelectedRoom &&
        messageData.chat_room_id === currentSelectedRoom.id &&
        !isMyMessage
      ) {
        // Add message to current chat only if it's not from current user
        setMessages((prev) => {
          const messageExists = prev.some((msg) => msg.id === messageData.id);
          if (!messageExists) {
            return [...prev, messageData];
          }
          return prev;
        });
      }

      // Always update room list regardless of selected room
      updateRoomWithNewMessage(messageData);
    });
  };

  const emit = (
    event,
    payload,
    successCallback = null,
    errorCallback = null
  ) => {
    if (!socketRef.current) return;

    socketRef.current.emit(event, payload, (response) => {
      if (response.status && typeof successCallback === "function") {
        successCallback(response.data);
      } else if (typeof errorCallback === "function") {
        errorCallback(response);
      }
    });
  };

  const loadRooms = () => {
    setLoading(true);
    emit(
      "_loadRoomsWithCb",
      { keyword: search, limit: 10 },
      (data) => {
        setRooms(data || []);
        setLoading(false);
      },
      (error) => {
        setLoading(false);
      }
    );
  };

  const selectRoom = (room) => {
    setSelectedRoom(room);
    setMessages([]);
    loadChatHistory(room.id);
  };

  const loadChatHistory = (roomId, lastRecordId = null) => {
    if (!roomId) return;

    emit(
      "_loadChatHistoryWithCb",
      {
        chat_room_id: roomId,
        last_record_id: lastRecordId,
      },
      (data) => {
        setMessages(Array.isArray(data) ? data.reverse() : []);
      }
    );
  };

  const sendMessage = (messageText) => {
    if (!messageText.trim() || !selectedRoom) return;

    // Handle direct message scenario
    if (selectedRoom.isDirectMessage) {
      // For direct messages, we need to create/find the room first
      emit(
        "_sendMessageWithCb",
        {
          other_user_id: selectedRoom.targetUserId,
          message: messageText,
          message_type: "text",
        },
        (data) => {
          console.log("Direct message sent:", data);
          const completeMessage = {
            ...data,
            user: {
              id: window.user?.id,
              name: window.user?.name,
              image_url: window.user?.image_url,
            },
          };

          // Add message immediately to messages list
          setMessages((prev) => [...prev, completeMessage]);

          // Update the selected room with the actual room ID from response
          if (data.chat_room_id) {
            setSelectedRoom((prev) => ({
              ...prev,
              id: data.chat_room_id,
              isDirectMessage: false, // Convert to regular room
            }));
          }

          // Update room list with new message
          updateRoomWithNewMessage(completeMessage);
          // Reload rooms to get the new conversation in the sidebar
          loadRooms();
        }
      );
      return;
    }

    // Regular room message sending
    const currentUserId = window.user?.id;
    const otherUserId = selectedRoom.users?.find(
      (user) => user.id !== currentUserId
    )?.id;

    emit(
      "_sendMessageWithCb",
      {
        chat_room_id: selectedRoom.id,
        other_user_id: otherUserId,
        message: messageText,
        message_type: "text",
      },
      (data) => {
        console.log("data", data);
        const completeMessage = {
          ...data,
          user: {
            id: window.user?.id,
            name: window.user?.name,
            image_url: window.user?.image_url,
          },
        };

        // Add message immediately to messages list
        setMessages((prev) => [...prev, completeMessage]);
        // Update room list with new message
        updateRoomWithNewMessage(completeMessage);
      }
    );
  };

  const updateRoomWithNewMessage = (message) => {
    setRooms((prevRooms) => {
      const updatedRooms = [...prevRooms];
      const roomIndex = updatedRooms.findIndex(
        (room) => room.id === message.chat_room_id
      );

      if (roomIndex !== -1) {
        // Update the room with new message
        const updatedRoom = {
          ...updatedRooms[roomIndex],
          last_message: {
            ...message,
            created_at: message.created_at || new Date().toISOString(),
          },
        };

        // Remove room from current position and add to top
        updatedRooms.splice(roomIndex, 1);
        updatedRooms.unshift(updatedRoom);

        // Update selected room if it's the same room
        if (selectedRoom?.id === message.chat_room_id) {
          setSelectedRoom(updatedRoom);
        }
      } else {
        // Room doesn't exist in current list (new conversation for receiver)
        // Reload rooms to get the new conversation
        loadRooms();
      }

      return updatedRooms;
    });
  };

  const handleSearchChange = (value) => {
    setSearch(value);
    // Debounce search
    setTimeout(() => {
      if (connected) {
        loadRooms();
      }
    }, 500);
  };

  // Get other user info for chat header
  const getOtherUser = () => {
    if (!selectedRoom?.users) return null;
    const currentUserId = window.user?.id;
    return (
      selectedRoom.users.find((user) => user.id !== currentUserId) ||
      selectedRoom.users[0]
    );
  };

  const otherUser = getOtherUser();

  return (
    <InnerLayout>
      <div className="container-fluid mt-5">
        <div className="row">
          <div className="col-12">
            <div className="chat-box">
              <div className="chat-listing">
                <div className="listing-header d-flex justify-content-between">
                  <div className="d-flex">
                    <p className="color-black font-18 me-2">Messages</p>
                    <p className="msg-numbers">{rooms.length}</p>
                  </div>
                </div>
                <div>
                  <BaseInput
                    placeholder="Search in Messenger"
                    icon={<SearchOutlined style={{ color: "gray" }} />}
                    className="chat-search"
                    value={search}
                    onChange={(e) => handleSearchChange(e.target.value)}
                  />
                </div>
                <ChatList
                  rooms={rooms}
                  selectedRoom={selectedRoom}
                  onSelectRoom={selectRoom}
                  loading={loading}
                  connected={connected}
                />
              </div>
              <ChatDetail
                messages={messages}
                selectedRoom={selectedRoom}
                otherUser={otherUser}
                onSendMessage={sendMessage}
                connected={connected}
              />
            </div>
          </div>
        </div>
      </div>
    </InnerLayout>
  );
};

export default Inbox;
