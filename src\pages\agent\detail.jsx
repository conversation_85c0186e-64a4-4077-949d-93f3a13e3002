import React, { useEffect, useState } from "react";
import InnerLayout from "@/components/shared/layout/innerlayout";
import IconButton from "@/components/shared/button/iconbutton";
import FlatButton from "@/components/shared/button/flatbutton";
import { useNavigate, useParams } from "react-router-dom";
import { useQuery } from "@/hooks/reactQuery";
import { Skeleton, Empty, Button } from "antd";
import AgentPosts from "./components/AgentPosts";
import AgentProperties from "./components/AgentProperties";

const AgentDetail = ({ isMyProfile = false }) => {
  const params = useParams();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("posts");

  const userId = isMyProfile ? window.user?.id : params.user_id;
  const type = params.type;

  const {
    data: userResponse,
    isLoading: isUserLoading,
    error: userError,
  } = useQuery("getUser", {
    slug: userId,
    enabled: !isMyProfile && !!userId,
    staleTime: 0,
    gcTime: 0,
  });

  const userData = isMyProfile ? window.user : userResponse?.data;

  const getProfessionalTypeDisplay = (user) => {
    if (!user) return "Real Estate Professional";

    if (user.professional_types && Array.isArray(user.professional_types)) {
      return user.professional_types
        .map(
          (item) =>
            window.helper?.getLabel("professional_type", item.name) || item.name
        )
        .join(", ");
    }

    return (
      window.helper?.getLabel("professional_type", user.professional_type) ||
      user.professional_type ||
      "Real Estate Professional"
    );
  };

  // Set active tab based on URL parameter
  useEffect(() => {
    if (type && ["posts", "properties"].includes(type)) {
      setActiveTab(type);
    } else {
      // Default to posts tab
      const defaultPath = isMyProfile
        ? "/profile/posts"
        : `/agent/detail/${userId}/posts`;
      navigate(defaultPath, { replace: true });
    }
  }, [type, navigate, isMyProfile, userId]);

  const handleTabClick = (tabType) => {
    setActiveTab(tabType);
    const newPath = isMyProfile
      ? `/profile/${tabType}`
      : `/agent/detail/${userId}/${tabType}`;
    navigate(newPath);
  };

  const handleMessageClick = (e) => {
    e.stopPropagation(); // Prevent card click navigation
    // Navigate to chat with agent info
    navigate("/inbox", {
      state: {
        directMessage: true,
        targetUser: userData,
      },
    });
  };
  // Only show loading for Agent Detail, not My Profile
  if (!isMyProfile && isUserLoading) {
    return (
      <InnerLayout>
        <div className="container-fluid">
          <div className="row">
            <div className="col-12">
              <div className="agent-box mt-5">
                <div className="agent-header w-100">
                  <Skeleton.Image
                    active
                    className="w-100 text-center align-items-center d-flex "
                    style={{ width: "100%", height: 200, display: "block" }}
                  />
                </div>
                <div className="agent-body d-flex align-items-center justify-content-between p-4">
                  <div className="d-flex align-items-center">
                    <Skeleton.Avatar size={80} />
                    <div className="ms-3">
                      <Skeleton.Input style={{ width: 150, height: 20 }} />
                      <div className="mt-2">
                        <Skeleton.Input style={{ width: 120, height: 16 }} />
                      </div>
                      <div className="mt-2">
                        <Skeleton.Input style={{ width: 100, height: 16 }} />
                      </div>
                    </div>
                  </div>
                  <div className="d-flex align-items-center">
                    <Skeleton.Button size="default" className="me-3" />
                    <Skeleton.Button size="default" className="me-3" />
                    <Skeleton.Button size="default" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </InnerLayout>
    );
  }

  // Only show error for Agent Detail API failure, not My Profile
  if ((!isMyProfile && userError) || !userData) {
    return (
      <InnerLayout>
        <div className="container-fluid">
          <div className="row">
            <div className="col-12 mt-4">
              <Empty
                description={
                  isMyProfile ? "Profile data not available" : "Agent not found"
                }
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              >
                <Button
                  type="primary"
                  onClick={() => navigate(isMyProfile ? "/home" : "/agent")}
                >
                  {isMyProfile ? "Go to Home" : "Back to Agents"}
                </Button>
              </Empty>
            </div>
          </div>
        </div>
      </InnerLayout>
    );
  }

  return (
    <InnerLayout>
      <div className="container-fluid">
        <div className="row">
          <div className="col-12">
            <div className="agent-box mt-5">
              <div className="agent-header">
                <img
                  src={userData.cover_image || "/assets/img/home-img.png"}
                  alt="Cover"
                  style={{ width: "100%", height: "200px", objectFit: "cover" }}
                />
              </div>
              <div className="agent-body d-flex align-items-center justify-content-between">
                <div className="d-flex align-items-center">
                  <div className="agent-profile">
                    <img
                      src={userData.image_url || "/assets/img/avatar-2.png"}
                      alt="Profile"
                      style={{
                        width: "80px",
                        height: "80px",
                        borderRadius: "50%",
                        objectFit: "cover",
                      }}
                    />
                  </div>
                  <div className="ms-3">
                    <div className="d-flex align-items-center">
                      <p className="me-2 font-600">
                        {userData.name || "Unknown User"}
                      </p>
                      {userData.is_verified && (
                        <img
                          src="/assets/img/badge.png"
                          alt="Verified"
                          className="img-fluid"
                        />
                      )}
                    </div>
                    <p className="color-light">
                      {getProfessionalTypeDisplay(userData)}
                    </p>
                    <div className="d-flex align-items-center">
                      <div>
                        <img src="/assets/img/location_on.png" alt="Location" />
                      </div>
                      <div>
                        <p>
                          {userData.city ||
                            userData.state ||
                            "Location not specified"}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Show different action buttons based on profile type */}
                <div className="d-flex align-items-center">
                  {isMyProfile ? (
                    <div className="me-3">
                      <FlatButton
                        title="Edit your Profile"
                        className="blue-btn"
                        onClick={() => navigate("/editprofile")}
                      />
                    </div>
                  ) : (
                    <>
                      <div className="me-3">
                        <IconButton
                          icon={
                            <img src="/assets/img/call-icon.png" alt="Call" />
                          }
                          title="Call"
                          className="gray-btn"
                          onClick={() =>
                            window.open(`tel:${userData.mobile_no}`)
                          }
                        />
                      </div>
                      <div className="me-3">
                        <IconButton
                          icon={
                            <img
                              src="/assets/img/message-icon.png"
                              alt="Message"
                            />
                          }
                          title="Message"
                          className="gray-btn"
                          onClick={handleMessageClick}
                        />
                      </div>
                      <div className="me-3">
                        <IconButton
                          icon={
                            <img src="/assets/img/mail-icon.png" alt="Mail" />
                          }
                          title="Mail"
                          className="blue-btn"
                          onClick={() =>
                            window.open(`mailto:${userData.email}`)
                          }
                        />
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="col-12 mt-5">
            <div className="text-center">
              <FlatButton
                title="Posts"
                className={
                  activeTab === "posts"
                    ? "active-tab-button"
                    : "post-tab-button"
                }
                onClick={() => handleTabClick("posts")}
              />
              <FlatButton
                title="Listings"
                className={
                  activeTab === "properties"
                    ? "active-tab-button"
                    : "post-tab-button"
                }
                onClick={() => handleTabClick("properties")}
              />
            </div>
          </div>

          {/* Tab Content */}
          <div className="col-12">
            {activeTab === "posts" && (
              <AgentPosts userId={userId} isMyProfile={isMyProfile} />
            )}
            {activeTab === "properties" && (
              <AgentProperties userId={userId} isMyProfile={isMyProfile} />
            )}
          </div>
        </div>
      </div>
    </InnerLayout>
  );
};

export default AgentDetail;
