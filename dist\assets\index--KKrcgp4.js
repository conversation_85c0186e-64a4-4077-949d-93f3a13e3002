import{u as c,j as e,_ as d}from"./index-Dklazue-.js";import{I as p}from"./index-vmMMvWhJ.js";import{I as r}from"./iconbutton-DMkwwwLX.js";import{S as g}from"./searchbar-BCS1MYCc.js";import{F as x}from"./Filter-D5O_6hp-.js";import{R as j}from"./ReusablePagination-B7_yMXG_.js";import{E as h}from"./EmptyState-YbiQZMha.js";import{u as f}from"./useSearchFilterPagination-BuDUyyEy.js";import{S as N}from"./Skeleton--lTrbnrp.js";import"./useMutation-BrUrPIzr.js";import"./button-DNhBCuue.js";import"./index-Cj6uPc4c.js";import"./index-BUt89ETK.js";import"./react-stripe.esm-ypQSOYN5.js";import"./useLocale-BNhrTARD.js";import"./index-CjGjc6T5.js";import"./index-CHbHgJvR.js";import"./useLocationData-CQHRFCOb.js";import"./useQuery-C3n1GVcJ.js";import"./useStartupData-QD8kuEYy.js";import"./languageUtils-BKYM3hOY.js";import"./index-CatU6E6a.js";import"./index-ChnPz6Q1.js";import"./fade-B36sOBLs.js";const u=({agent:s})=>{const a=c(),i=t=>{t.stopPropagation(),a("/inbox",{state:{directMessage:!0,targetUser:s}})};return e.jsxs("div",{className:"agent-item d-flex align-items-center justify-content-between",onClick:()=>a(`/agent/detail/${s.id}/post`),children:[e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("div",{className:"agent-profile",children:e.jsx("img",{src:s.image_url?s.image_url:"/assets/img/placeholder.jpg",alt:s.name})}),e.jsxs("div",{className:"ms-3",children:[e.jsx("div",{className:"d-flex",children:e.jsx("p",{className:"me-2 font-600",children:s.name})}),e.jsx("p",{className:"color-light",children:s.city})]})]}),e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("div",{className:"me-3",children:e.jsx(r,{icon:e.jsx("img",{src:"/assets/img/call-icon.png"}),title:"Call",className:"gray-btn",onClick:()=>window.open(`tel:${s.mobile_no}`)})}),e.jsx("div",{className:"me-3",children:e.jsx(r,{icon:e.jsx("img",{src:"/assets/img/message-icon.png"}),title:"Message",className:"gray-btn",onClick:i})}),e.jsx("div",{className:"me-3",children:e.jsx(r,{icon:e.jsx("img",{src:"/assets/img/mail-icon.png"}),title:"Mail",className:"blue-btn",onClick:()=>window.open(`mailto:${s.email}`)})})]})]})},G=()=>{const{data:s,isLoading:a,pagination:i,handlePageChange:t,handleFilterClick:o}=f("getUser",{pageSize:10}),n=[{name:"state",label:"State",type:"select",placeholder:"Select State"},{name:"city",label:"City",type:"select",placeholder:"Select City"},{name:"professional_type",label:"Professional Type",type:"radio"},{name:"languages",label:"Languages",type:"select",placeholder:"Select Languages"}];return e.jsx(p,{children:e.jsxs("div",{className:"container-fluid",children:[e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-12",children:e.jsx(g,{onFilterClick:o})})}),e.jsx(x,{fields:n}),e.jsx("div",{className:"row mt-5",children:a?Array.from({length:10}).map((l,m)=>e.jsx("div",{className:"col-12 col-md-6 col-lg-6",children:e.jsx(N,{active:!0,avatar:!0,paragraph:{rows:3}})},m)):d.isEmpty(s==null?void 0:s.data)?e.jsx(h,{title:"No agents found",description:"No agents available at the moment"}):s.data.map(l=>e.jsx("div",{className:"col-12 col-md-6 col-lg-6",children:e.jsx(u,{agent:l})},l.id))}),e.jsx(j,{pagination:i,handlePageChange:t,isLoading:a,itemName:"agents",pageSizeOptions:["10","20","50"],align:"end"})]})})};export{G as default};
