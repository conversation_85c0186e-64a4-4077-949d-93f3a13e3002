import React, { useState, useEffect, useMemo } from "react";
import { usePaginatedQuery, useQuery } from "@/hooks/reactQuery";
import { useScopedSearch } from "@/store/ScopedSearchContext";
import { usePostFilter } from "@/store/PostFilterContext";
import { debounce } from "lodash";

export const usePostSearchFilterPagination = (options = {}) => {
  const { searchKeyword } = useScopedSearch();
  const { filters, setIsFilterModalOpen } = usePostFilter();
  const [debouncedSearchKeyword, setDebouncedSearchKeyword] =
    useState(searchKeyword);

  const debouncedSetSearch = useMemo(
    () =>
      debounce((keyword) => {
        setDebouncedSearchKeyword(keyword);
      }, 500),
    []
  );

  useEffect(() => {
    debouncedSetSearch(searchKeyword);

    return () => debouncedSetSearch.cancel();
  }, [searchKeyword, debouncedSetSearch]);

  // Check if there are active filters or search
  const hasActiveFilters = useMemo(() => {
    return (
      Object.keys(filters).some(
        (key) =>
          filters[key] !== undefined &&
          filters[key] !== null &&
          filters[key] !== ""
      ) ||
      (debouncedSearchKeyword && debouncedSearchKeyword.trim())
    );
  }, [filters, debouncedSearchKeyword]);

  const apiParams = React.useMemo(() => {
    const params = { ...filters };

    if (options.defaultParams) {
      Object.assign(params, options.defaultParams);
    }

    if (debouncedSearchKeyword && debouncedSearchKeyword.trim()) {
      params.keyword = debouncedSearchKeyword.trim();
    }

    // Add is_anonymous parameter for postItem API
    params.is_anonymous = false;

    // Clean up empty values
    Object.keys(params).forEach((key) => {
      if (
        params[key] === undefined ||
        params[key] === null ||
        params[key] === ""
      ) {
        delete params[key];
      }
    });

    return params;
  }, [debouncedSearchKeyword, filters, options.defaultParams]);

  // Use different query strategies based on whether filters are active
  const paginatedResult = usePaginatedQuery("postItem", {
    params: apiParams,
    initialPage: 1,
    initialPageSize: options.pageSize || 12,
    staleTime: 0,
    gcTime: 0,
    keepPreviousData: true,
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    enabled: !hasActiveFilters, // Only use pagination when no filters are active
    ...options,
  });

  const filteredResult = useQuery("postItem", {
    params: apiParams,
    staleTime: 0,
    gcTime: 0,
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    enabled: hasActiveFilters, // Only use non-paginated query when filters are active
    ...options,
  });

  // Choose which result to use based on filter state
  const queryResult = hasActiveFilters ? filteredResult : paginatedResult;

  const handlePageChange = (newPage, newPageSize) => {
    // Only handle pagination when no filters are active
    if (
      !hasActiveFilters &&
      paginatedResult.setPageSize &&
      paginatedResult.setPage
    ) {
      if (newPageSize !== paginatedResult.pageSize) {
        paginatedResult.setPageSize(newPageSize);
        paginatedResult.setPage(1);
      } else {
        paginatedResult.setPage(newPage);
      }
    }
  };

  const handleFilterClick = () => {
    setIsFilterModalOpen(true);
  };

  return {
    ...queryResult,
    // Override pagination data when filters are active
    pagination: hasActiveFilters
      ? {
          current: 1,
          pageSize: queryResult.data?.data?.length || 0,
          total: queryResult.data?.data?.length || 0,
          totalPages: 1,
        }
      : paginatedResult.pagination,
    handlePageChange,
    handleFilterClick,
    searchKeyword,
    debouncedSearchKeyword,
    filters,
    apiParams,
    hasActiveFilters,
  };
};

export default usePostSearchFilterPagination;
