import React, { memo, useEffect, useState } from "react";
import AuthLayout from "@/components/shared/layout/authlayout";
import BaseInput from "@/components/shared/inputs/index";
import FlatButton from "@/components/shared/button/flatbutton";
import CustomUpload from "@/components/shared/upload/index";
import { Checkbox, Form } from "antd";
import { Link, useNavigate } from "react-router-dom";
import { combineRules, validations } from "@/config/rules";
import PhoneInputField from "@/components/shared/inputs/phonenumber";
import { useMutation, useQuery } from "@/hooks/reactQuery";
import useStartupData from "@/hooks/reactQuery/useStartupData";
import useLocationData from "@/hooks/useLocationData";
import { dateHelper } from "@/helpers/dateHelper";
import Helper from "@/helpers";
import {
  transformLanguagesForAPI,
  getLanguageOptions,
} from "@/utils/languageUtils";
import notificationService from "@/services/notification";

const SignUp = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const { data: startupData } = useStartupData();
  const [selectedProfessionTypes, setSelectedProfessionTypes] = React.useState(
    []
  );
  const [googleSignupData, setGoogleSignupData] = useState(null);
  const [deviceToken, setDeviceToken] = useState(null);

  // Use the location data hook
  const {
    selectedState,
    stateOptions,
    cityOptions,
    statesLoading,
    citiesLoading,
    handleStateChange,
  } = useLocationData();

  const { mutate, isPending } = useMutation("signup", {
    onSuccess: async (data) => {
      if (data) {
        await Helper.setStorageData("session", data.data);
        window.user = data.data;
        localStorage.removeItem("googleSignupData");
        localStorage.removeItem("googleUserData");
        navigate("/home");
      }
    },
  });

  // Initialize device token
  useEffect(() => {
    const initNotifications = async () => {
      try {
        const token = await notificationService.init();
        setDeviceToken(token);
      } catch (error) {
        const fallbackToken = `web_init_error_${Date.now()}`;
        setDeviceToken(fallbackToken);
        localStorage.setItem("device_token", fallbackToken);
      }
    };

    initNotifications();
  }, []);

  // Check for Google signup data and pre-fill form
  useEffect(() => {
    const googleData = localStorage.getItem("googleSignupData");
    if (googleData) {
      try {
        const parsedData = JSON.parse(googleData);
        setGoogleSignupData(parsedData);

        // Pre-fill form with Google data
        form.setFieldsValue({
          email: parsedData.email,
          name: parsedData.full_name,
        });
      } catch (error) {
        console.error("Error parsing Google signup data:", error);
        localStorage.removeItem("googleSignupData");
      }
    }
  }, [form]);

  // Get language options using the utility (now uses IDs as values)
  const languageOptions = React.useMemo(() => {
    return getLanguageOptions(startupData?.data?.languages);
  }, [startupData?.data?.languages]);

  const onFinish = (values) => {
    // Get device token
    let currentToken =
      deviceToken ||
      notificationService.getToken() ||
      localStorage.getItem("device_token");

    if (!currentToken) {
      currentToken = `web_final_fallback_${Date.now()}`;
      localStorage.setItem("device_token", currentToken);
    }

    // Check if this is a Google Sign-Up
    if (googleSignupData) {
      // For Google Sign-Up, use the social payload structure
      const googleSignupPayload = {
        type: "social",
        social_id: googleSignupData.social_id,
        social_platform: googleSignupData.social_platform,
        // Include all the form data for profile completion
        ...values,
        device: "web",
        device_token: currentToken,
      };

      // Build professional_types array for Google signup too
      const professionalTypes = [];
      if (values.professional_type && values.professional_type.length > 0) {
        values.professional_type.forEach((type) => {
          const professionData = { name: type };

          // Add license data based on profession type
          if (type === "broker" && values.broker_license_number) {
            professionData.license_number = values.broker_license_number;
            professionData.license_expiry_date =
              values.broker_license_expiry_date
                ? dateHelper.formatForAPI(values.broker_license_expiry_date)
                : "";
          } else if (type === "lender" && values.lender_license_number) {
            professionData.license_number = values.lender_license_number;
            professionData.license_expiry_date =
              values.lender_license_expiry_date
                ? dateHelper.formatForAPI(values.lender_license_expiry_date)
                : "";
          } else if (
            type === "commercial" &&
            values.commercial_license_number
          ) {
            professionData.license_number = values.commercial_license_number;
            professionData.license_expiry_date =
              values.commercial_license_expiry_date
                ? dateHelper.formatForAPI(values.commercial_license_expiry_date)
                : "";
          }

          professionalTypes.push(professionData);
        });
      }

      // Add professional_types to payload
      googleSignupPayload.professional_types =
        JSON.stringify(professionalTypes);

      // Remove individual license fields and professional_type from the payload
      delete googleSignupPayload.professional_type;
      delete googleSignupPayload.broker_license_number;
      delete googleSignupPayload.broker_license_expiry_date;
      delete googleSignupPayload.lender_license_number;
      delete googleSignupPayload.lender_license_expiry_date;
      delete googleSignupPayload.commercial_license_number;
      delete googleSignupPayload.commercial_license_expiry_date;

      // Remove languages from main payload
      delete googleSignupPayload.languages;

      // Transform languages using the utility and merge with main payload
      const languagePayload = transformLanguagesForAPI(values.languages);
      const finalPayload = {
        ...googleSignupPayload,
        ...languagePayload,
      };

      mutate(finalPayload);
      return;
    }

    // Regular email signup flow
    const professionalTypes = [];

    if (values.professional_type && values.professional_type.length > 0) {
      values.professional_type.forEach((type) => {
        const professionData = {
          name: type,
        };

        // Add license data based on profession type
        if (type === "broker" && values.broker_license_number) {
          professionData.license_number = values.broker_license_number;
          professionData.license_expiry_date = values.broker_license_expiry_date
            ? dateHelper.formatForAPI(values.broker_license_expiry_date)
            : "";
        } else if (type === "lender" && values.lender_license_number) {
          professionData.license_number = values.lender_license_number;
          professionData.license_expiry_date = values.lender_license_expiry_date
            ? dateHelper.formatForAPI(values.lender_license_expiry_date)
            : "";
        } else if (type === "commercial" && values.commercial_license_number) {
          professionData.license_number = values.commercial_license_number;
          professionData.license_expiry_date =
            values.commercial_license_expiry_date
              ? dateHelper.formatForAPI(values.commercial_license_expiry_date)
              : "";
        }

        professionalTypes.push(professionData);
      });
    }

    const transformedData = {
      ...values,
      // Stringify the professional_types array
      professional_types: JSON.stringify(professionalTypes),
      device: "web",
      device_token: currentToken,
      type: "email",
    };

    // Remove individual license fields and professional_type from the payload
    delete transformedData.professional_type;
    delete transformedData.broker_license_number;
    delete transformedData.broker_license_expiry_date;
    delete transformedData.lender_license_number;
    delete transformedData.lender_license_expiry_date;
    delete transformedData.commercial_license_number;
    delete transformedData.commercial_license_expiry_date;

    // Remove languages from main payload
    delete transformedData.languages;

    // Transform languages using the utility and merge with main payload
    const languagePayload = transformLanguagesForAPI(values.languages);
    const finalPayload = {
      ...transformedData,
      ...languagePayload,
    };

    mutate(finalPayload);
  };

  // Handle state change with form reference
  const onStateChange = (value) => {
    handleStateChange(value, form);
  };

  // Handle profession type change
  const onProfessionTypeChange = (checkedValues) => {
    setSelectedProfessionTypes(checkedValues);
  };

  const onChange = (e) => {
    console.log(`checked = ${e.target.checked}`);
  };

  return (
    <>
      <div className="text-center sign-up-logo">
        <img src="../assets/img/logo.png" alt="Auth Logo" />
      </div>
      <AuthLayout showSidebar={false} pageType="signup">
        <div className="col-12 text-center">
          <h1 className="font-36 color-black mb-4">Sign Up</h1>
          <h1 className="font-36 color-black">Create An Account</h1>
          <p>Register yourself here to continue</p>
        </div>
        <Form
          name="register"
          layout="vertical"
          form={form}
          onFinish={onFinish}
          scrollToFirstError={true}
          initialValues={{
            remember: true,
          }}
          autoComplete="off"
        >
          <div className="row">
            <div className="col-12 text-center mt-4">
              <CustomUpload
                static_img="/assets/img/avatar-img.svg"
                fileType="image"
                multiple={false}
                maxSize={5}
                uploadAction=""
                useFormItem={true}
                formItemProps={{
                  name: "image_url",
                  rules: combineRules("profile-image", validations.required),
                }}
                callback={(file) => {
                  form.setFieldsValue({
                    image_url: file.fileObj || file.originFileObj,
                  });
                }}
              />
            </div>
          </div>
          <div className="row mt-5 justify-content-center">
            <div className="col-12 col-md-6 col-lg-5 offset-lg-1">
              <div className="row">
                <div className="col-12">
                  <h4 className="">Personal Info</h4>
                </div>
                <div className="col-12 col-md-12 ">
                  <BaseInput
                    name="name"
                    placeholder="e.g John Doe"
                    label="Full Name"
                    rules={combineRules(
                      "full-name",
                      validations.required,
                      validations.minLength(2),
                      validations.maxLength(25)
                    )}
                  />
                </div>
                <div className="col-12 col-md-12 ">
                  <BaseInput
                    name="email"
                    placeholder="<EMAIL>"
                    label="Email"
                    rules={combineRules(
                      "email",
                      validations.required,
                      validations.email
                    )}
                  />
                </div>
                <div className="col-12 col-md-12 ">
                  <PhoneInputField
                    name="mobile_no"
                    label="Phone Number"
                    rules={combineRules(
                      "Phone Number",
                      validations.required,
                      validations.phone
                    )}
                  />
                </div>
                {/* Only show password fields for regular signup, not Google signup */}
                {!googleSignupData && (
                  <>
                    <div className="col-12 col-md-12 ">
                      <BaseInput
                        name="password"
                        placeholder="***"
                        label="Password"
                        type="password"
                        rules={combineRules(
                          "password",
                          validations.required,
                          validations.password
                        )}
                      />
                    </div>
                    <div className="col-12 col-md-12 ">
                      <BaseInput
                        name="confrimpassword"
                        placeholder="***"
                        label="Confirm Password"
                        type="password"
                        rules={[
                          validations.required("confirm-password"),
                          ({ getFieldValue }) => ({
                            validator: (_, value) => {
                              const password = getFieldValue("password");
                              if (!value || !password) {
                                return Promise.resolve();
                              }
                              if (value !== password) {
                                return Promise.reject(
                                  new Error("Confirm Passwords does not match")
                                );
                              }
                              return Promise.resolve();
                            },
                          }),
                        ]}
                        dependencies={["password"]}
                      />
                    </div>
                  </>
                )}
              </div>
            </div>
            <div className="col-12 col-md-6 col-lg-5 offset-lg-1">
              <div className="row">
                <div className="col-12">
                  <h4>Professional Info</h4>
                </div>
                <div className="col-12 col-md-12 ">
                  <div className="row">
                    <div className="col-6 col-md-6">
                      <BaseInput
                        name="state"
                        placeholder="State"
                        label="Location"
                        type="select"
                        rules={combineRules("state", validations.required)}
                        options={stateOptions}
                        loading={statesLoading}
                        handlechange={onStateChange}
                        showSearch={true}
                      />
                    </div>
                    <div className="col-6 col-md-6">
                      <BaseInput
                        name="city"
                        placeholder="City"
                        label=" "
                        type="select"
                        options={cityOptions}
                        rules={
                          cityOptions.length > 0
                            ? combineRules("city", validations.required)
                            : []
                        }
                        disabled={!selectedState}
                        loading={citiesLoading}
                        showSearch={true}
                      />
                    </div>
                  </div>
                </div>
                <div className="col-6 col-md-6">
                  <BaseInput
                    name="languages"
                    placeholder="Select Language"
                    label="Languages Spoken"
                    type="select"
                    rules={combineRules("language", validations.required)}
                    mode="multiple"
                    options={languageOptions}
                  />
                </div>
                <div className="col-12 col-md-6 ">
                  <div className="ant-form-item d-block">
                    <div className="mt-2">
                      <BaseInput
                        type="radio"
                        name="multi_state_license"
                        label="Multi State License"
                        options={[
                          { value: true, label: "Yes" },
                          { value: false, label: "No" },
                        ]}
                        rules={combineRules(
                          "multi-state-license",
                          validations.required
                        )}
                      />
                    </div>
                  </div>
                </div>
                <div className="col-12 ">
                  <BaseInput
                    type="checkboxgroup"
                    name="professional_type"
                    label="Profession Type"
                    options={[
                      {
                        value: "broker",
                        label: "Real Estate Broker",
                      },
                      {
                        value: "lender",
                        label: "Lender Mortgage Broker",
                      },
                      {
                        value: "commercial",
                        label: "Commercial Agent",
                      },
                    ]}
                    onChange={onProfessionTypeChange}
                    rules={combineRules(
                      "profession-type",
                      validations.required
                    )}
                  />
                </div>

                {/* Conditional License Sections */}
                {selectedProfessionTypes.includes("broker") && (
                  <div className="col-12">
                    <h5 className="mt-3 font-18">Real Estate Broker License</h5>
                    <div className="row">
                      <div className="col-12 col-md-6">
                        <BaseInput
                          name="broker_license_number"
                          placeholder="1ABC234"
                          label="License No."
                          rules={combineRules("license", validations.required)}
                        />
                      </div>
                      <div className="col-12 col-md-6">
                        <BaseInput
                          name="broker_license_expiry_date"
                          placeholder="Expiry Date"
                          label="Expiry Date"
                          type="datepicker"
                          rules={combineRules("expiry", validations.required)}
                        />
                      </div>
                    </div>
                  </div>
                )}

                {selectedProfessionTypes.includes("lender") && (
                  <div className="col-12">
                    <h5 className="font-18 mt-3">Lender/ mtg Broker License</h5>
                    <div className="row">
                      <div className="col-12 col-md-6">
                        <BaseInput
                          name="lender_license_number"
                          placeholder="1ABC234"
                          label="License No."
                          rules={combineRules("license", validations.required)}
                        />
                      </div>
                      <div className="col-12 col-md-6">
                        <BaseInput
                          name="lender_license_expiry_date"
                          placeholder="Expiry Date"
                          label="Expiry Date"
                          type="datepicker"
                          rules={combineRules("expiry", validations.required)}
                        />
                      </div>
                    </div>
                  </div>
                )}

                {selectedProfessionTypes.includes("commercial") && (
                  <div className="col-12">
                    <h5 className="font-18 mt-3">Commercial Agent License</h5>
                    <div className="row">
                      <div className="col-12 col-md-6">
                        <BaseInput
                          name="commercial_license_number"
                          placeholder="1ABC234"
                          label="License No."
                          rules={combineRules("license", validations.required)}
                        />
                      </div>
                      <div className="col-12 col-md-6">
                        <BaseInput
                          name="commercial_license_expiry_date"
                          placeholder="Expiry Date"
                          label="Expiry Date"
                          type="datepicker"
                          rules={combineRules("expiry", validations.required)}
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="row">
            <div className="col-12">
              <div className="text-center">
                <div className="mt-4 check-item">
                  <Form.Item
                    name={"check"}
                    valuePropName="checked"
                    rules={[
                      {
                        validator: (_, value) =>
                          value
                            ? Promise.resolve()
                            : Promise.reject(
                                new Error(
                                  "Please accept the Terms and Conditions"
                                )
                              ),
                      },
                    ]}
                  >
                    <Checkbox onChange={onChange}>
                      I agree to the
                      <Link to="#" className="color-blue  font-16 ms-1">
                        Terms and Conditions
                      </Link>
                    </Checkbox>
                  </Form.Item>
                </div>
              </div>
            </div>
          </div>

          <div className="text-center">
            <FlatButton
              title={isPending ? "Creating Account..." : "Register Now"}
              className="mx-auto mt-4 signin-btn signup-btn mt-5"
              htmlType="submit"
              loading={isPending}
              disabled={isPending}
            />
          </div>
          <div>
            <p className="signup-text">
              Don't have an account?
              <Link to="/login" className="color-blue  font-16 ms-1">
                Sign In
              </Link>
            </p>
          </div>
        </Form>
      </AuthLayout>
    </>
  );
};

export default SignUp;
