import React, { useState } from "react";
import { Modal, Form, Button, Space } from "antd";
import { FilterOutlined } from "@ant-design/icons";
import BaseInput from "./inputs";
import { usePostFilter } from "@/store/PostFilterContext";
import useLocationData from "@/hooks/useLocationData";
import useStartupData from "@/hooks/reactQuery/useStartupData";

const PostsFilter = ({ fields = [] }) => {
  const {
    filters,
    updateFilters,
    clearFilters,
    isFilterModalOpen,
    setIsFilterModalOpen,
  } = usePostFilter();
  const [form] = Form.useForm();
  const [showQuestionDropdown, setShowQuestionDropdown] = useState(false);

  const { data: startupResponse } = useStartupData();
  const startupData = startupResponse?.data;

  const {
    selectedState,
    stateOptions,
    cityOptions,
    statesLoading,
    citiesLoading,
    handleStateChange,
    updateSelectedState,
  } = useLocationData();

  // Generate question options from startup data
  const questionOptions = React.useMemo(() => {
    if (!startupData?.postQuestions) return [];
    return startupData.postQuestions.map((question) => ({
      value: question.id,
      label: question.name || question.title || question.question,
    }));
  }, [startupData?.postQuestions]);

  React.useEffect(() => {
    const formValues = {
      ...filters,
    };

    form.setFieldsValue(formValues);

    if (filters.state) {
      updateSelectedState(filters.state);
    } else {
      updateSelectedState("");
    }

    // Show question dropdown if most_asked is checked
    setShowQuestionDropdown(!!filters.most_asked);
  }, [filters, form, updateSelectedState]);

  // Watch for changes in most_asked checkbox
  const handleMostAskedChange = (checked) => {
    setShowQuestionDropdown(checked);
    if (!checked) {
      // Clear question_id when unchecking most_asked
      form.setFieldValue('question_id', undefined);
    }
  };

  const handleApplyFilters = () => {
    const values = form.getFieldsValue();

    const cleanedValues = Object.entries(values).reduce((acc, [key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        // Transform field names to match API requirements
        if (key === "professional_type") {
          acc.user_professional_type = value;
        } else if (key === "state") {
          acc.user_state = value;
        } else if (key === "city") {
          acc.user_city = value;
        } else if (key === "last_10_days" && value === true) {
          acc.last_days = 10;
        } else if (key === "least_commented" && value === true) {
          // Contract Q sorting: Least Commented On - only send sort_by
          acc.sort_by = "comments_count";
        } else if (key === "question_id" && value) {
          // Contract Q: Send question_id when Most Asked Questions is checked
          acc.question_id = value;
        } else {
          acc[key] = value;
        }
      }
      return acc;
    }, {});

    // Add page and limit to the payload
    cleanedValues.page = 1;
    cleanedValues.limit = 12;

    updateFilters(cleanedValues);
    setIsFilterModalOpen(false);
  };

  const handleResetFilters = () => {
    form.resetFields();
    clearFilters();
    updateSelectedState("");
    setShowQuestionDropdown(false);
    setIsFilterModalOpen(false);
  };

  const onStateChange = (value) => {
    handleStateChange(value, form);
  };

  const renderField = (field) => {
    const { name, label, type, placeholder, options, ...fieldProps } = field;

    if (name === "state") {
      return (
        <BaseInput
          key={name}
          name={name}
          label={label}
          type="select"
          placeholder={placeholder}
          options={stateOptions}
          loading={statesLoading}
          handlechange={onStateChange}
          showSearch={true}
          {...fieldProps}
        />
      );
    }

    if (name === "city") {
      return (
        <BaseInput
          key={name}
          name={name}
          label={label}
          type="select"
          placeholder={placeholder}
          options={cityOptions}
          disabled={!selectedState}
          loading={citiesLoading}
          showSearch={true}
          {...fieldProps}
        />
      );
    }

    if (name === "professional_type") {
      return (
        <BaseInput
          key={name}
          name={name}
          label={label}
          type="radio"
          options={[
            {
              value: "broker",
              label: "Real Estate Broker",
            },
            {
              value: "lender",
              label: "Lender/ mtg Broker",
            },
            {
              value: "commercial",
              label: "Commercial Agent",
            },
          ]}
          {...fieldProps}
        />
      );
    }

    if (name === "last_10_days") {
      return (
        <BaseInput
          key={name}
          name={name}
          type="checkbox"
          placeholder="Last 10 days posts only"
          {...fieldProps}
        />
      );
    }

    if (name === "least_commented") {
      return (
        <BaseInput
          key={name}
          name={name}
          type="checkbox"
          placeholder="Least Commented On"
          {...fieldProps}
        />
      );
    }

    if (name === "most_asked") {
      return (
        <div key={name}>
          <BaseInput
            name={name}
            type="checkbox"
            placeholder="Most asked questions"
            handlecheckbox={(e) => handleMostAskedChange(e.target.checked)}
            {...fieldProps}
          />
          {showQuestionDropdown && (
            <div className="mt-2">
              <BaseInput
                name="question_id"
                type="select"
                placeholder="Select Question"
                options={questionOptions}
                loading={!startupData}
                showSearch={true}
              />
            </div>
          )}
        </div>
      );
    }

    switch (type) {
      case "select":
        return (
          <BaseInput
            key={name}
            name={name}
            label={label}
            type="select"
            placeholder={placeholder}
            options={options}
            loading={fieldProps.loading}
            {...fieldProps}
          />
        );
      case "radio":
        return (
          <BaseInput
            key={name}
            name={name}
            label={label}
            type="radio"
            options={options}
            {...fieldProps}
          />
        );
      case "checkbox":
        return (
          <BaseInput
            key={name}
            name={name}
            label={label}
            type="checkbox"
            options={options}
            {...fieldProps}
          />
        );
      case "input":
      default:
        return (
          <BaseInput
            key={name}
            name={name}
            label={label}
            placeholder={placeholder}
            type={type === "input" ? "number" : undefined}
            {...fieldProps}
          />
        );
    }
  };

  return (
    <>
      <Button
        icon={<FilterOutlined />}
        onClick={() => setIsFilterModalOpen(true)}
        type="default"
        className="d-none"
      >
        Filter
      </Button>

      <Modal
        title="Filter Posts"
        open={isFilterModalOpen}
        onCancel={() => setIsFilterModalOpen(false)}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleApplyFilters}
          preserve={false}
        >
          <div className="row">
            {fields.map((field) => (
              <div key={field.name} className="col-12 col-md-6 mb-3">
                {renderField(field)}
              </div>
            ))}
          </div>

          <div className="d-flex justify-content-end gap-2 mt-4">
            <Space>
              <Button onClick={handleResetFilters}>Reset</Button>
              <Button type="primary" htmlType="submit">
                Apply Filters
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </>
  );
};

export default PostsFilter;
